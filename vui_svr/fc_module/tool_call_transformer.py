import json
import structlog
from typing import Dict, Any, Optional, List

logger = structlog.get_logger(__name__)


def transform_tool_call(
    tool_call: Dict[str, Any],
    task_id: Optional[str] = None,
    task_parameters_status: Optional[Dict[str, str]] = None
) -> Dict[str, Any]:
    """
    Transforms a function call result into the specified dictionary format.

    Args:
        tool_call: The tool call object from the LLM, containing function name and arguments.
        task_id: The existing task ID, if available.
        task_parameters_status: A dictionary mapping parameter names to their confirmation status.

    Returns:
        A dictionary with the transformed data.
    """
    function_name = tool_call.get('function', {}).get('name')
    try:
        function_args = json.loads(tool_call.get('function', {}).get('arguments', '{}'))
    except json.JSONDecodeError:
        logger.error(f"Failed to decode JSON arguments: {tool_call}")
        function_args = {}

    # Special handling for 'confirm_parameter'
    if function_name == "confirm_parameter":
        wf_instance_id = function_args.get("task_id", "")
        operation = function_args.get("task_function_name", "unknown_operation")
        
        param_list = []
        param_name = function_args.get("param_name")
        param_value = function_args.get("param_value")
        status = function_args.get("status", "TO_VALIDATE")

        if param_name and param_value is not None and (isinstance(param_value, str) and param_value.strip()):
            param_entry = {
                "name": param_name,
                "value": param_value,
                "status": status
            }
            param_list.append(param_entry)
            
        return {
            "wf_instance_id": wf_instance_id,
            "operation": operation,
            "param_list": param_list
        }

    # General handling for other functions
    # 1) Handle wf_instance_id
    # If a task_id from a previous context exists, use it. Otherwise, default to "".
    wf_instance_id = task_id or function_args.get("task_id", "")

    # 2) Handle operation
    # The operation is the function name. The user example mentioned 'exec_taxi_main_flow'
    # but the comment indicated it should be the function name.
    operation = function_name

    # 3) Handle param_list
    param_list = []
    if function_args:
        for param_name, value in function_args.items():
            # If the parameter value is None or an empty string, it's not included.
            if value is None or (isinstance(value, str) and not value.strip()):
                continue

            # Determine the status of the parameter
            if task_parameters_status and param_name in task_parameters_status:
                status = task_parameters_status[param_name]
            else:
                # Default status for new parameters
                status = "TO_VALIDATE"

            param_entry = {
                "name": param_name,
                "value": value,
                "status": status
            }
            param_list.append(param_entry)

    # Assemble the final dictionary
    transformed_data = {
        "wf_instance_id": wf_instance_id,
        "operation": operation,
        "param_list": param_list
    }

    return transformed_data

# --- Example Usage ---
if __name__ == "__main__":
    print("--- Running Transformation Examples ---")

    # --- Scenario 1: User's first-time request to create a task ---
    print("\n--- Scenario 1: New Task Creation ---")
    first_time_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "北京首都国际机场", "end_place": "北京环球影城"}'
        }
    }
    # No existing task_id, so it will be set to ""
    # No existing parameter statuses, so they will default to "to_validate"
    transformed_output_1 = transform_tool_call(first_time_tool_call)
    print("Input tool_call:")
    print(json.dumps(first_time_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_1, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="", all params "to_validate"

    print("\n" + "="*40 + "\n")

    # --- Scenario 2: User continues a conversation with an existing task_id ---
    print("--- Scenario 2: Continuing an Existing Task ---")
    existing_task_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "东方明珠", "end_place": "上海迪士尼度假区", "car_prefer": "舒适型"}'
        }
    }
    existing_task_id = "task_12345"
    # Simulate that 'start_place' was confirmed in a previous turn
    param_statuses = {
        "start_place": "confirmed",
        "end_place": "TO_VALIDATE",
        # 'car_prefer' is new, so it will default to "to_validate"
    }
    transformed_output_2 = transform_tool_call(existing_task_tool_call, existing_task_id, param_statuses)
    print("Input tool_call:")
    print(json.dumps(existing_task_tool_call, indent=2, ensure_ascii=False))
    print(f"\nInput context: task_id='{existing_task_id}', statuses={param_statuses}")
    print("\nTransformed output:")
    print(json.dumps(transformed_output_2, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="task_12345", start_place is "confirmed", others "to_validate"

    print("\n" + "="*40 + "\n")

    # --- Scenario 3: Function call with a missing required parameter (value is None or empty) ---
    print("--- Scenario 3: Parameter with None/Empty Value ---")
    missing_param_tool_call = {
        "function": {
            "name": "call_taxi_service",
            "arguments": '{"start_place": "广州塔", "end_place": "", "car_prefer": null}'
        }
    }
    # 'end_place' and 'car_prefer' should not be in the final param_list
    transformed_output_3 = transform_tool_call(missing_param_tool_call)
    print("Input tool_call:")
    print(json.dumps(missing_param_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_3, indent=2, ensure_ascii=False))
    # Expected: param_list only contains 'start_place'

    print("\n" + "="*40 + "\n")

    # --- Scenario 4: Handling 'confirm_parameter' function call ---
    print("--- Scenario 4: 'confirm_parameter' call ---")
    confirm_parameter_tool_call = {
        "function": {
            "name": "confirm_parameter",
            "arguments": '{"task_id": "task_12345", "task_function_name": "call_taxi_service", "param_name": "end_place", "param_value": "北京环球影城", "status": "CONFIRMED"}'
        }
    }
    transformed_output_4 = transform_tool_call(confirm_parameter_tool_call)
    print("Input tool_call:")
    print(json.dumps(confirm_parameter_tool_call, indent=2, ensure_ascii=False))
    print("\nTransformed output:")
    print(json.dumps(transformed_output_4, indent=2, ensure_ascii=False))
    # Expected: wf_instance_id="task_12345", operation="call_taxi_service", param_list contains 'end_place' with status "confirmed"
