import json
import structlog
import uuid
from datetime import datetime
from typing import List
from urllib.parse import urljoin

from redis import Redis

import requests

from vui_svr.agents.llm_api import llm_wrap
from vui_svr.fc_module.prompts import BASE_TOOL_CALL_PROMPT
from vui_svr.fc_module.amap_mcp_tools import AmapMCPTools
from vui_svr.fc_module.tool_call_transformer import transform_tool_call
from vui_svr.fc_module.result_formatter import format_execution_result
from vui_svr.settings import global_setting_dict

logger = structlog.get_logger(__name__)


class AgentFlow:
    def __init__(self, cache: Redis):
        self.cache = cache
        self.messages = []

        # self.param_model = "doubao-seed-1.6-flash-250615"
        self.param_model = "qwen-max"
        self.reply_model = "qwen-max"
        # self.reply_model = "doubao-seed-1.6-flash-250615"

    def set_session_hist(self, user_id: str, conversation_id: str, intent: str, messages: List):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"

        # msg_json = self.cache.get(user_session_key)
        # messages_prev = json.loads(msg_json) if msg_json else []

        # 1 hour to expire
        self.cache.set(user_session_key, json.dumps(messages), ex=60 * 60)

    def user_session_hist(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"
        msg_json = self.cache.get(user_session_key)
        return json.loads(msg_json) if msg_json else []

    def clear_session_hist(self, user_id: str, conversation_id: str, intent: str):
        user_session_key = f"flow:session:{user_id}:{conversation_id}:{intent}"
        self.cache.delete(user_session_key)

    @staticmethod
    def _city_info_from_coords(longitude: str, latitude: str) -> str:
        mcp_tool = AmapMCPTools()
        res = mcp_tool.reverse_geocode_poi(float(longitude), float(latitude))
        if not res["status"]:
            logger.error(f"reverse poi failed, err: {res}")
            return ""
        return res["data"]["formatted_address"]

    def _location_info_from_coords(self, coords: str):
        longitude, latitude = coords.split(",") if coords else ("", "")
        city_info = self._city_info_from_coords(longitude, latitude)
        return f"- 用户当前所在位置：{city_info} \n- 坐标：经度{longitude}, 纬度{latitude}"

    def _cached_loc_from_coords(self, coords: str) -> str:
        coord_key = f"coords:location:{coords}"
        if cached_loc := self.cache.get(coord_key):
            return cached_loc.decode()

        loc = self._location_info_from_coords(coords)
        self.cache.set(coord_key, loc, ex=60 * 60)
        return loc

    @staticmethod
    def _time_info():
        now = datetime.now()
        current_time = now.strftime("%H:%M")

        # 根据时间判断时段
        if 6 <= now.hour < 12:
            time_period = "上午"
        elif 12 <= now.hour < 18:
            time_period = "下午"
        elif 18 <= now.hour < 22:
            time_period = "晚上"
        else:
            time_period = "深夜"
        return f"{time_period} {current_time}"

    def _generate_base_tool_call_prompt(self, coords: str):
        env_prompt = f"""当前环境信息：
        现在是{self._time_info()}

        在处理用户请求时，要充分利用当前位置和时间信息，提供更智能的服务。
        """
        return BASE_TOOL_CALL_PROMPT + "\n\n" + env_prompt

    def call_fix_params_model(self, inp: str, tools: List, coords: str, history_str: str):
        system_prompt = self._generate_base_tool_call_prompt(coords)
        system_prompt += "\n\n以下是对话历史:\n" + history_str

        if not self.messages:
            self.messages = [{"role": "system", "content": system_prompt}]
        self.messages.append({"role": "user", "content": inp})
        logger.info(f"{self.messages}")
        return llm_wrap.get_responses(model=self.param_model, messages=self.messages, tools=tools)

    def get_tools(self, intent) -> List:
        if intent == "10007": # 打车
            from vui_svr.fc_module.fc_tools import car_tools
            return car_tools
        return []

    def tools_name(self, intent: str):
        return [t['function']['name'] for t in self.get_tools(intent)]

    def validata_and_exec_tools(self, transformed_tool_dict: dict):
        url = urljoin(global_setting_dict["phone_client"][0], "/agent-api/param/commit")
        res = requests.post(url=url, json=transformed_tool_dict)
        if res.status_code != 200:
            logger.error(f"call tools failed, code: {res.status_code} err: {res.text}")
        logger.debug(f"call tools result: {res.text}")
        return res.json()

    def process_tools(self, user_id: str, req_id: str, conversation_id: str, intent: str, coords: str, tools: List):
        # transform and re-organize -> send it to data
        # task_id = self.get_workflow_id(user_id, intent)
        # params = self.param_status_from_workflow_id(task_id)
        params = dict()

        # FIXME: multiple tools called?
        tool = tools[0]

        if tool["function"]["name"] not in self.tools_name(intent):
            return {
                'role': 'tool',
                'tool_call_id': tool["id"],
                'name': tool['function']['name'],
                'content': "未知的工具,不在能力范围内"
            }
        # elif :
        else:
            transformed = transform_tool_call(tool, None, params)
            transformed["app_id"] = "shouqi"
            transformed["request_id"] = req_id
            transformed["uid"] = user_id
            transformed["intent_id"] = intent
            transformed["conversation_id"] = conversation_id
            transformed["extra_data"] = {"coords": coords}

            # send_to_data
            logger.debug(f"transformed： {transformed}")
            result = self.validata_and_exec_tools(transformed)
            logger.debug(f"result: {result}")

        # transfer to LLM readable.
            return format_execution_result(tool["id"], result, operation=tool['function']['name'],transformed=transformed)

    def main_agent_flow(self, user_id: str, req_id: str, conversation_id: str, intent: str, inp: str, history_str: str, coords: str):
        # get tools by intent first
        tools = self.get_tools(intent)

        self.messages = self.user_session_hist(user_id, conversation_id, intent)

        # call LLM1 for tool choice and params fix && <>regenerate intent<>
        res = self.call_fix_params_model(inp=inp, tools=tools, history_str=history_str, coords=coords)
        if not res["tools"] and res["message"]:
            self.messages.append({"role": "assistant", "content": res["message"]})
            self.set_session_hist(user_id, conversation_id, intent, self.messages)
            return res["message"]
        if not res["tools"] and not res["message"]:
            raise Exception()

        logger.debug(f"tools: {res['tools']}")
        self.messages.append({"role": "assistant", "content": "", "tool_calls": res['tools']})

        # call API for param detail
        tool_result = self.process_tools(
            user_id=user_id, req_id=req_id, conversation_id=conversation_id,
            coords=coords, intent=intent, tools=res["tools"]
        )
        # for tool_result in tool_results:
        logger.debug(f"tool_result: {tool_result}")
        self.messages.append(tool_result)

        # LLM2 generate reply from API output
        llm2_res = llm_wrap.get_responses(model=self.reply_model, messages=self.messages)

        self.messages.append({"role": "assistant", "content": llm2_res["message"]})
        self.set_session_hist(user_id, conversation_id, intent, self.messages)
        return llm2_res["message"]


if __name__ == '__main__':
    _cache = Redis(host="127.0.0.1", port=6379, db=0)

    af = AgentFlow(_cache)
    af.clear_session_hist("123", "111111111",  "10007")

    inp = "我要打车"
    while True:
        r = af.main_agent_flow("123", str(uuid.uuid4()), "111111111", "10007", inp, "", "116.480881,39.989410")
        print(r)

        inp = input("输入对话：")
    # print(r)
