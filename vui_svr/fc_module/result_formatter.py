import json
import structlog
from typing import Dict, Any, List

logger = structlog.get_logger(__name__)

def format_execution_result(
    tool_call_id: str,
    execution_result: Dict[str, Any],
    operation: str = '',
    wf_instance_id: str = '',
    transformed: Dict[str, Any]={}

) -> Dict[str, Any]:
    """
    Transforms a detailed execution result into a human-readable summary
    formatted for an OpenAI tool call response.

    Args:
        tool_call_id: The ID of the original tool call.
        execution_result: The detailed result from the function execution.
        wf_instance_id: The ID of the workflow instance.

    Returns:
        A dictionary formatted as an OpenAI tool call result.
    """
    logger.debug(f"transformed: {transformed}")

    status = execution_result["status"]
    summary_parts = [f"任务 {operation} 的执行情况如下："]
    if not status:
        summary_parts.append(f'\n任务执行失败，错误信息：{execution_result}')
        return {
            "role": "tool",
            "tool_call_id": tool_call_id,
            "name": operation,
            "content": " ".join(summary_parts)
        }

    execution_data = execution_result.get("data", {})
    old_operation= operation
    operation = execution_data.get("operation", "unknown_operation")
    param_list = execution_data.get("param_list", [])
    wf_instance_id = wf_instance_id or execution_data.get("wf_instance_id", "")

    # 1. Aggregate parameters by status
    params_by_status = {
        "TO_INPUT": [],
        "TO_VALIDATE": [],
        "TO_CONFIRM": [],
    }
    extra_data = execution_data.get("extra_data", {})
    parameters_completeness = extra_data.get("parameters_completeness", False)

    if  parameters_completeness or old_operation=="confirm_important_task":
        summary_parts.append(f"任务 {operation} 已完成参数收集，可以跟用户确认完成任务信息。\n")
    else:
        summary_parts.append(f"任务 {operation} 还有需要用户确认或者填写的信息。\n")


    for param_dict in param_list:
        status = param_dict.get("status")
        if status in params_by_status:
            params_by_status[status].append(param_dict)

    # 2. Generate a human-readable text summary
    # Handle parameters needing to be filled
    if params_by_status["TO_INPUT"]:
        summary_parts.append("\n请填写以下缺失的信息（TO_INPUT）：")
        for p in params_by_status["TO_INPUT"]:
            reason = p.get('extra', {}).get('reason')
            if reason:
                if reason == "SEARCH_RESULT_LIST_EMPTY":
                    param_value = [item['value'] for item in transformed["param_list"] if item['name']==p['name']]
                    summary_parts.append(f"- {p['name']} 输入参数'{param_value}'不支持或者查不到这个参数值，需要用户重新填写有效或者支持的参数值")
                else:
                    summary_parts.append(f"- {p['name']}的缺失的信息可能因为工具调用错误，以下补充说明: {reason}")
                
            else:
                summary_parts.append(f"- {p['name']}: 需要用户提供具体内容。如果上文已经有相关参数，代表上文的相关参数无效，需要进行更改参数，或者使用其他工具")

    # Handle parameters needing validation
    if params_by_status["TO_VALIDATE"]:

        summary_parts.append("\n其中存在需要用户校验选择以下信息（TO_VALIDATE）：")
        for p in params_by_status["TO_VALIDATE"]:
            param_name = p['name']
            param_value = p.get('value', '未提供')
            summary_parts.append(f"- 参数 '{param_name}' (用户提供的值: '{param_value}') 需要验证。")
            
            suggestions = p.get('select_one')
            if suggestions:
                suggestion_str = ", ".join([f"'{s}'" for s in suggestions])
                summary_parts.append(f"\n存在多个相似的候选： {suggestion_str}，需要用户确认其中的哪一个？")
            else:
                # FIXME : This is a placeholder, you should replace it with more elastic and user-friendly suggestions.
                summary_parts.append(f"参数 '{param_name}的查询接口可能有点问题，可以稍后再确认\n")
            if p.get('extra'):
                extra_info = p.get('extra').get('reason')
                if extra_info:
                    summary_parts.append(f"  补充说明: {extra_info}")

    # Handle parameters needing confirmation
    if params_by_status["TO_CONFIRM"]:
        summary_parts.append("\n,请确认以下信息（TO_CONFIRM）：")
        for p in params_by_status["TO_CONFIRM"]:
            param_name = p['name']
            param_value = p.get('value', '未提供')
            summary_parts.append(f"- {param_name}: {param_value}")

    if wf_instance_id:
        summary_parts.append(f"\n当前任务id: {wf_instance_id}")

    if status and not param_list:
        #FIXME: 根据TaskName 存在不同的任务名称，需要根据不同的任务名称来确定回复内容
        summary_parts.append(f"\n当前任务已完成，请回复用户如下内容：好嘞，正在帮你叫车了，叫到了通知你")

    content_summary = " ".join(summary_parts)

    # 3. Format the final output
    formatted_response = {
        "role": "tool",
        "tool_call_id": tool_call_id,
        "name": operation,
        "content": content_summary
    }

    return formatted_response

# --- Example Usage ---
if __name__ == "__main__":
    print("--- Running Result Formatting Example ---")

    tool_call_id_example = "call_abc123"
    
    # The complex execution result provided in the prompt
    execution_result_example = {
        "extra_data": {},
        "conversation_id": "conv_xyz",
        "request_id": "req_456",
        "wf_instance_id": "wf_789",
        "uid": "user_1",
        "intent_id": "100009",
        "app_id": "shouqi",
        "operation": "call_taxi_service",
        "paramList": [
            {
                "to": {
                    "value": "北京环球影城",
                    "status": "TO_CONFIRM"
                }
            },
            {
                "from": {
                    "value": "机场",
                    "status": "TO_VALIDATE",
                    "select_one": [
                        "首都机场",
                        "大兴机场",
                        "南苑机场（已关闭）"
                    ],
                    "extra": {
                        "reason": "Ambiguous location",
                        "info": "检测到您提供的地点'机场'不明确。"
                    }
                }
            },
            {
                "price": {
                    "value": "100",
                    "status": "TO_CONFIRM"
                }
            },
            {
                "time": {
                    "status": "TO_INPUT"
                }
            }
        ]
    }

    # Add a 'TO_INPUT' case to the example
    execution_result_example["paramList"].append({"vehicle_type": {"status": "TO_INPUT"}})


    formatted_output = format_execution_result(tool_call_id_example, execution_result_example)

    print("\nInput Execution Result:")
    print(json.dumps(execution_result_example, indent=2, ensure_ascii=False))
    
    print(f"\nInput tool_call_id: {tool_call_id_example}")

    print("\n--- Transformed OpenAI Tool Call Result ---")
    print(json.dumps(formatted_output, indent=2, ensure_ascii=False))

    print("\n--- Just the 'content' field for clarity ---")
    print(formatted_output["content"])
