car_tools = [
    # 高德地图工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_geocode_address",
    #         "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "address": {
    #                     "type": "string",
    #                     "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
    #                 }
    #             },
    #             "required": ["address"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_search_poi",
    #         "description": "搜索POI（兴趣点），如餐厅、酒店、景点等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "keyword": {
    #                     "type": "string",
    #                     "description": "搜索关键词，如'星巴克'、'酒店'、'加油站'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于限定搜索范围"
    #                 },
    #                 "types": {
    #                     "type": "string",
    #                     "description": "POI类型，可选，如'餐饮服务'、'住宿服务'"
    #                 }
    #             },
    #             "required": ["keyword"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_reverse_geocode_poi",
    #         "description": "根据经纬度坐标查找附近的POI（兴趣点）。输入经纬度，输出附近的商店、餐厅、景点等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "longitude": {
    #                     "type": "number",
    #                     "description": "经度，如116.397428"
    #                 },
    #                 "latitude": {
    #                     "type": "number",
    #                     "description": "纬度，如39.90923"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "搜索半径（米），默认1000米，最大3000米"
    #                 }
    #             },
    #             "required": ["longitude", "latitude"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_recommend_similar_poi",
    #         "description": "根据POI名称推荐附近相似的POI。例如输入'北京上地地铁站附近有星巴克么？'，会推荐上地星巴克附近的咖啡店，而不是北京所有的星巴克。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "poi_name": {
    #                     "type": "string",
    #                     "description": "POI名称，可以是模糊名称，如'北京上地星巴克'、'杭州西湖银泰'，环境范围越具体越好，北京上地地铁站星巴克"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "推荐范围半径（米），默认2000米"
    #                 }
    #             },
    #             "required": ["poi_name"]
    #         }
    #     }
    # },
    # 导航距离和时间计算工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_calculate_driving_route",
    #         "description": "计算两个经纬度坐标之间的驾车导航距离和预估时间。返回距离、时间、过路费、红绿灯数量等信息。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_lng": {
    #                     "type": "number",
    #                     "description": "起点经度，如116.397428"
    #                 },
    #                 "origin_lat": {
    #                     "type": "number",
    #                     "description": "起点纬度，如39.90923"
    #                 },
    #                 "dest_lng": {
    #                     "type": "number",
    #                     "description": "终点经度，如116.465302"
    #                 },
    #                 "dest_lat": {
    #                     "type": "number",
    #                     "description": "终点纬度，如40.004717"
    #                 },
    #                 "strategy": {
    #                     "type": "integer",
    #                    "description": "路径规划策略，默认10（躲避拥堵，路程较短）。可选值：10-20为多策略，0-9为单策略"
    #                 }
    #             },
    #             "required": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"]
    #         }
    #     }
    # },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_calculate_poi_to_poi_route",
    #         "description": "计算两个POI名称之间的驾车导航距离和预估时间。先将POI名称转换为坐标，再计算路径。适用于'从北京天安门到北京西站'这类查询。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_poi": {
    #                     "type": "string",
    #                     "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
    #                 },
    #                 "dest_poi": {
    #                     "type": "string",
    #                     "description": "终点POI名称，如'北京西站'、'杭州东站'"
    #                 },
    #                 "origin_city": {
    #                     "type": "string",
    #                     "description": "起点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "dest_city": {
    #                     "type": "string",
    #                     "description": "终点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "strategy": {
    #                     "type": "integer",
    #                     "description": "路径规划策略，默认10（躲避拥堵，路程较短）"
    #                 }
    #             },
    #             "required": ["origin_poi", "dest_poi"]
    #         }
    #     }
    # },
    # 打车服务工具
    {
        "type": "function",
        "function": {
            "name": "call_taxi_service",
            "description": "调用打车服务，用户存在打车的意图（包含要去哪里），为用户安排车辆从起点到终点。比如用户询问打车、我要打车或者隐含打车的意图, 如果不存在出发地,可以使用当前所在位置进行补全；边缘情况：目前仅支持北京的同城打车，不支持跨城市打车。",
            "parameters": {
                "type": "object",
                "properties": {
                    "start_place": {
                        "type": "string",
                        "description": "出发地点名称，必须提供, 如果不存在 出发地 则填 无"
                    },
                    "end_place": {
                        "type": "string",
                        "description": "目的地名称，没有时填 无"
                    },
                    "car_prefer": {
                        "type": "string",
                        "description": "车辆偏好，可选，如'经济型'、'舒适型'、'豪华型'等，默认选择经济型。"
                    }
                },
                "required": ["start_place", "end_place"]
            }
        }
    },
    # 上车点推荐工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_search_taxi_spots",
    #         "description": "搜索上车点推荐。根据指定位置搜索附近适合打车的上车点，如地铁站、酒店、商场等交通便利的地点。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "location": {
    #                     "type": "string",
    #                     "description": "位置名称或地址，如'北京大学'、'方正大厦'、'海淀医院'"
    #                 },
    #                 "city": {
    #                     "type": "string",
    #                     "description": "城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "radius": {
    #                     "type": "integer",
    #                     "description": "搜索半径（米），默认1000米，建议500-2000米"
    #                 }
    #             },
    #             "required": ["location"]
    #         }
    #     }
    # },
    # 打车价格估算工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "mcp_estimate_taxi_price",
    #         "description": "估算打车价格。基于起点终点距离和时间，估算不同车型的打车费用，包括起步价、里程费、时长费等。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "origin_poi": {
    #                     "type": "string",
    #                     "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
    #                 },
    #                 "dest_poi": {
    #                     "type": "string",
    #                     "description": "终点POI名称，如'北京西站'、'杭州东站'"
    #                 },
    #                 "origin_city": {
    #                     "type": "string",
    #                     "description": "起点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "dest_city": {
    #                     "type": "string",
    #                     "description": "终点城市名称，可选，用于提高搜索精度"
    #                 },
    #                 "car_type": {
    #                     "type": "string",
    #                     "description": "车型类型，可选值：'经济型'、'舒适型'、'豪华型'，默认'经济型'"
    #                 }
    #             },
    #             "required": ["origin_poi", "dest_poi"]
    #         }
    #     }
    # },
    # 参数确认工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "confirm_parameter",
    #         "description": "确认低容错率函数的参数。用于用户确认已验证的参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "function_name": {
    #                     "type": "string",
    #                     "description": "函数名称，如'call_taxi_service'"
    #                 },
    #                 "parameter_name": {
    #                     "type": "string",
    #                     "description": "参数名称，如'start_place'、'end_place'"
    #                 },
    #                 "parameter_value": {
    #                     "type": "string",
    #                     "description": "参数值，用于确认"
    #                 }
    #             },
    #             "required": ["function_name", "parameter_name", "parameter_value"]
    #         }
    #     }
    # },
    # # 参数取消工具
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "cancel_parameter",
    #         "description": "取消/重置低容错率函数的参数。用于用户取消或重新设置参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "function_name": {
    #                     "type": "string",
    #                     "description": "函数名称，如'call_taxi_service'"
    #                 },
    #                 "parameter_name": {
    #                     "type": "string",
    #                     "description": "参数名称，如'start_place'、'end_place'"
    #                 }
    #             },
    #             "required": ["function_name", "parameter_name"]
    #         }
    #     }
    # },
    {
        "type": "function",
        "function": {
            "name": "confirm_important_task",
            "description": "用于用户确认可以执行低容错率函数的任务，或者当系统收集完信息用户确认执行某事，比如需要花钱或者较长时间的任务；",
            "parameters": {
                "type": "object",
                "properties": {
                    "function_name": {
                        "type": "string",
                        "description": "函数名称，如'call_taxi_service'"
                    },
                    "task_id": {
                        "type": "string",
                        "description": "任务ID"
                    },
                },
                "required": ["function_name","task_id"]
            }
        }
    },
    # 任务管理工具
    {
        "type": "function",
        "function": {
            "name": "confirm_parameter",
            "description": "确认或者修改、更新任务参数，将参数状态置为'已确认',用于用户确认已验证的参数值。",
            "parameters": {
                "type": "object",
                "properties": {
                    "task_id": {
                        "type": "string",
                        "description": "任务ID"
                    },
                    "task_function_name": {
                        "type": "string",
                        "description": "任务对应的函数名称，如'call_taxi_service'"
                    },
                    "param_name": {
                        "type": "string",
                        "description": "参数名称，如'start_place'、'end_place'"
                    },
                    "param_value": {
                        "type": "string",
                        "description": "参数值，对应start_place的出发地地址名称或者对应的目的地地址名称"
                    },
                    "status": {
                        "type": "string",
                        "description": "填TO_VALIDATE"
                    },
                },
                "required": ["task_id","task_function_name","param_name","param_value","status"]
            }
        }
    },
    # {
    #     "type": "function",
    #     "function": {
    #         "name": "cancel_parameter",
    #         "description": "取消任务参数，将参数状态置为'未填写',取消/重置低容错率函数的参数。用于用户取消或重新设置参数值。",
    #         "parameters": {
    #             "type": "object",
    #             "properties": {
    #                 "task_id": {
    #                     "type": "string",
    #                     "description": "任务ID"
    #                 },
    #                 "param_name": {
    #                     "type": "string",
    #                     "description": "参数名称"
    #                 },
    #                 "param_value": {
    #                     "type": "string",
    #                     "description": "参数值，具体出发地地址名称,无"
    #                 }
    #             },
    #             "required": ["task_id", "param_name"]
    #         }
    #     }
    # }
]
