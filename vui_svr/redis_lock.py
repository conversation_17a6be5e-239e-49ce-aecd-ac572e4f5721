import redis
import time
import uuid
import logging

class RedisLock:
    def __init__(self, redis_client, lock_key, timeout=3, retry_interval=0.1):
        self.redis_client = redis_client
        self.lock_key = f'{lock_key}_lock'
        self.timeout = timeout  # 锁的过期时间
        self.retry_interval = retry_interval  # 重试时间间隔
        self.lock_value = str(uuid.uuid4())  # 用一个唯一的值来标识锁
        self.lock_acquired = False  # 锁是否已经获取成功

    def acquire(self):
        #val = self.redis_client.get(self.lock_key)
        #logging.debug(f'redis key: {self.lock_key}, val: {val}, new val: {self.lock_value}')
        #self.redis_client.delete(self.lock_key)
        start_time = time.time()
        while True:
            # 使用SETNX命令（如果锁不存在，则设置锁）
            val = self.redis_client.get(self.lock_key)
            if val: val = val.decode('utf-8')
            ret = self.redis_client.setnx(self.lock_key, self.lock_value)
            #print(f'get redis lock, key:{self.lock_key}, old_val: {val}, curr_val: {self.lock_value}, {ret}')
            if ret:
                #logging.debug(f'set lock, key: {self.lock_key}, timeout: {self.timeout}, value: {self.lock_value}')
                self.redis_client.expire(self.lock_key, self.timeout)
                self.lock_acquired = True
                return True
            else:
                ttl = self.redis_client.ttl(self.lock_key)
                #logging.debug(f'set lock, key: {self.lock_key}, ttl: {ttl}, timeout: {self.timeout}, value: {self.lock_value}')
                if ttl == 0:
                    self.redis_client.expire(self.lock_key, self.timeout)
                    self.lock_acquired = True
                    return True

            if time.time() - start_time > self.timeout:
                return False  # 超过超时时间，获取锁失败
            time.sleep(self.retry_interval)

    def release(self):
        #logging.debug(f'release lock, key: {self.lock_key}, value: {self.lock_value}, acquired: {self.lock_acquired}')
        if self.lock_acquired:
            val = self.redis_client.get(self.lock_key)
            if val: val = val.decode('utf-8')
            #logging.debug(f'release lock, key: {self.lock_key}, old_val: {val}, curr_val: {self.lock_value}')
            if not self.redis_client.exists(self.lock_key) or val == self.lock_value:
                #logging.debug(f'delete redis key, {self.lock_key}, value: {self.lock_value}')
                self.redis_client.delete(self.lock_key)
                self.lock_acquired = False
                return True
        return False

if __name__ == "__main__":
    # 初始化Redis客户端
    redis_client = redis.StrictRedis(host='localhost', port=6379, db=0)
    lock = RedisLock(redis_client, "my_lock_key", timeout=10, retry_interval=0.1)

    if lock.acquire():
        try:
            print("Lock acquired, performing critical operation.")
            time.sleep(5)  # 模拟操作
        finally:
            lock.release()
            print("Lock released.")
    else:
        print("Could not acquire the lock.")
