import threading
import time
import structlog
import random
import json

logger = structlog.get_logger(__name__)

class UserTimerScheduler:
    def __init__(self,  client_req_agent):
        self.timers = {}  # 用户ID到定时器信息的映射
        self.lock = threading.Lock()  # 保证线程安全
        self.client_req_agent = client_req_agent
        self.req_time_out = 2

    def _safe_acquire_lock(self):
        return self.lock.acquire(timeout = self.req_time_out)

    def set_task(self, user_id, params, time_interval = 5, task_func = None):
        if not task_func:
            task_func = self._run_task

        ret_code = 0
        if not self._safe_acquire_lock():
            logger.error(f'timer get lock error, return')
            ret_code = -1
        else:
            try:
                self.stop(user_id, False)
                if user_id not in self.timers:
                    self.timers[user_id] = {
                        'active': False,
                        'count': 0,
                        'time_interval': time_interval,
                        'task_func': task_func,
                        'params': params,
                        'timer': None
                    }
                else:
                    entry = self.timers[user_id]
                    entry['task_func'] = task_func
                    entry['params'] = params
            finally:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return ret_code

    def restart(self, user_id):
        ret_code = 0
        # logger.debug(f'timer enter restart')
        if not self._safe_acquire_lock():
            logger.error(f'timer get lock error, return')
            ret_code = -1
        else:
            try:
                logger.debug(f'timer restart, user_id:{user_id}')
                if user_id not in self.timers:
                    logger.debug(f"Task not set for user_id: {user_id}")
                    return ret_code

                entry = self.timers[user_id]
                if entry['active']:
                    logger.debug(f'timer already start, clear')
                    if entry['timer']:
                        entry['timer'].cancel()
                        entry['timer'] = None

                # logger.debug(f'timer restart, user_id:{user_id}, after stop')
                entry['active'] = True
                entry['count'] = 0
                entry['timer'] = threading.Timer(entry["time_interval"], self._execute_task, (user_id,))
                entry['timer'].start()

                # logger.debug(f'timer restart, user_id:{user_id}, after start timer')
            finally:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return ret_code

    def start(self, user_id):
        ret_code = 0
        if not self._safe_acquire_lock():
            logger.error(f'timer get lock error, return')
            ret_code = -1
        else:
            try:
                logger.debug(f'timer start, user_id:{user_id}')
                if user_id not in self.timers:
                    logger.debug(f"Task not set for user_id: {user_id}")
                    return ret_code
                
                entry = self.timers[user_id]
                if entry['active']:
                    logger.debug(f'timer already start, nothing todo')
                    return ret_code
                    # self.pause(user_id, False)
           
                # logger.debug(f'timer start, user_id:{user_id}, after stop')
                entry['active'] = True
                entry['count'] = 0
                entry['timer'] = threading.Timer(entry["time_interval"], self._execute_task, (user_id,))
                entry['timer'].start()

                # logger.debug(f'timer start, user_id:{user_id}, after start timer')
            finally:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return ret_code

    def _run_task(self, current_count, params):
        logger.debug(f'timer task, current_count:{current_count}, params:{params}')

        user_id = params.get("uid", "")
        intent_id = params.get("intent_id", "")
        coords = params.get("coords", "")
        token = params.get("token", "")
        mic_status = params.get("mic_status", "off")
        talk_status = params.get("talk_status", "on")
        msg_list = params.get("tip_list", [])
        msg = msg_list[random.randint(0, len(msg_list) - 1)]

        extra_header = {"Authorization": token}
        pro_ver_id = params.get("pro_ver_id", "0")

        if msg:
            context = {
                "uid": user_id,
                "intent_id": intent_id,
                "coords": coords,
                "token": token,
                "pro_ver_id": pro_ver_id,
                # this is a padding message.
                "msg_type": "padding",
                "mic_status": mic_status,
                "talk_status": talk_status,
            }
            param = {"message": msg}

            context = json.dumps(context, ensure_ascii=False)
            param = json.dumps(param, ensure_ascii=False)
            content = {"context": context, "scriptParams": param}

            self.client_req_agent.post_request("/chat/rec_langflow", content, extra_header, "0", False)

    def _execute_task(self, user_id):
        ret_code = 0
        if not self._safe_acquire_lock():
            logger.error(f'timer get lock error, return')
            ret_code = -1
        else:
            try:
                if user_id not in self.timers:
                    return
                
                entry = self.timers[user_id]
                if not entry['active']:
                    return
                
                entry['count'] += 1
                current_count = entry['count']
                task_func = entry['task_func']
                params = entry['params']

                if task_func:
                    task_func(current_count, params)

            except Exception as e:
                logger.error(f"Error executing task for {user_id}: {str(e)}")
            finally:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return ret_code

    def pause(self, user_id, need_lock = True):
        if need_lock:
            if not self._safe_acquire_lock():
                logger.error(f'timer get lock error, return')
                return -1

        try:
            logger.debug(f'pause timer, uid:{user_id}')
            if user_id in self.timers:
                entry = self.timers[user_id]
                entry['active'] = False
                if entry['timer']:
                    entry['timer'].cancel()
                    entry['timer'] = None
        finally:
            if need_lock:
                self.lock.release()
                logger.debug(f'timer release lock success')
        return 0

    def stop(self, user_id, need_lock = True):
        if need_lock:
            if not self._safe_acquire_lock():
                logger.error(f'timer get lock error, return')
                return -1

        try:
            logger.debug(f'stop timer, uid:{user_id}')
            if user_id in self.timers:
                entry = self.timers[user_id]
                if entry['timer']:
                    entry['timer'].cancel()
                    entry['timer'] = None
    
                del self.timers[user_id]
        finally:
            if need_lock:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return 0

    def stop_all(self):
        ret_code = 0
        if not self._safe_acquire_lock():
            logger.error(f'timer get lock error, return')
            ret_code = -1
        else:
            try:
                for user_id in list(self.timers.keys()):
                    self.stop(user_id, False)
            finally:
                self.lock.release()
                logger.debug(f'timer release lock success')

        return ret_code
