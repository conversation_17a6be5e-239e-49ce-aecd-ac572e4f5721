import logging

def rule_post_luckin(scene_id, last_scene_id, llm_task_ouput):
    #state_trans_dict = {"1": {}}

    intent_vars = llm_task_ouput.get("out_vars", None) 

    # 规范化抽取出的变量
    if intent_vars and type(intent_vars) == dict:
        if "add_dish" in intent_vars and intent_vars["add_dish"]:
            logging.debug(f'old add_dish: {intent_vars["add_dish"]}')
            intent_vars["add_dish"] = [dish_item for dish_item in intent_vars["add_dish"] if dish_item.get("dish_name", "") not in ["咖啡",""]]
            logging.debug(f'new add_dish: {intent_vars["add_dish"]}')

        if intent_vars.get("shop_name", "") == "瑞幸":
            intent_vars["shop_name"] = ""
            intent_vars["shop_alias"] = ""
  
    llm_task_ouput["out_vars"] = intent_vars
    # 验证场景跳转关系
    #if last_scene_id in state_trans_dict:
    #    if scene_id not in state_trans_dict[last_scene_id]:
    #        scene_id = -1
                    
    return scene_id, llm_task_ouput
