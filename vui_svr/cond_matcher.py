from .text_matcher import text_matcher
from typing import Any, Dict

import json
import re
import requests
import structlog

logger = structlog.get_logger(__name__)

class cond_matcher(text_matcher):
    def __init__(self):
        pass

    def init_matcher(self, intent_info, file_path):
        self.index_dict = {}
        for intent_key in intent_info:
            file_item = intent_info[intent_key]["task_bot"].get("rule", "")
            if file_item:
                file_item = f'{file_path}/{file_item}'
                logger.debug(f'Loading cond file, intent_id:{intent_key}, file:{file_item}')
                with open(file_item, 'r', encoding='utf-8') as file:
                    rule_data = json.load(file)
                    self.index_dict[intent_key] = rule_data

        logger.debug(f'cond_matcher, cond index built')
        return 0

    def resolve_operand(self, req_id, user_id, global_conversation_id, node: Any, variables: Dict[str, Any]) -> Any:
        """解析操作数：支持变量引用、字面量及长度计算"""
        if isinstance(node, dict):
            if "var" in node:
                return variables.get(node["var"], None)
            elif "len" in node:
                target = self.resolve_operand(req_id, user_id, global_conversation_id, node["len"], variables)
                if isinstance(target, (list, dict, str)):
                    return len(target)
                logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] Cannot get length of {type(target).__name__}")
            else:
                logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] Invalid operand node: {node}")
        return node

    def evaluate_condition(self, req_id, user_id, global_conversation_id, condition: Dict, variables: Dict[str, Any]) -> bool:
        # 布尔值直接判断
        if "var" in condition:
            value = variables.get(condition["var"])
            return bool(value) if value is not None else False
                                                                                                                                                
        # 逻辑运算符
        for op in ["and", "or"]:
            if op in condition:
                sub_conds = condition[op]
                evaluator = all if op == "and" else any
                return evaluator(self.evaluate_condition(req_id, user_id, global_conversation_id, c, variables) for c in sub_conds)
                                                                                                                                                                                                        
        if "not" in condition:
            return not self.evaluate_condition(req_id, user_id, global_conversation_id, condition["not"], variables)

        # 比较运算符处理
        compare_ops = {
            "gt": lambda a, b: a > b,
            "lt": lambda a, b: a < b,
            "gte": lambda a, b: a >= b,
            "lte": lambda a, b: a <= b,
            "eq": lambda a, b: a == b,
            "neq": lambda a, b: a != b
        }
                                                                                                                                
        for op in compare_ops:
            if op in condition:
                operands = condition[op]
                if len(operands) != 2:
                    logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] {op} requires exactly two operands")

                left = self.resolve_operand(req_id, user_id, global_conversation_id, operands[0], variables)
                right = self.resolve_operand(req_id, user_id, global_conversation_id, operands[1], variables)
            
                # 自动处理数值类型转换
                if isinstance(left, (int, float)) and isinstance(right, (int, float)):
                    return compare_ops[op](left, right)
                elif type(left) != type(right):
                    if type(left) != str:
                        left = str(left)
                    if type(right) != str:
                        right = str(right)
               
                #logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] Type mismatch: {type(left)} vs {type(right)}, left:{left}/{operands[0]}, right:{right}/{operands[1]}")
                return compare_ops[op](left, right)

        logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] Invalid condition: {condition}")

    def match(self, req_id, user_id, global_conversation_id, intent_id, task_var, default_scene_id, last_scene_id, 
            last_scene_sub_id, debug=False):
        cond_rule = self.index_dict.get(intent_id, {})
        scene_id = default_scene_id

        task_var["last_scene_id"] = last_scene_id
        task_var["last_scene_sub_id"] = last_scene_sub_id
        logger.info(f'cond_matcher: match start, input:{task_var}')

        if cond_rule:
            for branch in cond_rule.get('branches', []):
                if "condition" not in branch or "output" not in branch:
                    logger.error("[{req_id}][{user_id}][{global_conversation_id}] Invalid branch structure")
                try:
                    if self.evaluate_condition(req_id, user_id, global_conversation_id, branch['condition'], task_var):
                        output = branch['output']
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] condition:{branch["condition"]}, ' \
                                f'out:{output}')
                        if not (0 <= output <= 99):
                            logger.error("[{req_id}][{user_id}][{global_conversation_id}] Output out of 0-99 range")
                        scene_id = output
                except KeyError as e:
                    logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] Missing key in condition: {e}")

        return scene_id

if __name__ == '__main__':
    logger.getLogger('root').setLevel(logger.DEBUG)
    with open("./intent_config.cmdp", 'r', encoding='utf-8') as file:
        intent_info = json.load(file)

        test_mat = cond_matcher()
        test_mat.init_matcher(intent_info, "./task_rule.cmdp/")
        task_var = {"debug": "用户最初表示要'点杯咖啡'，但没有明确具体咖啡种类，属于未明确菜品目标。随后助理推荐了'开拓大厦店的生椰拿铁'，用户确认接受。因此，用户需要点单但初始未明确菜品，后确认了推荐菜品。根据规则，order=2（用户没有明确菜品目标，但要点餐）。shop_name为'开拓大厦店'，add_dish为'生椰拿铁'，dish_cnt默认为1，无dish_prefer。", "new_task": True, "scene_id": 4, "out_vars": {"shop_name": "开拓大厦店", "add_dish": [{"dish_name": "生椰拿铁", "dish_cnt": 1, "dish_prefer": []}], "delete_dish": [], "shop_prefer": [], "order_dish": True, "order": 1}}

        scene_id = test_mat.match("111", "222", "333", "10002", task_var["out_vars"], task_var["scene_id"])
        logger.info(f'scene_id: {scene_id}')
