# config/logging_config.py
import logging
import sys
import structlog

def setup_logging(log_level="INFO", is_json=True):
    """
    配置 structlog
    """
    # 定义共享的处理器，无论日志最终输出到哪里都会经过它们
    shared_processors = [
        structlog.contextvars.merge_contextvars, # 整合上下文信息
        structlog.stdlib.add_logger_name,        # 添加 logger 名称
        structlog.stdlib.add_log_level,          # 添加日志级别
        structlog.processors.TimeStamper(fmt="iso", utc=False), # 添加 ISO 格式的时间戳
    ]

    # 配置标准 logging 模块，让 structlog 接管其输出
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=log_level,
    )

    if is_json:
        # 生产环境：JSON 格式
        processors = shared_processors + [
            structlog.stdlib.PositionalArgumentsFormatter(),
            structlog.processors.StackInfoRenderer(), # 渲染堆栈信息
            structlog.processors.format_exc_info,     # 格式化异常信息
            structlog.processors.UnicodeDecoder(),
            structlog.processors.JSONRenderer(),      # 最终渲染为 JSON
        ]
    else:
        # 开发环境：易读的控制台格式
        processors = shared_processors + [
            structlog.dev.ConsoleRenderer(colors=False), # 带颜色的控制台渲染器
        ]

    # 配置 structlog
    structlog.configure(
        processors=processors,
        logger_factory=structlog.stdlib.LoggerFactory(),
        wrapper_class=structlog.stdlib.BoundLogger,
        cache_logger_on_first_use=True,
    )


setup_logging(log_level=logging.DEBUG, is_json=False)

logger = structlog.getLogger(__name__)
