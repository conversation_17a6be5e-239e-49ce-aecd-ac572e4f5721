import re
import json
import time
import structlog
from typing import Iterator

import emoji
import requests
from urllib.parse import urljoin


logger = structlog.get_logger(__name__)


def remove_emoji(text: str) -> str:
    """
    使用 emoji 库移除字符串中的所有 emoji。
    """
    return emoji.replace_emoji(text, replace='')


def remove_content_in_closed_parentheses(text: str) -> str:
    """
    移除字符串中闭合的括号（半角或全角）及其内部的内容。
    该方法会迭代处理，以正确处理嵌套括号。

    例如:
    "你好(这是(一段)测试)世界" -> "你好世界"
    "你好（这是（一段）全角测试）世界" -> "你好世界"
    "你好(混合（测试）结束)世界" -> "你好世界"
    "你好(abc)def(ghi)" -> "你好def"

    Args:
        text: 原始字符串

    Returns:
        处理后的字符串
    """
    # 正则表达式匹配：
    # \(     -> 匹配半角左括号 (
    # [^()（）]* -> 匹配任意不是括号的字符零次或多次 (这是最内层的内容)
    # \)     -> 匹配半角右括号 )
    # |      -> 或者
    # （     -> 匹配全角左括号 （
    # [^()（）]* -> 匹配任意不是括号的字符零次或多次
    # ）     -> 匹配全角右括号 ）
    #
    # 这个模式会优先匹配不包含其他括号的括号对 (即最内层的)
    pattern = r"\([^()（）]*\)|（[^()（）]*）"

    # 持续替换，直到字符串中不再有符合模式的匹配项
    # 这样可以处理嵌套的情况，例如 "a (b (c) d) e"
    # 第一次会移除 "(c)" -> "a (b  d) e"
    # 第二次会移除 "(b  d)" -> "a  e"
    while re.search(pattern, text):
        text = re.sub(pattern, "", text)
        # print(f"中间步骤: {text}") # 如果需要调试，可以取消注释这行

    return text

def merge_pieces(chunks: Iterator[str]) -> Iterator[str]:
    marks = {"。", "？", "！", ",", ".", "?", ":", "，", " "}

    s = ""
    for chunk in chunks:
        chunk = chunk.strip()
        if not chunk:
            continue

        for c in chunk:
            s += c
            if c in marks and len(s) >= 5:
                yield remove_content_in_closed_parentheses(remove_emoji(s))
                s = ""
    if s:
        yield remove_content_in_closed_parentheses(remove_emoji(s))


class RequestSession(requests.Session):
    def __init__(self, base_url=None):
        super().__init__()
        self.base_url = base_url

    def request(self, method, url, *args, **kwargs):
        joined_url = urljoin(self.base_url, url)
        return super().request(method, joined_url, *args, **kwargs)


class DifyAgent:
    def __init__(self, pat_auth, bot_id, bot_name, global_setting_dict):
        # self.pat_auth = f"Bearer {pat_auth}"
        self.bot_id = bot_id
        self.bot_name = bot_name
        self.wait_time = 1
        self.retry_cnt = 180

        self.session = RequestSession(base_url=global_setting_dict["dify_addr"])

        self.default_headers = {
            'Content-Type': 'application/json',
            'Authorization': "Bearer app-7CmMZKFzUF7to6aGY2MmQNJG"
        }

    def kill_chat(self, req_id, user_id, global_conversation_id, conversation_id, chat_id):
        pass

    def op_send_req(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, conversation_id = ""):
        pass

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        return 0, "", global_conversation_id

    def op_check_status(self, req_id, user_id, global_conversation_id, chat_id, conversation_id):
        pass

    def op_get_msg(self, req_id, user_id, global_conversation_id, chat_id, conversation_id, target = "answer"):
        pass

    def wait_chat_finish(self, req_id, user_id, global_conversation_id, conversation_id, chat_id, target, create_code):
        pass


    def chat(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, need_parse = True, stream = False):
        start = time.time()
        code = 0
        err_msg = ""
        answer = ""
        model_name = ""
        prompt_token = 0
        output_token = 0

        content = {
            "user_id": user_id,
            "user": user_id,
            "response_mode": "streaming" if stream else "blocking",
            "query": chat_msg,
            "req_id": req_id,
            "inputs": {
                "input": chat_msg,
                "user_id": user_id,
                "history_str": "",
                "conversation_id": global_conversation_id,
            }
        }
        if custom_var and len(custom_var) > 0:
            # content["custom_variables"] = custom_var
            if "hist_info" in custom_var.keys():
                format_msg = lambda x: f"{x['role'] if x['role'] != 'bot' else 'assistant'}:{x['msg']}"
                content["inputs"]["history_str"] = "\n".join([format_msg(c) for c in json.loads(custom_var["hist_info"])])
                content["inputs"]["history"] = custom_var["hist_info"]
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new dify req: {content}')

        res = self.session.post(f"/v1/chat-messages", headers=self.default_headers, json=content, stream=stream)
        if not res.ok:
            code = -1
            err_msg = f"code: {res.status_code}, content: {res.text}"
        elif not stream:
            data = res.json()
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] send req, name:{self.bot_name}, resp:{data}, type:{type(data)}')
            answer = remove_content_in_closed_parentheses(remove_emoji(data["answer"]))
        else:
            # stream mode.
            def resp_iter(stream_resp):
                for line in stream_resp.iter_lines(decode_unicode=True):
                    if line.startswith("data: "):
                        try:
                            data_str = line[len("data: "):]
                            data_json = json.loads(data_str)
                            if "answer" in data_json:
                                print(data_json["answer"])
                                yield data_json["answer"]
                        except json.JSONDecodeError:
                            print(f"\n[解析错误]: {line}")
            answer = merge_pieces(resp_iter(stream_resp=res))
        return code, err_msg, answer, model_name, prompt_token, output_token, time.time() - start

if __name__ == '__main__':
    from vui_svr.settings import global_setting_dict

    bot = DifyAgent(pat_auth="", bot_id="", bot_name="", global_setting_dict=global_setting_dict)
    now = time.time()
    r = bot.chat(
        req_id="111",
        user_id="222",
        global_conversation_id="",
        chat_msg="给我讲讲今天的新闻",
        stream=True
    )
    for line in r[2]:
        print(time.time() - now)
        print(line)

