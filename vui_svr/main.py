import logging
import structlog

from flask import Flask, render_template, request, jsonify

from vui_svr.logging_config import logger

from vui_svr.agents import test_uid

from vui_svr.views.memo_view import memo_router
from vui_svr.views.chat_view import chat_router, chat_history
from vui_svr.views.aibot_view import aibot_router
from vui_svr.views.internal_view import internal_router

from vui_svr.agent_svr_V4 import clear_context_global


# create flask app
app = Flask(__name__)

app.register_blueprint(memo_router, url_prefix="/memo")
app.register_blueprint(chat_router, url_prefix="/chat")
app.register_blueprint(aibot_router, url_prefix="/aibot")

# special route
app.register_blueprint(internal_router, url_prefix="/internal")

@app.get('/ai_web')
def hello_world():
    return render_template('chat.html', chat_history=chat_history)


@app.get('/api/logger')
def logger_check():
    logger.debug("debug logger")
    logger.info("info logger")
    logger.warning("warning logger")
    logger.error("error logger")
    return {"status": "ok"}


@app.get('/depth')
def thread_depth():
    import requests
    d = request.args['d']
    print(f"current depth: {d}")
    requests.get("http://127.0.0.1:8000/depth" + f"?d={int(d)+1}")

    return {"depth": d}


# clear context before app run
clear_context_global(test_uid, "", "")


if __name__ == '__main__':
    app.run(host='0.0.0.0',port=5000)
