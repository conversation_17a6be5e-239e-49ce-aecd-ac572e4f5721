import json
import time
import requests
import structlog
# from composio.tools.local.greptile.actions.codequery import message
import re

logger = structlog.get_logger(__name__)

class LangflowAgent:
    def __init__(self, pat_auth, bot_id, bot_name, global_setting_dict):
        self.api_addr = global_setting_dict["langflow_addr"]
        self.flow_id = bot_id

    def parse_duration(self,duration_str):
        # 使用正则提取数值部分（整数或小数）
        match = re.search(r"(\d+\.?\d*)", duration_str)
        if not match:
            raise ValueError(f"无法解析 duration: {duration_str}")

        duration = float(match.group(1))  # 转换为浮点数

        # 判断单位
        if "ms" in duration_str:
            return duration / 1000  # 毫秒转换为秒
        return duration  # 默认返回秒

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        return 0, "", global_conversation_id

    ##开始执行工作流
    def create_chat(self, req_id, user_id, global_conversation_id, chat_msg, target, extra_header, ver_id, custom_var={}, conversation_id=""):
        url = f"{self.api_addr}/api/v1/build/{self.flow_id}/flow?log_builds=true"
        input_data = {
            "input_value": chat_msg 
        }

        headers = {
            "accept": "application/json",
            "Content-Type": "application/json",
            "x-api-key":"sk-RNeADGq3zkKiZG9H90C6LcdU3vovbxg4_ZjXY9PZz5Q"
        }
        for k, v in extra_header.items():
            headers[k] = v

        job_id = None
        code = 0
        err_msg = ""
        if "flow_params" in input_data:
            if type(input_data["flow_params"]) == str:
                input_data["flow_params"] = json.loads(input_data["flow_params"])
            logger.debug(f'flow_params: {input_data["flow_params"]}, type:{type(input_data["flow_params"])}')

        try:
            response = requests.post(url, json={"inputs": input_data}, headers=headers)
            response.raise_for_status()  # 确保请求成功

            response_json = response.json()

            # 解析 job_id
            job_id = response_json.get("job_id", None)
            logger.debug(f'langflow create resp: {response_json}')
            if not job_id:
                job_id = None
                logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] API 响应中未找到 job_id")
        except requests.exceptions.RequestException as e:
            code = 1
            err_msg = f'{e}'
            logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] 请求错误: {e}")
        return code, err_msg, job_id, conversation_id

    ##获取工作流的节点并获取支持参数及返回值
    def wait_chat_finish(self, req_id, user_id, global_conversation_id, conversation_id, chat_id, target, create_code):
        url = f"{self.api_addr}/api/v1/build/{chat_id}/events"
        headers = {"accept": "application/json"}
        result = {}

        output = ""
        status = "failed"
        code = 1
        run_code = "fail"
        message = None
        err_msg = ""

        try:
            with requests.get(url, headers=headers, stream=True) as response:
                response.raise_for_status()  # 确保请求成功
                sum_duration = 0
                for line in response.iter_lines():
                    if line.strip():  # 忽略空行
                        try:
                            event = json.loads(line)  # 解析 JSON
                            build_data = event.get("data", {}).get("build_data", {})
                            id = build_data.get("id")
                            next_vertices_ids = build_data.get("next_vertices_ids")
                            data = build_data.get("data")
                            if id and "duration" in data:
                                duration_str = data["duration"]
                                # 移除 "ms" 并转换为整数
                                try:
                                    duration = self.parse_duration(duration_str)
                                    sum_duration += duration
                                except ValueError:
                                    logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] 无法转换 duration: " \
                                            f"{duration_str}")  # 记录错误值
                            #if next_vertices_ids is not None and len(next_vertices_ids) == 0:
                            valid = build_data.get("valid")
                            if valid is False:
                                message = build_data.get("data").get("outputs").get("output").get("message")
                                errorMessage = message.get("errorMessage", "未提供错误信息")
                                if errorMessage:
                                    err_msg = errorMessage
                                    status = "failed"
                                    break

                            # 确保 next_vertices_ids 是列表且非空
                            if isinstance(next_vertices_ids, list) and next_vertices_ids:
                                next_vertex_id = next_vertices_ids[0]  # 取列表的第一个元素
                            else:
                                next_vertex_id = None  # 如果 next_vertices_ids 为空或 None，则设为 None

                            logger.debug(f"[{req_id}][{user_id}][{global_conversation_id}] ######################{id}" \
                                    f"###############{next_vertex_id}")

                            #if id and id.startswith("DataToJson") and next_vertex_id and next_vertex_id.startswith("ChatOutput"):
                            #    text = build_data.get("data", {}).get("outputs", {}).get("text", {})
                                # 确保 text 是字符串
                            #    message_str = text.get("message")
                            if id and id.startswith("JSONToDataComponent") and next_vertex_id and next_vertex_id.startswith("ParseData"):
                                data = build_data.get("data", {}).get("outputs", {}).get("data", {})

                                message_str = data.get("message")
                                if isinstance(message_str, str):
                                    try:
                                        message = json.loads(message_str)  # 解析 JSON 字符串
                                    except json.JSONDecodeError:
                                        logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] 解析 message 失败: {text}")
                                        message = []
                                else:
                                    message = message_str  # 如果已经是对象，直接赋值

                                # 确保 message 是列表，并且有数据
                                #if isinstance(message, list) and len(message) > 0:
                                #    message = message[0]  # 取第一个元素（字典）
                                #else:
                                #    message = {}

                                code = 0
                                status = "success"
                                output = message.get("output")
                                ret_status = message.get("status")
                                err_msg = message.get("err_msg")
                                run_code = message.get("code")
                                params = message.get("params")
                                detail_info = message.get("detail_info")
                                result["output"] = output
                                result["status"] = ret_status
                                result["err_msg"] = err_msg
                                result["err_code"] = code
                                result["params"] = params
                                result["detail_info"] = detail_info
                                #result["action_ret_msg"] = result
                                result["expire"] = message.get("expire")

                            if id and id.startswith("DataToJson") and next_vertex_id and next_vertex_id.startswith(
                                            "ChatOutput"):
                                text = build_data.get("data", {}).get("outputs", {}).get("text", {})
                                # 确保 text 是字符串
                                message_str = text.get("message")
                                if isinstance(message_str, str):
                                    try:
                                        message = json.loads(message_str)  # 解析 JSON 字符串
                                    except json.JSONDecodeError:
                                        logger.error(
                                            f"[{req_id}][{user_id}][{global_conversation_id}] 解析 message 失败: {text}")
                                        message = []
                                else:
                                    message = message_str  # 如果已经是对象，直接赋值

                                # 确保 message 是列表，并且有数据
                                if isinstance(message, list) and len(message) > 0:
                                    message = message[0]  # 取第一个元素（字典）
                                else:
                                    message = {}

                                code = 0
                                output = message.get("output")
                                ret_status = message.get("status")
                                err_msg = message.get("err_msg")
                                run_code = message.get("code")
                                result["output"] = output
                                result["status"] = ret_status
                                result["err_msg"] = err_msg
                                result["err_code"] = code
                                result["detail_info"] = ""

                            if id and id.startswith("QwenComponent"):
                                data = build_data.get("data", {}).get("outputs", {}).get("data", {})
                                message = data.get("message", {})
                                input_tokens = message.get("input_tokens")
                                output_tokens = message.get("output_tokens")
                                total_tokens = message.get("total_tokens")
                                # 存入 result
                                result["input_tokens"] = input_tokens
                                result["output_tokens"] = output_tokens
                                result["total_tokens"] = total_tokens
                        except json.JSONDecodeError as e:
                            logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] JSON 解析错误: {e}, 原始数据: {line}")

        except requests.exceptions.RequestException as e:
            logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] 请求错误: {e}")
        result["time_cost"] = sum_duration
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] resp: {result}')

        return code, status, err_msg, run_code, result 

    def kill_chat(self, req_id, user_id, global_conversation_id, conversation_id, chat_id):
        cancel_url = f"{self.api_addr}/api/v1/build/{chat_id}/cancel"
        headers = {"accept": "application/json"}
        try:
            response = requests.post(cancel_url, headers=headers)
            response.raise_for_status()  # 检查请求是否成功
            return response.json()  # 返回 JSON 响应
        except requests.exceptions.RequestException as e:
            return {"error": str(e)}  # 发生异常时返回错误信息


if __name__ == "__main__":
    # 输入数据
    start_time = time.time()  # 记录开始时间
    input_value = '''{"uid":"1900437112298143744","coords":"1, 1","intent_id":"10006","req_id":"369a611a-cbd8-4f7a-b037-7b0557017020","conversation_id":"7480842901241659432","input":"北冰洋汽水。","hist_chat":"用户:我要在京东买东西。; 你:请问您想在京东购买什么商品？; 用户:我要在京东买东西。; 你:请问您想在京东购买什么商品？","app_id":"","ts":"1741769474.418243","scene_id":"1","rule_id":"1000272","rule_name":"京东购买","rule_str":"京东购买<goods>","app_names":"京东","params":"{\\"goods\\": \\"北冰洋汽水\\"}","task_desc":"","data_sample":"","answer_sample":"买好啦","answer_type":"1","ask":"您是想在京东购买北冰洋汽水吗？"}'''
    agent = LangflowAgent()
    # 启动任务并获取 job_id
    job_id = agent.create_chat("", "", "", input_value, "", {}, "")
    # 等待 3 秒
    finish = agent.wait_chat_finish("", "", "", "", job_id, "", "")
    print(f"finish的结果为{finish}")
    result = agent.kill_chat("", "", "", "", job_id)
    print(f"result的结果为{result}")
    end_time = time.time()  # 记录结束时间
    elapsed_time = end_time - start_time  # 计算总耗时
    print(f"程序执行时间: {elapsed_time:.2f} 秒")
