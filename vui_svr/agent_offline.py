from flask import Flask, render_template, request, jsonify
import requests
import uuid
import logging
import time
import datetime
import json
import sys
import redis
from coze_api_V4 import CozeAgent
from gpt_api_V4 import GPTAgent

# create flask app
app = Flask(__name__)

# init logging
LOG_FORMAT = '[%(asctime)s] [%(name)s] [%(levelname)s] [%(funcName)s] %(message)s'
formatter = logging.Formatter(LOG_FORMAT)
handle1 = logging.StreamHandler()
handle1.setFormatter(formatter)

rootlogger = logging.getLogger('root')
while rootlogger.handlers:
        rootlogger.handlers.pop().close()
rootlogger.setLevel(logging.DEBUG)
rootlogger.addHandler(handle1)
logging.getLogger('werkzeug').setLevel(logging.ERROR)
logging.getLogger('urllib3.connectionpool').setLevel(logging.ERROR)

boke_bot = CozeAgent("pat_Ocabs6nDQEujO2VEOkiWMTFdg4RjFTsFGOU1ovijbJklsO5mbTrTOu3wOT1XOxAB", "7448828195526901794", "news_boke")
redis_client = redis.StrictRedis(host = "127.0.0.1", port = 16379, username = "default", password = "Spotlight321@")
pubsub = redis_client.pubsub()

def reg_channel():
    listen_sleep_time = 0.01
    pubsub.subscribe(**{"boke-job-trigger": build_boke})
    pubsub.run_in_thread(sleep_time = listen_sleep_time, daemon = True)

def build_boke(message):
    ret_code = 0
    ret_info = ""
    ret_msg = ""
    req_id = str(uuid.uuid4())
    logging.info(f'[req_id] build_boke, start')
    code = 0
    err_msg = ""

    user_id = "crawler"
    coords = "1"
    intent_id = "10001"
    ts = time.time()
    chat_conversation_id = "0"

    custom_var = {"uid": user_id, "coords": coords, "intent_id": intent_id, "req_id": req_id, "conversation_id":chat_conversation_id, "input": "", "app_id": "", "ts": f'{ts}', "query": ""}
    combine_msg = json.dumps(custom_var, ensure_ascii=False)

    logging.info(f'[{req_id}] before action, msg:{combine_msg}')
    code, err_msg, chat_id, conversation_id = boke_bot.create_chat(combine_msg, user_id, "answer", custom_var)
    logging.info(f'[{req_id}] action submit, code:{code}, err_msg:{err_msg}')

    if code == 0:       # task start success
        code, status, err_msg, run_code, action_ret_msg = boke_bot.wait_chat_finish(user_id, conversation_id, chat_id, "answer", code)
        logging.info(f'[{req_id}] action finished, code:{code}, status:{status}, run_code:{run_code}, err_msg:{err_msg}')
        
        if status == "canceled":
            logging.info(f'[{req_id}] action canceled')

    logging.info(f'[req_id] build_boke, finished')
    return err_msg

if __name__ == '__main__':
    reg_channel()
    app.run(host='0.0.0.0',port=5001)
