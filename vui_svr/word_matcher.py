from text_matcher import text_matcher
from ac_extractor import ac_extractor
import json
import re

class word_matcher(text_matcher):
    def __init__(self):
        self.rule_list = []
        self.brace_dict = {"(": {"right": ")", "type":0, "origin": False}, "[": {"right": "]", "type": 1, "origin": False}, "<": {"right":">", "type":-1, "origin": True}}
                                                        
    def init_matcher(self, file_path, skip_words):
        file_list = self.get_files_in_directory(file_path)
        print(f'reg_matcher, filecnt:{len(file_list)}')
        
        for file_item in file_list:
            print(f'Loading reg rule file: {file_item}')
            with open(file_item, 'r', encoding='utf-8') as file:
                rule_data = json.load(file)
            self.rule_list.append(rule_data)

            normalized_var_type_dict = {}
            for var_key, var_val in rule_data["var_list"].items():               # norm var type list
                new_var_key = var_key
#               pieces = var_key.split(":")
#               if len(pieces) == 1: new_var_key = f'{var_key}:1'
                normalized_var_type_dict[new_var_key] = var_val
            self.rule_list[-1]["var_list"] = normalized_var_type_dict

            for scene_idx, scene_item in enumerate(rule_data["rules"]):
                normalized_pre_var_dict = {}
                for var_key, var_val in scene_item["var_status"].items():       # norm pre defined vars
                    new_var_key = var_key
                    pieces = var_key.split(":")
                    if len(pieces) == 1: new_var_key = f'{var_key}:1'
                    normalized_pre_var_dict[new_var_key] = var_val
                self.rule_list[-1]["rules"][scene_idx]["var_status"] = normalized_pre_var_dict

                normalized_var_status_dict = {}
                for var_key, var_val in scene_item["var_status"].items():       # norm var status 
                    new_var_key = var_key
                    pieces = var_key.split(":")
                    if len(pieces) == 1: new_var_key = f'{var_key}:1'
                    normalized_var_status_dict[new_var_key] = var_val
                self.rule_list[-1]["rules"][scene_idx]["var_status"] = normalized_var_status_dict


                new_task_str = scene_item["new_task"]                           # norm new_task_str
                matches = re.findall("(<.*?>)", new_task_str)
                for match_item in matches:
                    new_item = match_item
                    pieces = match_item[1:-1].split(":")
                    if len(pieces) == 1: new_item= f'<{pieces[0]}:{pieces[0]}:1>'
                    if len(pieces) == 2: new_item= f'<{pieces[0]}:{pieces[1]}:1>'
                    new_task_str = new_task_str.replace(match_item, new_item)
                self.rule_list[-1]["rules"][scene_idx]["new_task"] = new_task_str

                parse_rule_list = []
                for rule_str in scene_item["rules"]:                            # norm rule_str
                    parse_rule = self.parse_rule(rule_str)
                    parse_rule_list.append(parse_rule)
                self.rule_list[-1]["rules"][scene_idx]["rule_strs"] = scene_item["rules"]
                self.rule_list[-1]["rules"][scene_idx]["rules"] = parse_rule_list

            print(f'File loaded: {file_item}')

        self.omit_list = skip_words
        print(f'reg_matcher: reg index built')

        #for rule_item in self.rule_list:
        #    print(rule_item)
        return 0

    def parse_rule(self, rule_str):
        ret = []  # 用来存储最终的匹配规则
        end = 0
        rule_len = len(rule_str)
        wrong_format = False
       
        while end < rule_len and not wrong_format:
            start = end
            while end < rule_len and rule_str[end] not in self.brace_dict: end += 1
            target_str = rule_str[start:end]
            if target_str:
                ret.append({"data": [target_str], "type": -1})

            start = end
            if start < rule_len and rule_str[start] in self.brace_dict:
                left_brace = rule_str[start]
                right_brace = self.brace_dict[rule_str[start]]["right"]
                brace_type = self.brace_dict[rule_str[start]]["type"]
                need_origin = self.brace_dict[rule_str[start]]["origin"]

                start = end + 1
                end = start
                while end < rule_len and rule_str[end] != right_brace: end += 1

                if rule_str[end] == right_brace:
                    target_str = rule_str[start:end]
                    if left_brace == "<":                   # norm parsed_rule, add default value
                        pieces = target_str.split(":")
                        if len(pieces) == 1:
                            target_str = f'{target_str}:1:0'
                        elif len(pieces) == 2:
                            target_str = f'{target_str}:0'
                        target_str = f'{left_brace}{target_str}{right_brace}'

                    sublist = target_str.split("|")
                    for sub_idx, subitem in enumerate(sublist):
                        if subitem[0] == "<" and subitem[-1] == ">":
                            new_subitem = subitem[1:-1]
                            pieces = subitem[1:-1].split(":")
                            if len(pieces) == 1: new_subitem = f'{pieces[0]}:1:0'
                            elif len(pieces) == 2: new_subitem = f'{pieces[0]}:{pieces[1]}:0'
                            sublist[sub_idx] = f'<{new_subitem}>'
                            
                    ret.append({"data": sublist, "type": brace_type})
                    end = end + 1
                else:
                    wrong_format = True

        return ret

    def reg_match(self, input_idx, input_str, rule_idx, rule_item, extract_dict, pre_var_dict, hit_path):
        is_hit = False
        var_dict = pre_var_dict
        print(f'{hit_path}, {input_idx}/{len(input_str)}, {rule_idx}/{len(rule_item)}')
        if rule_idx <= len(rule_item) :
            if input_idx >= len(input_str): is_hit = True

            if not is_hit:
                for word in self.omit_list:                  # try omit words
                    if input_str.startswith(word, input_idx):
                        hit_path.append({"word": word, "rule": "omit"})
                        is_hit, var_dict = self.reg_match(input_idx + len(word), input_str, rule_idx, rule_item, extract_dict, var_dict, hit_path)
                        if is_hit: break
                        hit_path.pop()

            if not is_hit and rule_idx < len(rule_item) and rule_item[rule_idx]["type"] == 0:             # is (), skippable
                is_hit, var_dict = self.reg_match(input_idx, input_str, rule_idx + 1, rule_item, extract_dict, var_dict, hit_path)

            if not is_hit and rule_idx < len(rule_item) and rule_item[rule_idx]["type"] in {0, 1, -1}:    # is () or []  or simple text, test match
                for word in rule_item[rule_idx]["data"]:
                    if input_str.startswith(word, input_idx):                   # is text
                        print(f'{rule_idx}, {input_idx}, {word}, {rule_item[rule_idx]}')
                        hit_path.append({"word": word, "rule": "text"})
                        is_hit, var_dict = self.reg_match(input_idx + len(word), input_str, rule_idx + 1, rule_item, extract_dict, var_dict, hit_path)
                        if is_hit: break
                        hit_path.pop()
                    elif word[0] == "<" and word[-1] == ">":                    # is entity
                        extract_key =  word[1:-1]
                        pieces = extract_key.split(":")
                        extract_type = pieces[0]
                        extract_mode = int(pieces[2])
                        extract_idx = int(pieces[1])
                        new_extract_key = f'{extract_type}:{extract_idx}'

                        if extract_type in extract_dict:
                            for extract_val in extract_dict[extract_type]:
                                if input_idx >= extract_val["start"] and input_idx <= extract_val["end"]:
                                    target_str = extract_val["wordB"] #input_str[extract_val["start"]:extract_val["end"]+1]

                                    hit_path.append({"word": word, "rule": "entity"})
                                    if f'{new_extract_key}' not in var_dict:  var_dict[f'{new_extract_key}'] = []
                                    var_dict[f'{new_extract_key}'].append(target_str)

                                    if extract_mode == 1:                # multi, test multi times
                                        is_hit, var_dict = self.reg_match(extract_val["end"] + 1, input_str, rule_idx, rule_item, extract_dict, var_dict, hit_path)
                                        if is_hit: break
                                    is_hit, var_dict = self.reg_match(extract_val["end"] + 1, input_str, rule_idx+1, rule_item, extract_dict, var_dict, hit_path)
                                    if is_hit: break

                                    hit_path.pop()
                                    if len(var_dict[f'{new_extract_key}']) > 1: var_dict[f'{new_extract_key}'].pop()
                                    else:
                                        del var_dict[f'{new_extract_key}']

        return is_hit, var_dict
    
    def match(self, input_str, extract_dict, pre_var_dict):
        print(f'reg_matcher: match start, input:{input_str}')
        result = []
        for rule_data in self.rule_list:
            intent_id = rule_data['intent_id']
            var_type_dict = rule_data['var_list']
            # print(f'reg_matcher, stage 1, testing config intent: {intent_id}')

            for rule_item in rule_data['rules']:
                scene_id = rule_item["scene_id"]
                var_val_dict = rule_item["var_default"]
                fast_ret = rule_item["fast_ret"]
                new_task = rule_item["new_task"]
                is_hit = False
                # print(f'reg_matcher, stage 2, testing scene:{scene_id}, desc:{rule_item["desc"]}')

                hit_path = []
                for rule_idx, rule_parsed in enumerate(rule_item["rules"]):
                    # print(f'reg_matcher, stage 3, testing scene:{scene_id}, rule_str:{rule_item["rule_strs"][rule_idx]}')
                    # print(rule_parsed)
                    # print(new_task)
                    is_hit, hit_var = self.reg_match(0, input_str, 0, rule_parsed, extract_dict, {}, hit_path)

                    if is_hit:
                        hit_new_task = new_task
                        out_var = {}
                        matches = re.findall("(<.*?>)", hit_new_task)
                        for match_item in matches:
                            pieces = match_item[1:-1].split(":")
                            var_key = f'{pieces[0]}'
                            ent_key = f'{pieces[1]}:{pieces[2]}'

                            if ent_key in hit_var:
                                new_val = ",".join(hit_var[ent_key])
                                hit_new_task = hit_new_task.replace(match_item, new_val)

                                if var_type_dict[var_key] == "list":
                                    out_var[var_key] = hit_var[ent_key]
                                elif var_type_dict[var_key] == "str":
                                    out_var[var_key] = ",".join(hit_var[ent_key])
                                elif var_type_dict[var_key] == "float":
                                    out_var[var_key] = float(hit_var[ent_key])
                            elif ent_key in var_val_dict:
                                new_val = ",".join(var_val_dict[ent_key])
                                hit_new_task = hit_new_task.replace(match_item, new_val)

                                if var_type_dict[var_key] == "list":
                                    out_var[var_key] = var_val_dict[ent_key]
                                elif var_type_dict[var_key] == "str":
                                    out_var[var_key] = ",".join(var_val_dict[ent_key])
                                elif var_type_dict[var_key] == "float":
                                    out_var[var_key] = float(var_val_dict[ent_key])
                            elif var_key in var_type_dict:
                                hit_new_task = hit_new_task.replace(match_item, "")

                        result.append({"hit_rule": rule_item["rule_strs"][rule_idx], "intent_id":intent_id, "scene_desc": rule_item["desc"], "scene_id":scene_id, "fast_ret": fast_ret[0], "new_task": hit_new_task, "hit_var": hit_var, "out_var": out_var})
                        break

                if is_hit:
                    print(f'reg_matcher, HIT scene:{scene_id}, vars:{result[-1]["hit_var"]}, fast_ret:{result[-1]["fast_ret"]}, new_task:{result[-1]["new_task"]}, desc:{rule_item["desc"]}, hit_path:{hit_path}')
                    break
                else:
                    print(f'reg_matcher, MISS scene:{scene_id}, desc:{rule_item["desc"]}')

        print(f'reg_matcher: match finished')
        return result


if __name__ == '__main__':
    omit_list = ["和", "或", "吗", "吧", "的", "了", "呢", "嗯", "啊", "呀", "哈", "，", "。"]

    # init extractors
    ac_ext = ac_extractor()
    ac_ext.build_index("wordlist")
    test_mactcher = reg_matcher()
    test_mactcher.init_matcher("matchrule", omit_list)

    #input_str = "帮我在附近的瑞幸点杯咖啡"
    #input_str = "帮我点杯抹茶拿铁和香草拿铁吧"
    #input_str = "附近有瑞幸或星巴克吗"
    #input_str = "帮我点一杯瑞幸的拿铁吧"
    #input_str = "好的，帮我支付吧"
    #input_str = "打车从康得去西直门"
    #input_str = "最近有啥新闻吗"
    input_str = "有啥科技类的新闻吗"
    ext_ret = ac_ext.extract(input_str)
    extract_dict = {}
    for ret_item in ext_ret:
        new_word = input_str[ret_item["start"]:ret_item["end"]+1]
        print(f'found new syn piece, start:{ret_item["start"]}, end:{ret_item["end"]}, type:{ret_item["type"]}')
        if ret_item["type"] in extract_dict:
            is_cover = False
            for exist_item in extract_dict[ret_item["type"]]:
                if ret_item["start"] <= exist_item["start"] and ret_item["end"] >= exist_item["end"]:
                    is_cover = True
                    break
            if not is_cover:
                extract_dict[ret_item["type"]].append(ret_item)
        else:
            extract_dict[ret_item["type"]] = [ret_item]
       
    print(extract_dict)
    match_ret = test_mactcher.match(input_str, extract_dict, {})

    print(f"#######################, input:{input_str}")
    for ret_item in match_ret:
        print(f'hit, intent_id:{ret_item["intent_id"]}, scene_id:{ret_item["scene_id"]}, desc:{ret_item["scene_desc"]}, hit_rule: {ret_item["hit_rule"]}, fast_ret: {ret_item["fast_ret"]}, new_task: {ret_item["new_task"]}, hit_var: {ret_item["hit_var"]}, out_var: {ret_item["out_var"]}')
