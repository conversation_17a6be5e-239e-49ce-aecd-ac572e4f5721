import structlog

from flask import Blueprint, request, jsonify

from vui_svr.agents import report_agent
from vui_svr.agent_svr_V4 import send_msg
from vui_svr.memo_module import info_agent, memo_agent

# for internal use temporarily.
internal_router = Blueprint("internal_router", __name__)

logger = structlog.get_logger(__name__)

def extract_memo_portrait(req_id, user_id, global_conversation_id, intent_id, coords):
    intent_vars = info_agent.get_vars(user_id, global_conversation_id, "0")
    intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.get_hist_chat(req_id, user_id, global_conversation_id,
            intent_id)
    logger.debug(f'[{req_id}][{req_id}][{global_conversation_id}] before extracting, intent_hist_chat:{intent_hist_chat}')
    if intent_hist_msg:
        memo_agent.extract_portrait(req_id, user_id, global_conversation_id, intent_id, coords, intent_vars, intent_hist_msg)


def set_message_hist(*args, **kwargs):
    memo_agent.set_hist_chat(*args, **kwargs)


def message_push_and_send(*args, **kwargs):
    push_args = kwargs["push_args"]
    send_args = kwargs["send_args"]

    report_agent.push_report(*push_args)
    send_msg(*send_args)


def message_push_with_merge_and_send(*args, **kwargs):
    push_args = kwargs["push_args"]
    send_args = kwargs["send_args"]

    report_agent.push_report_with_merge(*push_args)
    send_msg(*send_args)


func_name_map = {
    # "extract_memo_portrait": extract_memo_portrait,
    # "set_message_hist": set_message_hist,
    "message_push_and_send": message_push_and_send,
    "message_push_with_merge_and_send": message_push_with_merge_and_send,
}


@internal_router.post("/vui_func/exec")
def intent_status_view():
    data = request.json

    logger.debug(f"vui func exec param: {data}")

    # execute func
    func_name_map[data["func"]](*data["args"], **data["kwargs"])
    return jsonify({"result": "success"})
