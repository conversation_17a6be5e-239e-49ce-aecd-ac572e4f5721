import time
import json
import random
import uuid
import structlog
from datetime import datetime
from typing import Iterator

from flask import Blueprint, request, jsonify

from vui_svr.settings import global_setting_dict

from vui_svr.agents import (
    info_agent,
    task_agent,
    report_agent,
    status_agent,
    plugin_req_agent,
)
from vui_svr.memo_module import memo_agent
from vui_svr.intent_module import intent_bot, intent_info, action_bots
from vui_svr.redis_lock import RedisLock
from vui_svr.agent_svr_V4 import (
    role_dict,
    user_timer,
    send_msg,
    clear_context_global,
    build_task,
    redis_client,
    do_act,
    merge_details,
    clear_context
)
from vui_svr.fc_module.agent_flow import AgentFlow


aibot_router = Blueprint("aibot_router", __name__)


logger = structlog.get_logger(__name__)

@aibot_router.post('/mic_open')
def on_mic_open():
    ret_code = 0
    ret_info = ""

    data = request.json
    extra_header = request.headers

    user_id = data["uid"]
    token = data["token"]
    coords = data["coords"]
    pro_ver_id= data.get("pro_ver_id", "0")
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    all_task_list = task_agent.get_task_list(user_id, global_conversation_id)
    task_list = [item for item in all_task_list if item["status"] == "running"]
    task_status_dict = {}
    for item in task_list:
        intent_id = item["intent_id"]
        if intent_id not in task_status_dict or task_status_dict[intent_id]["curr_report_status"] > item["curr_report_status"]:
            if item.get("curr_report_status", 0) >= 0:
                task_status_dict[intent_id] = item.get("curr_report_status", 0)

    # exe_cnt = 1
    # flow_intent = ["10002", "10006"]
    # for flow_intent_id in flow_intent:
    #    if flow_intent_id in task_status_dict:
    #        if task_status_dict[flow_intent_id] > 0:
    #            exe_cnt = 2

    # cancel old timer
    if global_setting_dict["TASK_EXP_TIME"] > 0 and global_setting_dict["NEED_TIMER"]:
        longtime_tips = role_dict["default"]["script"].get("intent_llm_tips", [])
        timer_param = {"uid": user_id, "intent_id": "-1", "coords": coords,
                       "token": token, "tip_list": longtime_tips, "pro_ver_id": pro_ver_id, "mic_status": "off", "talk_status": "off"}
        # logger.debug(f'[][{user_id}][{global_conversation_id}] timer_param:{timer_param}')

        user_timer.set_task(user_id, timer_param, global_setting_dict["TASK_EXP_TIME"])
        # logger.debug(f'[][{user_id}][{global_conversation_id}] after set')

        user_timer.start(user_id)
        # logger.debug(f'[][{user_id}][{global_conversation_id}] before longtime timer, longtime_tips:{longtime_tips}')

        # timer_job_info = timer_agent.get_timer_task(user_id, global_conversation_id)
        # if timer_job_info and "idle_exit" in timer_job_info:
        #    timer_job_id = timer_job_info["idle_exit"]
        #    timer_agent.cancel_task(timer_job_id)

        # start new timer
        # params = {"uid": user_id, "intent_id": "0", "scene_type": "talk_time_exceed", "talk_cmd": "off", "mic_cmd": "off", "exe_cnt":exe_cnt, "global_conversation_id": global_conversation_id}
        # timer_agent.register_task(user_id, global_setting_dict["TASK_EXP_TIME"], params)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})


@aibot_router.post('/finish_msg')
def finish_msg():
    ret_code = 0
    ret_info = ""

    data = request.json
    extra_header = request.headers

    user_id = data["uid"]
    pro_ver_id = data.get("pro_ver_id", "0")
    finish_req_id = data.get('req_id')
    intent_id = data.get('intent_id')
    req_id = str(uuid.uuid4())
    coords = ""
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    # initial structure log contex.
    ctx_vars = dict(req_id=req_id, user_id=user_id, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        report_agent.finish_req_report(finish_req_id, user_id, global_conversation_id, intent_id)
        send_msg(req_id, coords, user_id, intent_id, global_conversation_id, "0", extra_header, pro_ver_id)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})


@aibot_router.post('/timeout_task')
def timeout_task():
    ret_code = -1
    ret_info = ""

    data = request.json
    extra_header = request.headers

    user_id = data["uid"]
    pro_ver_id = data.get("pro_ver_id", "0")

    req_id = str(uuid.uuid4())
    coords = ""
    intent_id = "0"
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    # initial structure log contex.
    ctx_vars = dict(req_id=req_id, user_id=user_id, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        task_list = task_agent.get_target_task(user_id, global_conversation_id, {"running"})
        if not task_list:   # 无运行中的任务，但APP在等待，意味着同步出错
            ret_msg = "服务出错了。"
            report_agent.push_report(req_id, coords, user_id, intent_id, "0", "-1", ret_msg, "", -1, "0", global_conversation_id ,"off", "off", "error", "must", -1)
            msg_cnt = send_msg(req_id, coords, user_id, intent_id, global_conversation_id, "0", extra_header, pro_ver_id)
            task_agent.cancel_all_task(req_id, user_id, global_conversation_id, action_bots)
            clear_context_global(user_id, global_conversation_id, coords, req_id)
        else:               # 有任务在跑，垫话提示
            ret_msg = "稍等一会。"
            ret_code = 0
            report_agent.push_report(req_id, coords, user_id, intent_id, "0", "-1", ret_msg, "", -1,
                                     "0", global_conversation_id, "on", "off", "error", "must", -1)
            send_msg(req_id, coords, user_id, intent_id, global_conversation_id, "0", extra_header, pro_ver_id)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})


@aibot_router.post('/disturb_task')
def disturb_task():
    ret_code = 0
    ret_info = ""
    ret_msg = ""
    global_conversation_id = ""

    data = request.json
    extra_header = request.headers

    user_id = data["uid"]
    pro_ver_id = data.get("pro_ver_id", "0")
    req_id = str(uuid.uuid4())
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    task_agent.cancel_all_task(req_id, user_id, global_conversation_id, action_bots)
    if global_setting_dict["interaction_type"] == "simple_round":
        clear_context_global(user_id, global_conversation_id, "", req_id)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})


@aibot_router.post('/disturb_msg')
def disturb_msg():
    ret_code = 0
    ret_info = ""

    data = request.json

    extra_header = request.headers

    user_id = data["uid"]
    pro_ver_id= data.get("pro_ver_id", "0")
    finish_req_id = data.get('req_id')
    intent_id = data.get('intent_id')
    req_id = str(uuid.uuid4())
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    task_agent.cancel_task(req_id, user_id, global_conversation_id, action_bots)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})


@aibot_router.post('/talk_start')
def talk_start():
    ret_code = 0
    ret_info = ""
    ret_msg = ""
    global_conversation_id = ""

    data = request.json
    extra_header = request.headers

    user_id = data["uid"]
    pro_ver_id= data.get("pro_ver_id", "0")
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    status_agent.set_status(user_id, "on")
    if global_setting_dict["TASK_EXP_TIME"] > 0 and global_setting_dict["NEED_TIMER"]:
        user_timer.stop(user_id)
        # timer_job_info = timer_agent.get_timer_task(user_id, global_conversation_id)
        # if timer_job_info and "exp_task_id" in timer_job_info:
        #    timer_job_id = timer_job_info["exp_task_id"]
        #    timer_agent.cancel_task(timer_job_id)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})

@aibot_router.post('/add_push')
def add_push():
    data = request.json
    info_agent.add_user_push(data["uid"], data["push_info"])
    return jsonify({'output': "", "code": 0, "info": "", "status" :0, 'detail_info': "{}"})


@aibot_router.post('/send_push')
def send_push():
    ret_code = 0
    ret_info = ""

    data = request.json
    user_id = data["uid"]

    client_status = json.loads(data["client_status"])
    if isinstance(client_status, str):
        client_status = json.loads(client_status)
    # logger.info(f"[{user_id}] client status data: {client_status}")

    req_id = data.get('req_id')
    pro_ver_id = data.get("pro_ver_id", "0")
    coords = data.get('coords')
    global_conversation_id = data.get('global_conversation_id')

    fg_intent_id = "0"
    fg_intent_source = "task"
    extra_header = dict(request.headers)

    mic_status = "on"
    talk_status = "on"
    if int(client_status["message"]["data"]) == 1:
        mic_status = "off"
        talk_status = "off"

    # initial structure log contex.
    ctx_vars = dict(req_id=req_id, user_id=user_id, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        push_list = info_agent.del_user_push(user_id)
        for item in push_list:
            # last_item = push_list[-1]
            logger.debug(f'do push go! msg: {item}')

            if item.get('expire_time') and time.time() > item["expire_time"]:
                logger.debug(f'skip expired msg: {item}')
                continue

            mic_status = item.get("mic_status", mic_status)
            talk_status = item.get("talk_status", talk_status)
            report_agent.push_report(req_id, coords, user_id, item["intent_id"], "-1", "-1", item["message"], "", -1, "0",
                                     global_conversation_id, talk_status, mic_status, "info", "must", -1)
            send_msg(req_id, coords, user_id, item["intent_id"], global_conversation_id, fg_intent_id, extra_header, pro_ver_id)

    return jsonify({'output': "", "code": ret_code, "info": ret_info, "status" :0, 'detail_info': "{}"})

@aibot_router.post('/new_msg')
def new_msg():
    time_start = time.time()
    run_details = {"time_cost": "0", "time_details": {}, "prompt_token" :0, "output_token" :0, "token_details": {}}

    data = request.json

    chat_msg = data.get('msg').strip()
    req_id = data.get('req_id')
    global_conversation_id = data.get('global_conversation_id')
    coords = data.get('coords')
    token = data.get('token')
    extra_header = dict(request.headers)

    user_id = data["uid"]
    pro_ver_id= data.get("pro_ver_id", "0")
    audio_blob_id = data.get("audio_blob_id", "")
    # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] pro_ver_id:{pro_ver_id}')

    # initial structure log contex.
    ctx_vars = dict(req_id=req_id, user_id=user_id, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(**ctx_vars):
        fg_intent_id = "0"
        fg_intent_source = "task"

        status_agent.set_status(user_id, global_conversation_id, "off")     # turn talking status off

        # cancel timer
        if global_setting_dict["TASK_EXP_TIME"] > 0 and global_setting_dict["NEED_TIMER"]:
            user_timer.stop(user_id)
            # timer_job_info = timer_agent.get_timer_task(user_id, global_conversation_id)
            # if timer_job_info and "exp_task_id" in timer_job_info:
            #    timer_job_id = timer_job_info["exp_task_id"]
            #    timer_agent.cancel_task(timer_job_id)

        if chat_msg == "PROLOGUE":
            role_name = role_dict["default"]["name"]
            prologue_msg_list = role_dict["default"]["script"]["first_prologue"]
            prologue_msg = prologue_msg_list[random.randint(0, len(prologue_msg_list) - 1)]
            intent_id = "10004"
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] prologue, msg:{chat_msg}, role:{role_name}, '
                         f'intent_id:{intent_id}')

            clear_context_global(user_id, global_conversation_id, coords, req_id)

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", prologue_msg, "", -1, "0",
                                     global_conversation_id, "on", "on", "info", "must", -1)
            send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': '', "code": 1, "info": "hit prologue", "status" :0, 'detail_info': run_details})

        # get fg_intent & chat history & vars
        intent_status = info_agent.get_intent_status(user_id, global_conversation_id)
        for intent_key, intent_val in intent_status.items():
            if intent_val["is_fg"] == 1:
                fg_intent_id = intent_key
                fg_intent_source = intent_val["source"]
        # dont pass conversation as filter atm.
        hist_chat, hist_msg, last_msg = memo_agent.get_hist_chat(req_id, user_id, "", "0")

        # set intent name to hist_chat
        for i, v in enumerate(hist_chat):
            intent_obj = intent_info.get(v["intent_id"], dict())
            hist_chat[i]["intent_name"] = intent_obj["name"] if intent_obj else ""

        hist_chat_json = json.dumps(hist_chat, ensure_ascii=False)
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] new msg:"{chat_msg}", hist_msg:"{hist_chat_json}"')

        vars_lock_key = f"{user_id}_0_vars"
        vars_lock = RedisLock(redis_client, vars_lock_key)
        if vars_lock.acquire():
            try:
                intent_vars = info_agent.get_vars(user_id, global_conversation_id, "0")
                # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] old intent_vars:{intent_vars}')

                custom_var = {
                    "hist_info": hist_chat_json,
                    "ts": f'{time_start * 1000}',
                    "req_id": req_id,
                    "uid": user_id,
                    "coords": coords,
                    "conversation_id": "",
                    "intent_id": "",
                    "app_id": ""
                }

                intent_in_var = intent_vars
                # if "flow_params" in intent_in_var:
                #    for param_key, param_val in intent_in_var["flow_params"].items():
                #        intent_in_var[param_key] = param_val

                intent_tips = role_dict["default"]["script"].get("intent_llm_tips", [])
                timer_param = {
                    "uid": user_id,
                    "intent_id": "-1",
                    "coords": coords,
                    "token": token,
                    "tip_list": intent_tips,
                    "pro_ver_id": pro_ver_id
                }
                # logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] timer_param:{timer_param}')
                # user_timer.set_task(user_id, timer_param, 5)
                # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] after set')
                # user_timer.start(user_id)
                # logger.debug(
                #     f'[{req_id}][{user_id}][{global_conversation_id}] before intent timer, intent_tips:{intent_tips}'
                # )
                intent_ret_dict, intent_run_details = intent_bot.do_analyze(
                    req_id, user_id, chat_msg, custom_var, intent_in_var, global_conversation_id, hist_chat
                )
                # user_timer.stop(user_id)

                # if "out_var" in intent_ret_dict:
                #    intent_out_var = intent_ret_dict["out_var"]
                # else:
                #    intent_out_var = intent_vars

                merge_details(run_details, intent_run_details, "intent_analyze")
                info_agent.set_vars(user_id, global_conversation_id, "0", intent_in_var)
                # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new intent_vars:{intent_vars}')
            except Exception as e:
                vars_lock.release()
                logger.exception(f'error when analyze intent, {e}')

                intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.set_hist_chat(
                    req_id, user_id, global_conversation_id, "0", "user", chat_msg, "0", -1, "-1", "on", "on",
                    audio_blob_id=audio_blob_id,
                )

                safe_msg_list = role_dict["default"]["script"]["safe_answer"]
                safe_msg = safe_msg_list[random.randint(0, len(safe_msg_list) - 1)]

                before_sent_time = time.time()
                report_agent.push_report(req_id, coords, user_id, "0", "0", "-1", safe_msg, "", -1, "0", global_conversation_id,
                                         "on", "on", "error", "must", -1)
                send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
                after_sent_time = time.time()

                run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
                run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
                return jsonify({'output': '', "code": -6, "info": "intent err", "status" :0, 'detail_info': run_details})
            finally:
                # user_timer.stop(user_id)
                logger.info(f'stop intent timer')

            vars_lock.release()
        else:
            intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.set_hist_chat(
                req_id, user_id, global_conversation_id, "0", "user", chat_msg, "0", -1, "-1", "on", "on",
                audio_blob_id=audio_blob_id,
            )
            logger.error(f'error when getting vars lock: {vars_lock_key}')
            before_sent_time = time.time()
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': '', "code": -1, "info": "varlock err", "status" :0, 'detail_info': run_details})


        instant_reply = intent_ret_dict.get("instant_reply", "")
        intent_id = intent_ret_dict["intent_id"]

        if intent_id == "-1" or "fast_ret" not in intent_ret_dict: # or intent_id not in action_bots:
            err_msg_list = [
                "不好意思，我刚刚开小差了",
                "嗯，这个我暂时还不会呢。",
                "我还不太会呢。",
                "我还没学会这个哎。",
                "我目前还没掌握啦。",
                "我现在还做不来。",
                "我还没弄明白这个怎么做。"
            ]
            err_msg = err_msg_list[random.randint(0, len(err_msg_list) - 1)]
            mic_cmd = "on"
            talk_cmd = "on"
            if global_setting_dict["interaction_type"] in {"single_round", "simple_round"}:
                mic_cmd = "off"
                talk_cmd = "off"

            intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.set_hist_chat(
                req_id, user_id, global_conversation_id, "0", "user", chat_msg, "0", "1", "-1", "on", "on",
                audio_blob_id=audio_blob_id,
            )

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "0", "-1", err_msg, "", 0, "0", global_conversation_id,
                                     talk_cmd, mic_cmd, "error", "must", -1)
            logger.error(f'unknown intent, intent:{intent_id}, reply:{err_msg}')
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': err_msg, "code" :-2, "info": "unknown intent", "status" :0, 'detail_info': run_details})

        fast_ret = intent_ret_dict["fast_ret"]
        # intent_name = intent_info[intent_id]["name"]
        cmd_str = intent_ret_dict["cmd_str"]
        intent_new_task = intent_ret_dict["new_task"]
        task_out_vars = intent_vars.get(intent_id, {}) # intent_ret_dict["out_var"]
        scene_id = intent_ret_dict["scene_id"]
        target_words = intent_ret_dict["params"].get("hit_word", "")
        target_req_id = intent_ret_dict["params"].get("hit_req_id", "")

        intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.set_hist_chat(
            req_id, user_id, global_conversation_id, intent_id, "user", chat_msg, intent_id, scene_id, "-1", "on", "on",
            audio_blob_id=audio_blob_id,
        )

        # do cmds, to be continued
        if cmd_str == "BYEBYE":
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] cmd byebye')
            bye_msg = " "
            if global_setting_dict["NEED_BYEBYE_TEXT"]:
                bye_msg_list = role_dict["default"]["script"]["bye_text"]
                bye_msg = bye_msg_list[random.randint(0, len(bye_msg_list) - 1)]

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, "0", "0", "-1", bye_msg, "", 0, "0", global_conversation_id,
                                     "off", "off", "info", "must", -1)
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': '', "code": 6, "info": "cmd bye", "status" :0, 'detail_info': run_details})
        elif cmd_str == "INSTANT_REPLY" or intent_id not in action_bots:
            logger.info(f'cmd instant reply, intent_id:{intent_id}, msg:{instant_reply}, scene_id:{scene_id}')
            talk_cmd = "on"
            # if global_setting_dict["interaction_type"] == "simple_round": talk_cmd = "off"
            # streaming support
            if isinstance(instant_reply, Iterator):
                total_msg = ""
                while True:
                    try:
                        cur_msg = next(instant_reply)
                        # logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] first msg return {cur_msg}')
                        total_msg += cur_msg
                        report_agent.push_report(
                            req_id, coords, user_id, intent_id, "-1", "-1", cur_msg, {}, -1, "0",
                            global_conversation_id, talk_cmd, "on", "task", "must", -1, with_ack=False
                        )
                        send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header,
                                 pro_ver_id)
                    except StopIteration:
                        report_agent.push_report(
                            req_id, coords, user_id, intent_id, "-1", "-1", "*#*#4636#*#*" + total_msg, {}, -1, "0",
                            global_conversation_id, talk_cmd, "on", "task", "must", -1, with_ack=True
                        )
                        send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header,
                                 pro_ver_id)
                        break
            else:
                report_agent.push_report(
                    req_id, coords, user_id, intent_id, "-1", "-1", instant_reply, {}, -1, "0", global_conversation_id,
                    talk_cmd, "on", "task", "must", -1
                )
                send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)

            task_status_dict = {intent_id: {"in_stack": 0, "is_fg": 1}, fg_intent_id: {"in_stack": 1, "is_fg": 0}}
            info_agent.set_intent_status(user_id, global_conversation_id, [intent_id], [], task_status_dict)

            before_sent_time = time.time()
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': f'{cmd_str}:{intent_new_task}', "code": 2, "info": "instant reply", "status" :0,
                            'detail_info': run_details})

        elif cmd_str == "REPLAY":
            if last_msg == "":
                resp_msg = f'咦，我刚刚似乎没有说话。'
            else:
                resp_msg = f'{fast_ret}, {last_msg}'

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", resp_msg, "", -1, "0", global_conversation_id,
                                     "on", "on", "task", "must", -1)
            logger.info(f'cmd replay, last_msg:{last_msg}')
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            after_sent_time = time.time()

            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': cmd_str, "code": 3, "info": "replay word", "status" :0, 'detail_info': run_details})

        elif cmd_str == "INSTANT_CMD": # unused yet
            resp_msg = ""
            if intent_new_task == "TASK_CONFIRM":
                target_task = task_agent.set_task_param(user_id, intent_id, target_req_id, "running", global_conversation_id,
                                                        fg_intent_id)
                do_act(user_id, coords, intent_id, global_conversation_id, fg_intent_id, target_task, extra_header, pro_ver_id,
                       token)
            elif intent_new_task == "TASK_DENY":
                target_task = task_agent.set_task_param(user_id, intent_id, target_req_id, "cancel", global_conversation_id,
                                                        fg_intent_id)
                resp_msg = "好的，那我先把任务取消了。"
            elif intent_new_task == "TASK_DELAY":
                target_task = task_agent.set_task_param(user_id, intent_id, target_req_id, "hangup", global_conversation_id,
                                                        fg_intent_id)
                resp_msg = "好的，需要的时候再问我吧。"

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", resp_msg, "", -1, "0", global_conversation_id,
                                     "on", "on", "task", "must", -1)
            logger.info(f'cmd instant cmd, last_msg:{last_msg}')
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)

            after_sent_time = time.time()
            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': cmd_str, "code": 6, "info": "intent cmd", "status" :0, 'detail_info': run_details})

        elif cmd_str == "CANCEL_ACT" or cmd_str == "CANCEL_ACT_2": # unused yet
            logger.info(f'cmd cancel action, intent_id:{intent_id}, msg:{fast_ret}')
            workflow_cancel_code, workflow_cancel_msg, plugin_cancel_status, plugin_cancel_code, cancel_msg = task_agent.cancel_task(
                req_id, user_id, global_conversation_id, intent_id, action_bots[intent_id], plugin_req_agent)

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", cancel_msg, "", -1, "0", global_conversation_id,
                                     "on", "on", "task", "must", -1)
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            logger.info(f'cmd cancel action, intent_id:{intent_id}, msg:{cancel_msg}, return')

            after_sent_time = time.time()
            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': cmd_str, "code": 4, "info": "cancel action", "status" :0, 'detail_info': run_details})

        elif cmd_str == "CANCEL_INTENT":  # unused yet
            clear_context(req_id, user_id, intent_id, coords)

            before_sent_time = time.time()
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", fast_ret, "", -1, "0", global_conversation_id,
                                     "off", "off", "task", "must", -1)
            logger.info(f'cmd: [{cmd_str}], intent_id:{intent_id}, msg:{fast_ret}')
            send_msg(req_id, coords, user_id, "0", global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            after_sent_time = time.time()
            run_details["time_details"]["final_msg"] = f"{(after_sent_time - before_sent_time):.3f}"
            run_details["time_cost"] = f"{(after_sent_time - time_start):.3f}"
            return jsonify({'output': cmd_str, "code": 5, "info": "cancel intent", "status" :0, 'detail_info': run_details})

        if intent_id == "10007": # 打车
            timer_param = {
                "uid": user_id,
                "intent_id": intent_id,
                "coords": coords,
                "token": token,
                "tip_list": intent_tips,
                "pro_ver_id": pro_ver_id
            }
            user_timer.set_task(user_id, timer_param, 5)
            user_timer.start(user_id)

            try:
                af = AgentFlow(cache=redis_client)
                reply = af.main_agent_flow(user_id, req_id, global_conversation_id, intent_id, chat_msg, hist_msg, coords)
            except Exception:
                logger.exception(f"agent process message err.")
                reply = "服务出错了"
            finally:
                user_timer.stop(user_id)

            # send msg
            report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", reply, "", -1, "0",
                                     global_conversation_id, "on", "on", "task", "must", -1)
            send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            return jsonify({'output': reply, "code": 0, "info": "sample req", "status" :0, 'detail_info': run_details})


        # valid intent_id, do acts
        if intent_id in action_bots:
            # get chat_conversation_id
            chat_conversation_id = ""
            conversationid_lock_key = f"{user_id}_{global_conversation_id}_{intent_id}_conversationid"
            conversationid_lock = RedisLock(redis_client, conversationid_lock_key)
            if conversationid_lock.acquire():
                try:
                    # coze requires.
                    chat_conversation_id = info_agent.get_profile_item(
                        user_id, f"{global_conversation_id}_{intent_id}_conversationid"
                    )
                    if not chat_conversation_id:
                        code, msg, chat_conversation_id = action_bots[intent_id].op_new_conversation(req_id, user_id, global_conversation_id)
                        # logger.debug(f"[{req_id}] got new chat conversation_id:{chat_conversation_id}")
                    # else:
                    #     logger.info(f"[{req_id}][{user_id}][{global_conversation_id}] got exist chat_conversation_id")
                    info_agent.set_profile_item(user_id, f"{intent_id}_conversationid", chat_conversation_id)
                finally:
                    conversationid_lock.release()
            else:
                logger.error(f'error when getting report lock: {conversationid_lock_key}')

                run_details["time_details"]["final_msg"] = "0"
                run_details["time_cost"] = f"{(time.time() - time_start):.3f}"
                return jsonify(
                    {
                        'output': cmd_str,
                        "code": -3,
                        "info": "get conversationid fail",
                        "status" :0,
                        'detail_info': run_details
                    }
                )

            if global_setting_dict["interaction_type"] in {"simple_round"}:
                not_fast_ret = True
                hist_intent = {}
                intent_prolog = ""
                for chat_item in hist_chat:
                    # logger.debug(f'{req_id}][{user_id}][{global_conversation_id}] chat_item: {chat_item}')
                    chat_intent_id = chat_item["intent_id"]
                    hist_intent[chat_intent_id] = 1

                if intent_id not in hist_intent:
                    intent_prolog = intent_info.get(intent_id, {}).get("prolog", "")

                #if intent_prolog:
                    #report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", intent_prolog, "", -1,
                    #                         chat_conversation_id, global_conversation_id, "on", "off", "task", "omit", -1)
                    #send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header,
                    #         pro_ver_id)

            # do acts
            logger.info(f"submitting task, intent:{intent_id}, task submit:{chat_msg}")
            # executor.submit(build_task, req_id, intent_id, chat_msg, user_id, chat_conversation_id, coords, hist_msg, intent_hist_msg, fast_ret, intent_new_task, task_out_vars, scene_id)
            build_task_ret, build_task_run_details = build_task(req_id, intent_id, chat_msg, user_id, chat_conversation_id, coords, hist_msg,
                    intent_hist_msg, fast_ret, intent_new_task, task_out_vars, scene_id, global_conversation_id, fg_intent_id, fg_intent_source,
                    extra_header, pro_ver_id, token)
            merge_details(run_details, build_task_run_details, "build_task")

            ret_msg = fast_ret
        else:
            logger.error(f'input msg:{chat_msg}, Invalid intent_id: {intent_id}')
            ret_info = f"Invalid intent_id:{intent_id}"

            run_details["time_details"]["final_msg"] = "0"
            run_details["time_cost"] = f"{(time.time() - time_start):.3f}"
            return jsonify({'output': cmd_str, "code": -4, "info": ret_info, "status" :0, 'detail_info': run_details})

        run_details["time_details"]["final_msg"] = "0"
        run_details["time_cost"] = f"{(time.time() - time_start):.3f}"
        return jsonify({'output': ret_msg, "code": 0, "info": "sample req", "status" :0, 'detail_info': run_details})
