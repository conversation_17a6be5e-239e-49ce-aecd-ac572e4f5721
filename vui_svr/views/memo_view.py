import json
from json import JSONDecodeError

from flask import Blueprint, request, jsonify

from vui_svr.settings import global_setting_dict
from vui_svr.memo_module import memo_agent

memo_router = Blueprint("memory_router", __name__)


def parse_order_params(data: dict):
    context = json.loads(data['context'])
    script_params = data.get('scriptParams', dict())
    return {
        "user_id": context["uid"],
        "app_id": context["app_id"],
        "req_id": context.get("req_id", ""),
        "conversation_id": context.get("conversation_id", ""),
        "context": context,
        "order_data_list": script_params.get("order_data_list", []),
    }


@memo_router.post("/fetch_orders")
def user_order_history():
    params = parse_order_params(request.json)
    user_id = params.get("user_id")
    app_id = params.get("app_id")
    if not user_id or not app_id:
        raise
    results = memo_agent.get_order_history(user_id, app_id, limit=5)
    return jsonify({
        "status": "success",
        "data":[r.order_data for r in results]
    })


@memo_router.post("/add_orders")
def update_user_order_history():
    params = parse_order_params(request.json)
    memo_agent.add_order_history(
        user_id=params["user_id"],
        app_id=params["app_id"],
        context=params["context"],
        req_id=params.get("req_id", ""),
        conversation_id=params.get("conversation_id", ""),
        order_data_list=params["order_data_list"],
    )
    return jsonify({"status": "success"})


@memo_router.post("/news_prefs")
def add_news_preference_keywords():
    req_id = request.json["req_id"]
    user_id = request.json["user_id"]
    conversation_id = request.json["conversation_id"]
    likes = request.json["likes"]
    if not likes:
        return jsonify({"status": "fail", "message": "empty likes data"}), 400

    memo_agent.add_user_news_preference_history(
        req_id=req_id, user_id=user_id, conversation_id=conversation_id, likes=likes
    )
    return jsonify({"status": "success"})


@memo_router.get("/user_prefs_score")
def user_prefs_score():
    user_id = request.args["user_id"]
    data = memo_agent.user_news_preference_scores(user_id, limit=10)
    return jsonify({"status": "success", "data": data})
