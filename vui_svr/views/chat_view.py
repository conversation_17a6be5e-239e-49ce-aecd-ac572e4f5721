import uuid
import structlog
from datetime import datetime

from flask import Blueprint, request, jsonify

from vui_svr.agents import client_ver_dict
from vui_svr.settings import global_setting_dict, test_token
from vui_svr.client_message import *
from vui_svr.agent_svr_V4 import new_message_async, send_msg, executor, clear_context_global

from vui_svr.agents import (
    info_agent,
    task_agent,
    server_req_agent,
    token_agent,
    report_agent,
    appid_agent,
    test_uid
)
from vui_svr.intent_module import intent_info


chat_router = Blueprint("chat_router", __name__)

chat_history = []

run_type = global_setting_dict["run_type"]    # APP/WEB


logger = structlog.get_logger(__name__)


@chat_router.post('/send')
def send_message():
    message = request.form.get('message')
    if message:
        req_id = str(uuid.uuid4())
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        pro_ver_id = "0"
        extra_header = {"Authorization": test_token, "pro_ver_id": pro_ver_id}
        chat_history.append({'user': 'Me', 'message': message, 'time': timestamp})

        new_message_async(req_id, test_uid, "116.312564,40.059029", message, extra_header, pro_ver_id, test_token)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    return jsonify({'status': 'success', 'message': message})

# DEBUG - 处理WEB页面的信息更新
@chat_router.post('/rec')
def receive_message():
    data = request.get_json()
    data = json.loads(data)
    data_type = data["message"]["type"]
    output = data["message"]["data"]
    logger.debug(f'msg to send, type:{data_type}, data:{output}')

    timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    if message_type_dict[data_type] == "MESSAGE_TYPE_TEXT":
        chat_history.append({'user': 'AIBot_AUD', 'message': output, 'time': timestamp})
    elif message_type_dict[data_type] == "MESSAGE_TYPE_TPL_CARD":
        chat_history.append({'user': 'AIBot_TPL', 'message': f"title:{output['title']}, content:{output['content']}, ' \
                f'url:{output['pic_url']}", 'time': timestamp})
    return jsonify({'status': 'received', 'message': output})


# DEBUG - WEB页面获取消息列表
@chat_router.get('/history')
def chat_history_route():
    return jsonify(chat_history)


# APP - 处理信息输入
@chat_router.post('/clear_profile')
def clear_profile():
    req_id = str(uuid.uuid4())
    data = request.get_data()
    data = data.decode("utf-8")
    print(data, type(data))
    if type(data) == str:
        data = json.loads(data)
    #print(data, type(data))

    user_id = data["uid"]
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] clear context for user')
    info_agent.clear_global_context(user_id, global_conversation_id, intent_info)
    task_agent.clear_global_task(user_id, global_conversation_id, intent_info)
    report_agent.clear_global_report(user_id, global_conversation_id, intent_info)
    appid_agent.clear_global_appid(user_id, global_conversation_id, intent_info)

    return jsonify({'status': 'received', 'message': req_id})

# APP - 处理langflow信息
@chat_router.post('/rec_langflow')
def rec_message_from_langflow():
    ret_code = 0
    ret_info = ""

    if not request.is_json:
        logger.error(f"Invalid json request, msg:{request.get_data(parse_form_data=False)}")
        return jsonify({'output': "", "code": 1, "info": "not a json request", "status": 0, 'detail_info': "{}"})

    context = json.loads(request.json['context'])
    params = json.loads(request.json['scriptParams'])

    #data = json.loads(data)
    user_id = context.get("uid", "")
    intent_id = context.get("intent_id", "")
    coords = context.get("coords", "")
    token = context.get("token", "")
    extra_header = {"Authorization": token}
    pro_ver_id= context.get("pro_ver_id", "0")
    talk_status = context.get("talk_status", "on")
    mic_status = context.get("mic_status", "off")
    msg_type = context.get("msg_type", "must")

    msg_text = params.get("message", "")

    req_id = str(uuid.uuid4())
    global_conversation_id = info_agent.get_global_conversation_id(user_id)

    ctx_vars = dict(req_id=req_id, user_id=user_id, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(ctx_vars):
        logger.info(f"recv voice from langflow, context:{context}, param:{params}")
        report_agent.push_report(req_id, coords, user_id, intent_id, "-1", "-1", msg_text, "", 0, "",
            global_conversation_id, talk_status, mic_status, "info", msg_type, -1)
        send_msg(req_id, coords, user_id, intent_id, global_conversation_id, "0", extra_header, pro_ver_id)

        return jsonify({'output': "", "code": ret_code, "info": ret_info, "status":0, 'detail_info': "{}"})

# APP - 处理信息输入
@chat_router.post('/rec_app')
def rec_message_from_app():
    msg = ClientMessage()

    data = request.get_data()
    data = data.decode("utf-8")
    #print(data, type(data))
    #logger.debug(f'msg: {data}, {type(data)}')
    #if type(data) == str:
    #    data = json.loads(data)
    #print(data, type(data))
    req_id = str(uuid.uuid4())

    header = request.headers
    # logger.debug(f'header:{header}, type:{type(header)}')

    msg.loads(data)
    token = header.get("Authorization", "")
    if token:
        auth_res, auth_value = token_agent.auth_token_wrap(token, req_id)
        if not auth_res:
            return jsonify({'output': "授权错误", "code": 0, "info": "sample req", "status":0, 'detail_info': ""})
        uid = auth_value
        # logger.debug(f'token uid: {uid}')
    else:
        uid = msg.context.get("uid", "")
        # logger.debug(f'context: {msg.context}, uid:{uid}')

    global_conversation_id = info_agent.get_global_conversation_id(uid)

    ctx_vars = dict(req_id=req_id, user_id=uid, global_conversation_id=global_conversation_id)
    with structlog.contextvars.bound_contextvars(ctx_vars):
        # client_message = data
        # if type(client_message) != str:
        #     client_message = json.dumps(client_message, ensure_ascii=False)
        # msg.loads(client_message)

        #uid = msg.context["uid"]
        # coords = msg.context["coords"]
        ver_name = msg.context.get("voice_api_version", "")
        audio_blob_id = msg.context.get("audio_blob_id", "")
        pro_ver_id = client_ver_dict[run_type].get(ver_name, "0")

        coords = token_agent.coords_info_from_token(token, pro_ver_id)

        old_req_id = ""
        intent_id = ""
        extra_header = {"Authorization": header.get("Authorization", ""), "pro_ver_id": pro_ver_id}

        if _conversation := msg.message.get("conversation", None):
            old_req_id = _conversation.get("req_id", "")
            intent_id = _conversation.get("intent_id", "")
        logger.debug(f'coords:{coords}, old_req:{old_req_id}, intent_id:{intent_id}, ver_name:{ver_name}, pro_ver_id:{pro_ver_id}')

        if msg.message_modal == MESSAGE_MODAL_VOICE:
            if message_type_dict[msg.message_type] == "MESSAGE_TYPE_TEXT":
                logger.info(f"recv voice info! Sending waiting reports, data:{data}")
                msg_text = msg.message["data"].lower()
                if msg_text:
                    executor.submit(new_message_async, req_id, uid, coords, msg_text, extra_header, pro_ver_id, token, audio_blob_id)
                else:
                    resp_msg = "抱歉，没听清，请再说一遍吧"
                    logger.info(f"recv voice info! Empty info, Discard :[{msg_text}]")
                    report_agent.push_report(req_id, coords, uid, intent_id, "-1", "-1", resp_msg, "", 0, "",
                        global_conversation_id, "on", "on", "info", "must", -1)
                    send_msg(req_id, coords, uid, intent_id, global_conversation_id, "0", extra_header, pro_ver_id)

        if msg.message_modal == MESSAGE_MODAL_INFO:
            if message_type_dict[msg.message_type] == "MESSAGE_TYPE_TPL_CARD":
                logger.info(f"recv multi-modal info! Sending waiting reports, data:{data}")
                has_reply = False
                if 'action' in msg.message["data"]:
                    if 'reply' in msg.message["data"]["action"]:
                        has_reply = True
                        msg_text = msg.message["data"]["action"]["reply"]
                        logger.info(f'new conversation start,msg:{message_type_dict[msg.message_type]}, reply msg:{msg_text}')
                        executor.submit(new_message_async, req_id, uid, coords, msg_text, extra_header, pro_ver_id, token, audio_blob_id)
                if not has_reply:
                    logger.error(f'missing reply in tpl message, data struct:{msg.message["data"]}')

        if msg.message_modal == MESSAGE_MODAL_CMD:
            if message_type_dict[msg.message_type] == "MESSAGE_TYPE_CMD_TIMEOUTREQ":
                logger.info(f'recv time out signal! sending waiting reports, data:{data}')
                msg = {"uid":uid, "pro_ver_id": pro_ver_id}
                server_req_agent.post_request("/aibot/timeout_task", msg, extra_header, "0", False)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_CMD_ENDCALL":
                logger.info(f'recv call end signal! Sending waiting reports, data:{data}')
                msg = {"uid":uid, "pro_ver_id": pro_ver_id}
                server_req_agent.post_request("/aibot/disturb_task", msg,extra_header, "0", False)
                clear_context_global(uid, global_conversation_id, coords, "")
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_AUDIO_FINISHED":
                logger.info(f'recv play finish signal! sending waiting reports, data:{data}')
                msg = {"uid": uid, "intent_id": intent_id, "req_id": old_req_id, "pro_ver_id": pro_ver_id}
                server_req_agent.post_request("/aibot/finish_msg", msg, extra_header, "0", False)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_CARD_FINISHED":
                pass
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_VOICE_START":
                logger.info(f'recv voice start signal! sending waiting reports, data:{data}')
                msg = {"uid":uid, "pro_ver_id": pro_ver_id, "coords": coords, "token": token}
                server_req_agent.post_request("/aibot/mic_open",msg, extra_header, "0", False)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_LISTEN_START":
                logger.info(f'recv listen start signal! sending waiting reports, data:{data}')
                msg =  {"uid":uid, "pro_ver_id": pro_ver_id}
                server_req_agent.post_request("/aibot/talk_start", msg, extra_header, "0", False)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_PHONE_START":
                logger.info(f'recv phone start signal! sending prologue, data:{data}')

                if global_setting_dict["interaction_type"] in {"multi_round"}:
                    executor.submit(new_message_async, req_id, uid, coords, "PROLOGUE", extra_header, pro_ver_id, token)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_CLIENT_WAKE":
                logger.info(f'recv phone wake signal! sending push, data:{data}')

                msg = {
                    "uid": uid,
                    "req_id": req_id,
                    "global_conversation_id": global_conversation_id,
                    "client_status": data,
                    "intent_id": intent_id,
                    "pro_ver_id": pro_ver_id
                }
                server_req_agent.post_request("/aibot/send_push", msg, extra_header, "0", False)
            elif message_type_dict[msg.message_type] == "MESSAGE_TYPE_CLIENT_BREAK":
                logger.info(f'recv client break signal! cancel task, data:{data}')

                msg = {"uid":uid, "pro_ver_id": pro_ver_id}
                server_req_agent.post_request("/aibot/disturb_task", msg, extra_header, "0", False)

        logger.info(f"/chat/rec_app return!")
        return jsonify({'status': 'received', 'code': 0, 'message': req_id})
