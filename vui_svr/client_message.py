import json
import structlog

AUDIO_FINISHED= 1001
CARD_FINISHED = 1002

MESSAGE_MODAL_VOICE = 1
MESSAGE_MODAL_INFO = 2
MESSAGE_MODAL_CMD = 3

message_type_dict = {
    11: "MESSAGE_TYPE_CMD_ENDCALL",
    12: "MESSAGE_TYPE_CMD_TIMEOUTREQ",
    101: "MESSAGE_TYPE_TEXT",
    102: "MESSAGE_TYPE_STREAM",
    201: "MESSAGE_TYPE_TPL_CARD",
    202: "MESSAGE_TYPE_TPL_CARDLIST",
    1001: "MESSAGE_TYPE_AUDIO_FINISHED",
    1002: "MESSAGE_TYPE_CARD_FINISHED",
    1003: "MESSAGE_TYPE_VOICE_START",
    1004: "MESSAGE_TYPE_VOICE_STOP",
    1006: "MESSAGE_TYPE_LISTEN_START",
    1007: "MESSAGE_TYPE_LISTEN_STOP",
    1008: "MESSAGE_TYPE_PHONE_START",
    1010: "MESSAGE_TYPE_CLIENT_BREAK",
    1011: "MESSAGE_TYPE_CLIENT_WAKE"
}

logger = structlog.get_logger(__name__)

class ClientMessage():
    def __init__(self, uid = "", coords = "", message_type = 0):
        self.uid = uid
        self.message_type = message_type
        self.message_modal = self.get_modal_from_type(message_type)
        self.context = {"uid": uid, "coords": coords}
        self.talk_cmd_code = 0
        self.mic_cmd_code = 1
        self.message = {}
        self.conversation = None

    def set_context(self, context):
        if "uid" in context and "coords" in context:
            self.context = context
        else:
            print(f'No uid or coords in context: {context}')
            return -1
        return 0

    def set_vocal_message(self, message):
        ret_code = 0
        if self.message_modal == MESSAGE_MODAL_VOICE:
            if "data"in message and type(message["data"]) == str:
                self.message = message
            else:
                print(f'No data or wrong data in message: {message}')
                ret_code = -1
        else:
            print(f'Wrong message modal: {self.message_modal}, message: {message}')
            ret_code = -1
        return 0

    def set_cmd(self, talk_cmd_code, mic_cmd_code):
        self.talk_cmd_code = talk_cmd_code
        self.mic_cmd_code = mic_cmd_code

    def set_conversation(self, conversation):
        self.conversation = conversation

    def add_tpl_message(self, title, content, url, action):
        ret_code = 0
        if self.message_modal == MESSAGE_MODAL_INFO:
            if message_type_dict[self.message_type] == "MESSAGE_TYPE_TPL_CARD":
                #print(f'title:{title}, type:{type(title)}, content:{content}, type:{type(content)}, url:{url}, type:{type(url)}')
                if action == "" or len(action) <= 0:
                    self.message = {"type": self.message_type, "data": {"title": title, "content": content, "pic_url": url}, "conversaton": self.conversation, "action": { "mic_state": self.mic_cmd_code, "conversation_state": self.talk_cmd_code }}
                else:
                    self.message = {"type": self.message_type, "data": {"title": title, "content": content, "pic_url": url, "action": action}, "conversation": self.conversation, "action": { "mic_state": self.mic_cmd_code, "conversation_state": self.talk_cmd_code }}
                #print(f'message:{self.message}, type:{type(self.message)}')
            elif message_type_dict[self.message_type] == "MESSAGE_TYPE_TPL_CARDLIST":
                if not "type" in self.message or not "data" in self.message:
                    self.message = {"type": self.message_type, "data": {}, "action": { "mic_state": self.mic_cmd_code, "conversation_state": self.talk_cmd_code }}
                if action == "" or len(action) <= 0:
                    self.message["data"].append({"title": title, "content": content, "pic_url": url})
                else:
                    self.message["data"].append({"title": title, "content": content, "pic_url": url, "action": action})
            else:
                print(f'Unknown info message type: {self.message_type}')
                ret_code = -1
        else:
            print(f'Mismatch info message type, can not add tpl into modal: {self.message_modal}, must be info')
            ret_code = -1

        return ret_code

    @staticmethod
    def get_modal_from_type(message_type):
        message_modal = 0
        id_thousand = int(message_type/1000)
        if message_type == 101 or message_type == 102:
            message_modal = MESSAGE_MODAL_VOICE
        if message_type == 201 or message_type == 202:
            message_modal = MESSAGE_MODAL_INFO
        if 1 <= id_thousand < 10:
            message_modal = MESSAGE_MODAL_CMD
        if 1 <= message_type < 100:
            message_modal = MESSAGE_MODAL_CMD
        return message_modal

    def dump(self):
        content = {"context": self.context, "message": self.message}
        ret_str = json.dumps(content, ensure_ascii=False)
        return ret_str

    def loads(self, input_data):
        content = input_data
        logger.debug(f'data_content:{content}, type:{type(content)}')
        if type(content) == str:
            content = json.loads(content)
        if type(content) == str:
            content = json.loads(content)

        ret_code = 0
        if "context" in content:
            #if "uid" in content["context"] and "coords" in content["context"]:
            if "coords" in content["context"]:
                self.uid = ""
                self.context = content["context"]
                self.context["coords"] = f"{self.context['coords'].get('longitude', '')}, {self.context['coords'].get('latitude', '')}"
            else:
                print(f'Wrong app message format, no uid or coords in context')
                ret_code = -1
        else:
            print(f'Wrong app message format, no context')
            ret_code = -1

        if "message" in content:
            self.message = content["message"]
            if "type" in self.message:
                if self.message["type"] in message_type_dict:
                    self.message_type = self.message["type"]
                    self.message_modal = self.get_modal_from_type(self.message_type)
            else:
                print(f'Wrong app message format, no type in message')
                ret_code = -1
        else:
            print(f'Wrong app message format, no message')
            ret_code = -1

        return ret_code
