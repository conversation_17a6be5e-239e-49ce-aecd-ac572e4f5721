# coding=utf-8

import uuid
import redis
import time
import json
import sys
import structlog

from .redis_lock import RedisLock

logger = structlog.get_logger(__name__)

class InfoAgent:
    def __init__(self, global_setting_dict):
        self.db_client = redis.StrictRedis(host=global_setting_dict["redis_ip"], port=global_setting_dict["redis_port"], username=global_setting_dict["redis_user"], password=global_setting_dict["redis_pass"])
        self.task_expire_time = 300
        self.inst_expire_time = 864000

#        test_msg = [{"msg": "test", "role": "user", "intent_id": "10001", "scene_id": "1", "req_id": "1"}]
#        test_msg = json.dumps(test_msg, ensure_ascii=False)
#        self.set_profile_item("ai_cloud1", "10001_hist_chat", test_msg)
#        hist_chat = self.get_profile_item("ai_cloud1", "10001_hist_chat")

    def add_user_push(self, user_id, push_info):
        user_push_key = f"{user_id}_user_push"
        user_push_lock = RedisLock(self.db_client, user_push_key + "_lock")
        user_push_info = None
        if user_push_lock.acquire():
            try:
                user_push_info = self.db_client.get(user_push_key)
                push_list = json.loads(user_push_info) if user_push_info else []
                push_list.append(push_info)
                user_push_info = json.dumps(push_list, ensure_ascii=False)
                # Add 1800s expire time.
                self.db_client.set(user_push_key, user_push_info, ex=1800)
            finally:
                user_push_lock.release()
        else:
            logger.error(f'"[{req_id}][{user_id}][] error getting user_push lock.')

        return user_push_info

    def del_user_push(self, user_id):
        user_push_key = f"{user_id}_user_push"
        user_push_lock = RedisLock(self.db_client, user_push_key + "_lock")
        if user_push_lock.acquire():
            try:
                user_push_info = self.db_client.get(user_push_key)
                self.db_client.delete(user_push_key)
                push_list = json.loads(user_push_info) if user_push_info else []
            finally:
                user_push_lock.release()
        else:
            logger.error(f'"[{req_id}][{user_id}][] error getting user_push lock.')
            push_list = []

        return push_list

    def get_fg_intent(self, user_id, global_conversation_id):
        fg_intent_lock_key = f"{user_id}_{global_conversation_id}_fg_intent"
        fg_intent_lock = RedisLock(self.db_client, fg_intent_lock_key)
        fg_intent_id = None
        if fg_intent_lock.acquire():
            try:
                fg_intent_id = self.get_profile_item(user_id, f"{global_conversation_id}_fg_intent")
                if not fg_intent_id:
                    fg_intent_id = "0"
                    self.set_profile_item(user_id, f"{global_conversation_id}_fg_intent", fg_intent_id)
            finally:
                fg_intent_lock.release()
        else:
            logger.error(f'"[{req_id}][{user_id}][{global_conversation_id}] error when getting fgintent lock: {fg_intent_lock_key}')

        return fg_intent_id

    def set_fg_intent(self, user_id, global_conversation_id, fg_intent_id):
        fg_intent_lock_key = f"{user_id}_{global_conversation_id}_fg_intent"
        fg_intent_lock = RedisLock(self.db_client, fg_intent_lock_key)

        if fg_intent_lock.acquire():
            try:
                self.set_profile_item(user_id, f"{global_conversation_id}_fg_intent", fg_intent_id)
            finally:
                fg_intent_lock.release()
        else:
            logger.error(f'"[{req_id}][{user_id}][{global_conversation_id}] error when getting fgintent lock: {fg_intent_lock_key}')

        return fg_intent_id

    def get_intent_status(self, user_id: str, global_conversation_id: str):
        intent_status = self.get_profile_item(user_id, f"{global_conversation_id}_status_intent")
        return json.loads(intent_status) if intent_status else {}

    def set_intent_status(self, user_id, global_conversation_id, add_intent_list, del_intent_list, set_intent_status):
        intent_status_lock_key = f"{user_id}_{global_conversation_id}_status_intent"
        intent_status_lock = RedisLock(self.db_client, intent_status_lock_key)

        if intent_status_lock.acquire():
            try:
                intent_status = self.get_intent_status(user_id, global_conversation_id)
                for intent_id in add_intent_list:
                    if intent_id and intent_id != "0" and intent_id not in intent_status:
                        intent_status[intent_id] = {"in_stack": 0, "is_fg": 0, "source": "task"}

                for intent_id, intent_item in set_intent_status.items():
                    if intent_id and intent_id != "0" and intent_id in intent_status:
                        for param_key, param_val in intent_item.items():
                            intent_status[intent_id][param_key] = param_val
                
                for intent_id in del_intent_list:
                    if intent_id in intent_status:
                        del intent_status[intent_id]

                intent_status_str = json.dumps(intent_status, ensure_ascii=False)
                self.set_profile_item(user_id, f"{global_conversation_id}_status_intent", intent_status_str)
            finally:
                intent_status_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting status intent lock: ' \
                    f'{intent_status_lock_key}')

        return intent_status

    def has_global_conversation_id(self, user_id):
        global_conversation_id = self.get_profile_item(user_id, f"0_conversationid")
        return global_conversation_id

    def get_global_conversation_id(self, user_id):                  # 全局唯一
        conversationid_lock_key = f"{user_id}_0_conversationid"
        conversationid_lock = RedisLock(self.db_client, conversationid_lock_key)
        global_conversation_id = None
        ts = int(time.time()*1000)
        if conversationid_lock.acquire():
            try:
                global_conversation_id = self.get_profile_item(user_id, f"0_conversationid")
                if not global_conversation_id:
                    global_conversation_id = f"{uuid.uuid4()}_{ts}"
                self.set_profile_item(user_id, f"0_conversationid", global_conversation_id)
            finally:
                conversationid_lock.release()
        else:
            logger.error(f'"[{req_id}][{user_id}][] error when getting report lock: {conversationid_lock_key}')

        return global_conversation_id

    def get_vars(self, user_id, global_conversation_id, intent_id):
        varinfo = self.get_profile_item(user_id, f'{global_conversation_id}_{intent_id}_vars')
        if varinfo:
            varinfo = json.loads(varinfo)
        else:
            varinfo = {}
        return varinfo

    def set_vars(self, user_id, global_conversation_id, intent_id, var_info):
        var_info = json.dumps(var_info, ensure_ascii=False)
        self.set_profile_item(user_id, f'{global_conversation_id}_{intent_id}_vars', var_info)
        return 0

    def get_user_profile(self, user_id: str):
        profile_key = f'{user_id}_profile'
        profile_data = self.db_client.get(profile_key)
        self.db_client.expire(profile_key, self.task_expire_time)
        return json.loads(profile_data) if profile_data else {}

    def get_profile_item(self, user_id: str, key: str):
        profile_key = f'{user_id}_profile'
        profile_data = self.db_client.hget(profile_key, key)
        self.db_client.expire(profile_key, self.task_expire_time)
        return profile_data.decode('utf-8') if profile_data else ""

    def set_inst_profile_item(self, user_id, key, value):
        profile_key = f'{user_id}_inst_profile'
        self.db_client.hset(profile_key, key, value)
        self.db_client.expire(profile_key, self.inst_expire_time)
        return 0

    def get_inst_profile_item(self, user_id, key):
        profile_key = f'{user_id}_inst_profile'
        profile_str = self.db_client.hget(profile_key, key)
        self.db_client.expire(profile_key, self.inst_expire_time)
        if profile_str:
            profile_str = profile_str.decode('utf-8')
        return profile_str

    def set_profile_item(self, user_id, key, value):
        profile_key = f'{user_id}_profile'
        self.db_client.hset(profile_key, key, value)
        self.db_client.expire(profile_key, self.task_expire_time)
        return 0

    def del_profile_item(self, user_id, key):
        profile_key = f'{user_id}_profile'
        self.db_client.hdel(profile_key, key)
        return 0

    def del_user_profile(self, user_id):
        profile_key = f'{user_id}_profile'
        self.db_client.delete(profile_key)
        return 0

    def del_inst_user_profile(self, user_id):
        profile_key = f'{user_id}_inst_profile'
        self.db_client.delete(profile_key)
        return 0

    def set_hist_chat(self, req_id, user_id, global_conversation_id, intent_id, role, msg, real_intent_id, scene_id, hist_conversation_iter=5):
        hist_chat_list = []
        hist_msg = ""
        last_msg = ""
        hist_msg_list = []
        need_insert = True
        ts = time.time()

        histchat_lock_key = f"{user_id}_{global_conversation_id}_{intent_id}_histchat"
        histchat_lock = RedisLock(self.db_client, histchat_lock_key)
        if histchat_lock.acquire():
            try:
                hist_chat = self.get_profile_item(user_id, f"{global_conversation_id}_{intent_id}_hist_chat")
                if not hist_chat:
                    hist_msg = ""
                else:
                    hist_chat_list = json.loads(hist_chat)
                    for item_idx, item in enumerate(hist_chat_list):
                        #print(item)
                        role_name = "你"
                        if item["role"] == "user":
                            role_name = "用户"
                            if role == "user" and req_id == item["req_id"]:
                                hist_chat_list[item_idx]["scene_id"] = scene_id
                                hist_chat_list[item_idx]["intent_id"] = real_intent_id
                                need_insert = False
                        else:
                            last_msg = item["msg"]
                        hist_msg_list.append(f'{role_name}:{item["msg"]}')

                    if len(hist_msg_list) > hist_conversation_iter*2:
                        hist_msg_list = hist_msg_list[-hist_conversation_iter*2:]
                    hist_msg = "; ".join(hist_msg_list)

                # update chat history
                if need_insert:
                    hist_chat_list.append({
                        "msg":msg,
                        "role":role,
                        "type":"text",
                        "intent_id":real_intent_id,
                        "scene_id":scene_id,
                        "req_id": req_id, "time":ts
                    })
                if len(hist_chat_list) > 2*hist_conversation_iter:
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] cut hist chat length, reallen: '
                                  f'{len(hist_chat_list)}, limit:{2*hist_conversation_iter}')
                    hist_chat_list = hist_chat_list[-2*hist_conversation_iter:]
                hist_chat = json.dumps(hist_chat_list, ensure_ascii=False)
                #print(f"before set hist chat to profile, key:{intent_id}_hist_chat, hist_chat:{hist_chat}")
                self.set_profile_item(user_id, f"{global_conversation_id}_{intent_id}_hist_chat", hist_chat)
            finally:
                histchat_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting histchat lock: {histchat_lock_key}')


        # simplify hist_chat
        simp_hist_chat = [{"msg":item["msg"], "role":item["role"], "intent_id":item["intent_id"], "scene_id":item["scene_id"], "req_id": item["req_id"]} for item in hist_chat_list]
        if len(simp_hist_chat) > 2*hist_conversation_iter:
            simp_hist_chat = simp_hist_chat[-2*hist_conversation_iter:]

        return simp_hist_chat, hist_msg, last_msg

    def del_hist_chat(self, user_id, global_conversation_id, intent_id):
        self.del_profile_item(user_id, f"{global_conversation_id}_{intent_id}_hist_chat")
        #self.db_client.delete(f'{user_id}_{intent_id}_histchat')
        return 0

    def get_hist_chat(self, user_id, global_conversation_id, intent_id):
        hist_chat = self.get_profile_item(user_id, f"{global_conversation_id}_{intent_id}_hist_chat")
        hist_msg = ""
        last_msg = ""
        hist_msg_list = []

        if hist_chat is None or not hist_chat:
            hist_msg = ""
        else:
            hist_chat_list = json.loads(hist_chat)
            for item in hist_chat_list:
                role = "你"
                if item["role"] == "user": role = "用户"
                hist_msg_list.append(f'{role}:{item["msg"]}')
                last_msg = item["msg"]
            hist_msg = "; ".join(hist_msg_list)

        simp_hist_chat = []
        if hist_chat:
            simp_hist_chat = [{"msg":item["msg"], "role":item["role"], "intent_id":item["intent_id"], "scene_id":item["scene_id"], "req_id": item["req_id"]} for item in hist_chat_list]
        return simp_hist_chat, hist_msg, last_msg

    def clear_global_context(self, user_id, global_conversation_id, intent_info):
        self.del_user_profile(user_id)
        self.del_inst_user_profile(user_id)
        logger.info(f'[][{user_id}][{global_conversation_id}] All conversation_id, vars, taskid, chathist, profile & locks cleared')
        return 0

    def clear_intent_context(self, user_id, global_conversation_id, intent_id):
        logger.debug(f"[][{user_id}][{global_conversation_id}]Clearing conversation_id, vars, histchat, profiles for user:{user_id}, intent:{intent_id}")
        # update fg_intent
        old_fg_intent = self.get_profile_item(user_id, f"{global_conversation_id}_fg_intent")
        self.del_profile_item(user_id, f"{global_conversation_id}_fg_intent")
        new_fg_intent = self.get_profile_item(user_id, f"{global_conversation_id}_fg_intent")

        # update chat conversation_id
        old_conversation_id = self.get_profile_item(user_id, f"{intent_id}_conversationid")
        self.del_profile_item(user_id, f"{intent_id}_conversationid")
        new_conversation_id = self.get_profile_item(user_id, f"{intent_id}_conversationid")
        #logger.debug(f"old conversationid:{old_conversation_id}, new conversationid:{new_conversation_id}")

        # update vars
        old_vars = self.get_profile_item(user_id, f"{global_conversation_id}_{intent_id}_vars")
        self.del_profile_item(user_id, f"{global_conversation_id}_{intent_id}_vars")
        new_vars = self.get_profile_item(user_id, f"{global_conversation_id}_{intent_id}_vars")
        #logger.debug(f"old vars:{old_vars}, new vars:{new_vars}")

        # update chat hist
        old_hist_chat, old_hist_msg, old_last_msg = self.get_hist_chat(user_id, global_conversation_id, intent_id)
        self.del_hist_chat(user_id, global_conversation_id, intent_id)
        new_hist_chat, new_hist_msg, new_last_msg = self.get_hist_chat(user_id, global_conversation_id, intent_id)
        #logger.debug(f"old chat list: {old_hist_chat}, new chat list: {new_hist_chat}")

        # update intent_status
        self.set_intent_status(user_id, global_conversation_id, [], [intent_id], {})

        # update profile
        old_user_profile = self.get_inst_profile_item(user_id, f"{intent_id}_profile")
        if intent_id == "10005":
            profile_list = []
            #profile_list.append({"featureName": "name_pair",  "value":"家###南露园1号"})
            #profile_list.append({"featureName": "name_pair",  "value":"公司###康得大厦"})
            #profile_list.append({"featureName": "car_type",  "value":"快车###特惠快车###专车"})
            user_profile = json.dumps(profile_list, ensure_ascii=False)
            self.set_inst_profile_item(user_id, "10005_profile", user_profile)
            new_user_profile = self.get_inst_profile_item(user_id, f"{intent_id}_profile")

        if intent_id == "10002":
            profile_list = []
            #profile_list.append({"featureName": "order",  "value":"瑞幸###茉莉花拿铁"})
            #profile_list.append({"featureName": "order",  "value":"奈雪的茶###拿铁"})
            #profile_list.append({"featureName": "order",  "value":"肯德基###老北京鸡肉卷"})
            user_profile = json.dumps(profile_list, ensure_ascii=False)
            self.set_inst_profile_item(user_id, "10002_profile", user_profile)
            new_user_profile = self.get_inst_profile_item(user_id, f"{intent_id}_profile")
        #logger.debug(f"old_user_profile:{old_user_profile}, new_user_profile:{new_user_profile}")

if __name__ == '__main__':
    info_agent = InfoAgent(ip = "127.0.0.1", port = 16379, user = "default", pwd = "Spotlight321@")
    task_msg = " 附近有肯德基吗？"
    user_id = sys.argv[1] #"test1"
    #user_id = "('**************', 63381)"
    #user_id = "24542782532"
    req_id = "23dshfskjdf"
    conversation_id = "2342343"
    ts = time.time()
    
    #task_list = [{"msg":task_msg, "intent_id":intent_id, "status":"running", "req_id":req_id, "conversation_id":conversation_id, "time":ts}]
    #self.set_task_list(user_id, intent_id, task_list)

    # update conversation_id & chat hist & profile
    info_agent.clear_global_context(user_id, {}, "")

    print("All profile printed")
