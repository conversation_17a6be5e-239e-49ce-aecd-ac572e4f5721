import random
import structlog
import json
import uuid
from urllib.parse import urljoin

import redis
import requests

from concurrent.futures import ThreadPoolExecutor

from vui_svr.agents import (
    info_agent,
    memo_agent,
    task_agent,
    server_req_agent,
    report_agent,
    status_agent,
    client_req_agent,
)

logger = structlog.get_logger(__name__)

# from timer_svr import TimerAgent
from vui_svr.intent_module import intent_bot, intent_info, action_bots
from vui_svr.user_timer import UserTimerScheduler

from vui_svr.settings import global_setting_dict

# load agent style setting
with open(global_setting_dict["role_config_file"], 'r', encoding='utf-8') as file:
    role_config_dict = json.load(file)
for key in role_config_dict.keys():
    role_config_dict[key]["id"] = key
role_dict = {
    "default": role_config_dict[global_setting_dict["agent_role"]],
    "host": role_config_dict[global_setting_dict["host_role"]],
    "guest": role_config_dict[global_setting_dict["expert_role"]]
}

# set running params
executor = ThreadPoolExecutor(global_setting_dict["TASK_THREAD_CNT"])
MAX_HIST_CONVERSATION_ITER = global_setting_dict["MAX_HIST_CONVERSATION_ITER"]

user_timer = UserTimerScheduler(server_req_agent)

intent_bot.set_memo_engine(memo_agent)
intent_bot.set_task_agent(info_agent, task_agent)

# redis client
redis_client = redis.StrictRedis(host = global_setting_dict["redis_ip"], port = global_setting_dict["redis_port"], username = global_setting_dict["redis_user"], password = global_setting_dict["redis_pass"])


def new_message_async(req_id, uid, coords, msg, header_info, pro_ver_id, token, audio_blob_id = ""):
    ret = -1
    global_conversation_id = info_agent.get_global_conversation_id(uid)
    if global_setting_dict["interaction_type"] == "single_round":
        clear_context_global(uid, global_conversation_id, coords, req_id)

    running_task_list = task_agent.get_task_list(uid, global_conversation_id)
    logger.info(f'[{req_id}][{uid}][{global_conversation_id}] request start, reqid: {req_id}, coords: {coords}, ' \
            f'msg: {msg}, running_task_list:{running_task_list}')
    msg_dict =  {
        "msg":msg,
        "uid":uid,
        "coords": coords,
        "pro_ver_id": pro_ver_id,
        "req_id":req_id,
        "token": token,
        "global_conversation_id": global_conversation_id,
        "audio_blob_id": audio_blob_id,
    }
    resp = server_req_agent.post_request("/aibot/new_msg", msg_dict, extra_headers=header_info, pro_ver_id="0")
    if resp:
        ret = 0
        logger.info(f'[{req_id}][{uid}][{global_conversation_id}] request finish, reqid: {req_id}, coords: {coords}, ' \
                f'msg: {msg}, req_type:{resp["code"]}, req_info:{resp["info"]}, run_details: {resp["detail_info"]}')

    return ret


# DEBUG - 处理WEB页面的输入消息
def clear_context_global(user_id, global_conversation_id, coords, req_id = ""):
    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] Clear all context')
    # clear phone server cache
    params = {"context": f"{{\"req_id\": \"{req_id}\", \"uid\": \"{user_id}\", \"intent_id\": \"\", \"conversation_id\": " \
                f"\"{global_conversation_id}\", \"coords\": \"{coords}\", \"app_id\": \"\"}}"}
    #phone_req_agent.post_request("agent-api/agent_interactive/finishSession", params)

    # clear user_timer
    user_timer.stop_all()

    # clear local cache
    for key, val in intent_info.items():
        clear_context("0", user_id, global_conversation_id, key, coords)

    info_agent.clear_global_context(user_id, global_conversation_id, intent_info)
    return 0


def clear_context(req_id, user_id, global_conversation_id, intent_id, coords):
    logger.debug(f'[{req_id}][{req_id}][{global_conversation_id}] clear all context, uid:{user_id}, intentid:{intent_id}')

    # 总结意图对应的记忆信息
    intent_vars = info_agent.get_vars(user_id, global_conversation_id, "0")
    intent_hist_chat, intent_hist_msg, intent_last_msg = memo_agent.get_hist_chat(req_id, user_id, global_conversation_id, intent_id)
    # logger.debug(f'[{req_id}][{req_id}][{global_conversation_id}] before extracting, intent_hist_chat:{intent_hist_chat}')
    if intent_hist_msg:
        # executor.submit(memo_agent.extract_portrait, req_id, user_id, global_conversation_id, intent_id, coords,
        #     intent_vars, intent_hist_msg)                     # 后台执行对话任务解析
        memo_agent.extract_portrait(req_id, user_id, global_conversation_id, intent_id, coords, intent_vars, intent_hist_msg)

    # 清空intent对应的任务信息
    task_agent.clear_intent_task(user_id, global_conversation_id, intent_id)
    task_agent.clear_intent_task(user_id, global_conversation_id, "0")
    report_agent.clear_intent_report(user_id, global_conversation_id, intent_id)
    report_agent.clear_intent_report(user_id, global_conversation_id, "0")
    # appid_agent.clear_intent_appid(user_id, global_conversation_id, intent_id)
    # appid_agent.clear_intent_appid(user_id, global_conversation_id, "0")
    # redis_client.delete(f"{user_id}_0_vars")
    return 0


def send_msg(req_id, coords, user_id, intent_id, global_conversation_id, fg_intent, extra_header, pro_ver_id):
    # status_info = status_agent.get_status(user_id, global_conversation_id)
    # if status_info == "on":
    #     return 0 # 说话状态不能回复

    ret, last_report = 0, None
    while True:
        next_report, waiting_report_list = report_agent.pop_report(req_id, user_id, global_conversation_id, intent_id, fg_intent)
        # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] user not talking, fetching next report, '
        #               f'next_report:{next_report}, waiting_report_list:{waiting_report_list}')

        if not next_report:
            logger.debug("no next report")
            break

        status_info = status_agent.get_status(user_id, global_conversation_id)
        if status_info == "on" and next_report.get("with_ack", True):
            break

        if last_report and next_report["req_id"] == last_report["req_id"] and next_report["ret_output"] == last_report["ret_output"]:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] pop redundant report, error')
            break

        report_role_info = role_dict.get(next_report["role"]) or role_dict["default"]
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] message send start, msg:{next_report}, '
                     f'role:{report_role_info["name"]}, waiting report: {waiting_report_list}')

        # user_timer.pause(user_id)
        with_ack = next_report.get("with_ack", True)  # by default, we have ack.
        audio_blob_id = str(uuid.uuid4()) if with_ack else ""

        ret_output = next_report["ret_output"]
        save_output = next_report["ret_output"]
        if ret_output.startswith("*#*#4636#*#*"):
            save_output = ret_output[12:]
            ret_output = " "

        ret_info = client_req_agent.send_message_to_client(
            next_report["req_id"], user_id, intent_id, "",
            ret_output, next_report["ret_detail_info"], next_report["ret_status"], report_role_info,
            next_report["talk_cmd"], next_report["mic_cmd"], extra_header, pro_ver_id,
            with_ack=with_ack, audio_blob_id=audio_blob_id,
        )
        user_timer.restart(user_id)

        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] message send end, ret_info:{ret_info}')

        if with_ack:
            _ = memo_agent.set_hist_chat(
                req_id, user_id, global_conversation_id, intent_id, "bot",
                save_output, intent_id, next_report["scene_id"],
                next_report["scene_sub_id"], next_report["mic_cmd"],
                next_report["talk_cmd"], next_report["report_type"]
            )

            # if next_report["ret_status"] == 0 or next_report["ret_status"] == "0":
        if next_report["scene_id"] == 0:
            if global_setting_dict["interaction_type"] == "simple_round":
                clear_context_global(user_id, global_conversation_id, next_report["coords"], req_id)
            else:
                clear_context(req_id, user_id, global_conversation_id, intent_id, next_report["coords"])

            bye_msg = " "
            if global_setting_dict["NEED_BYEBYE_TEXT"]:
                bye_msg_list = report_role_info["script"]["bye_text"]
                bye_msg = bye_msg_list[random.randint(0, len(bye_msg_list) - 1)]
            report_agent.push_report(
                req_id, coords, user_id, global_conversation_id, intent_id, "-1", "-1", bye_msg, "", 0,
                next_report["conversation_id"], global_conversation_id, "off", "off", "task", "must", -1
            )
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] context cleared for intent:{intent_id}')

        if global_setting_dict["run_mode"] == "DEBUG":
            report_agent.finish_req_report(next_report["req_id"], user_id, global_conversation_id, next_report["intent_id"])
        if next_report["talk_cmd"] == "off":
            clear_context_global(user_id, global_conversation_id, next_report["coords"], req_id)
            # if global_setting_dict["interaction_type"] != "simple_round":
            #     clear_context_global(user_id, global_conversation_id, next_report["coords"], req_id)
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] conversation end')
            break

        # record current report as last one.
        last_report = next_report
        ret += 1

    return ret

def merge_details(src, dst, proc_name):
    if dst:
        src["time_details"].update(dst["time_details"])
        src["token_details"].update(dst["token_details"])
        src["prompt_token"] += dst["prompt_token"]
        src["output_token"] += dst["output_token"]
        src["time_details"][proc_name] = dst["time_cost"]

    return src

# simple agent service func wrapper for local func proxy.
def agent_wrapper(func):
    def wrapper(*args, **kwargs):
        body = {
            "func": func.__name__,
            "args": args,
            "kwargs": kwargs
        }

        url = urljoin(global_setting_dict["agent_svc_url"], "/agent_task/exec")
        resp = requests.post(url=url, json=body)
        resp.raise_for_status()
        return resp.json()["result"]
    return wrapper


@agent_wrapper
def build_task(*args, **kwargs):
    pass


@agent_wrapper
def do_act(*args, **kwargs):
    pass
