import math
import platform

if 'darwin' not in platform.system().lower():   # not macos
    import models.tr.tr as tr

import cv2
import easyocr
from PIL import Image, ImageDraw, ImageFont

def InitReader():
    easyocr_reader = easyocr.Reader(['ch_sim'])
    return easyocr_reader

def GetTxt(file_path, x, y, w, h, easyocr_reader = None):
    img_path = file_path

    if easyocr_reader is None:
        img_pil = Image.open(img_path)
        gray_pil = img_pil.convert("L")
        bbox = [x, y, x+w, y+h]
        region_of_interest = gray_pil.crop(bbox)

        results = tr.run(region_of_interest, flag=tr.FLAG_ROTATED_RECT)
        rets = []
        for i, rect in enumerate(results):
            cx, cy, cw, ch, a = tuple(rect[0])
            cx = cx + x
            cy = cy + y
            ret_item = {"val":rect[1], "pred":rect[2] , "x":cx, "y":cy, "w":cw, "h":ch}
            rets.append(ret_item)
    else:
        image = cv2.imread(img_path)
        region_of_interest = image[y:y+h, x:x+w]
        results = easyocr_reader.readtext(img_path)
        rets = []
        for i, item in enumerate(results):
            score = item[2]
            p1, p2, p3, p4 = item[0]
            cw = max(math.fabs(p2[0] - p1[0]), math.fabs(p3[0] - p1[0]), math.fabs(p2[0] - p4[0]), math.fabs(p3[0] - p4[0]))
            ch = max(math.fabs(p3[1] - p1[1]), math.fabs(p4[1] - p1[1]), math.fabs(p3[1] - p2[1]), math.fabs(p4[1] - p2[1]))
            text = item[1]
            cx = p1[0] + x
            cy = p1[1] + y
            ret_item = {"val": text, "pred": score, "x": cx, "y": cy, "w": cw, "h": ch}
            rets.append(ret_item)

    return rets
