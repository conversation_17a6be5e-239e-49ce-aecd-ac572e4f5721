from .text_matcher import text_matcher
from .ac_extractor import ac_extractor
import json
import re
import structlog
import copy

logger = structlog.getLogger(__name__)

class reg_matcher(text_matcher):
    def __init__(self):
        self.rule_list = []
        self.brace_dict = {"(": {"right": ")", "type":0, "origin": False}, "[": {"right": "]", "type": 1, "origin": False}, "<": {"right":">", "type":-1, "origin": True}}
                                                        
    def init_matcher(self, file_path, skip_words):
        file_list = self.get_files_in_directory(file_path)
        logger.debug(f'reg_matcher, filecnt:{len(file_list)}')
        
        for file_item in file_list:
            logger.debug(f'Loading reg rule file: {file_item}')
            with open(file_item, 'r', encoding='utf-8') as file:
                rule_data = json.load(file)
            self.rule_list.append(rule_data)

            # init param list
            if "param_list" in rule_data:
                normalized_param_type_dict = {}
                for var_key, var_val in rule_data["param_list"].items():               # norm var type list
                    new_var_key = var_key
#                   pieces = var_key.split(":")
#                   if len(pieces) == 1: new_var_key = f'{var_key}:1'
                    normalized_param_type_dict[new_var_key] = var_val
                self.rule_list[-1]["param_list"] = normalized_param_type_dict

            for scene_idx, scene_item in enumerate(rule_data["rules"]):
                if "param_default" in scene_item:
                    normalized_pre_var_dict = {}
                    for var_key, var_val in scene_item["param_default"].items():       # norm pre defined vars
                        new_var_key = var_key
#                        pieces = var_key.split(":")
#                        if len(pieces) == 1: new_var_key = f'{var_key}:1'
                        normalized_pre_var_dict[new_var_key] = var_val
                    self.rule_list[-1]["rules"][scene_idx]["param_default"] = normalized_pre_var_dict

                normalized_var_status_dict = {}
                for var_key, var_val in scene_item["var_status"].items():       # norm var status 
                    new_var_key = var_key
                    pieces = var_key.split(":")
                    if len(pieces) > 1: new_var_key = f'{pieces[0]}'
                    normalized_var_status_dict[new_var_key] = var_val
                self.rule_list[-1]["rules"][scene_idx]["var_status"] = normalized_var_status_dict

                #new_task_dict = {}
                #for new_task_idx, new_task_str in scene_item["new_task"].items():
                #    matches = re.findall("(<.*?>)", new_task_str)
                #    for match_item in matches:
                #        new_item = match_item
                #        pieces = match_item[1:-1].split(":")
                #        if len(pieces) == 1: new_item= f'<{pieces[0]}:{pieces[0]}:1>'
                #        if len(pieces) == 2: new_item= f'<{pieces[0]}:{pieces[1]}:1>'
                #        new_task_str = new_task_str.replace(match_item, new_item)
                #    new_task_dict[new_task_idx] = new_task_str
                #self.rule_list[-1]["rules"][scene_idx]["new_task"] = new_task_dict

                parse_rule_list = []
                for rule_str in scene_item["rules"]:                            # norm rule_str
                    parse_rule = self.parse_rule(rule_str)
                    parse_rule_list.append(parse_rule)
                self.rule_list[-1]["rules"][scene_idx]["rule_strs"] = scene_item["rules"]
                self.rule_list[-1]["rules"][scene_idx]["rules"] = parse_rule_list

            logger.debug(f'File loaded: {file_item}')

        self.omit_list = skip_words
        logger.debug(f'reg_matcher: reg index built')

        #for rule_item in self.rule_list:
        #    logger.debug(rule_item)
        return 0

    def parse_rule(self, rule_str):
        ret = []  # 用来存储最终的匹配规则
        end = 0
        rule_len = len(rule_str)
        wrong_format = False
       
        while end < rule_len and not wrong_format:
            start = end
            while end < rule_len and rule_str[end] not in self.brace_dict: end += 1
            target_str = rule_str[start:end]
            if target_str:
                target_str = target_str.lower()
                ret.append({"data": [target_str], "type": -1})

            start = end
            if start < rule_len and rule_str[start] in self.brace_dict:
                left_brace = rule_str[start]
                right_brace = self.brace_dict[rule_str[start]]["right"]
                brace_type = self.brace_dict[rule_str[start]]["type"]
                need_origin = self.brace_dict[rule_str[start]]["origin"]

                start = end + 1
                end = start
                while end < rule_len and rule_str[end] != right_brace: end += 1

                if rule_str[end] == right_brace:
                    target_str = rule_str[start:end]
                    if left_brace == "<":                   # norm parsed_rule, add default value
                        pieces = target_str.split(":")
                        if len(pieces) == 1:
                            target_str = f'{target_str}:1:0'
                        elif len(pieces) == 2:
                            target_str = f'{target_str}:0'
                        target_str = f'{left_brace}{target_str}{right_brace}'

                    sublist = target_str.split("|")
                    for sub_idx, subitem in enumerate(sublist):
                        if subitem[0] == "<" and subitem[-1] == ">":
                            new_subitem = subitem[1:-1]
                            pieces = subitem[1:-1].split(":")
                            if len(pieces) == 1: new_subitem = f'{pieces[0]}:1:0'
                            elif len(pieces) == 2: new_subitem = f'{pieces[0]}:{pieces[1]}:0'
                            sublist[sub_idx] = f'<{new_subitem}>'
                        else:
                            sublist[sub_idx] = sublist[sub_idx].lower()
                            
                    ret.append({"data": sublist, "type": brace_type})
                    end = end + 1
                else:
                    wrong_format = True

        return ret

    def reg_match(self, input_idx, input_str, rule_idx, rule_item, extract_dict, pre_param_dict, hit_path, debug):
        is_hit = False
        param_dict = pre_param_dict
        if debug:
            logger.debug(f'{hit_path}, {input_idx}/{len(input_str)}, {rule_idx}/{len(rule_item)}')
        if rule_idx <= len(rule_item) :
            if input_idx >= len(input_str) and rule_idx >= len(rule_item): is_hit = True

            if not is_hit:
                for word in self.omit_list:                  # try omit words
                    if input_str.startswith(word, input_idx):
                        hit_path.append({"word": word, "rule": "omit"})
                        is_hit, param_dict = self.reg_match(input_idx + len(word), input_str, rule_idx, rule_item, extract_dict, param_dict, hit_path, debug)
                        if is_hit: break
                        hit_path.pop()

            if not is_hit and rule_idx < len(rule_item) and rule_item[rule_idx]["type"] == 0:             # is (), skippable
                is_hit, param_dict = self.reg_match(input_idx, input_str, rule_idx + 1, rule_item, extract_dict, param_dict, hit_path, debug)

            if not is_hit and rule_idx < len(rule_item) and rule_item[rule_idx]["type"] in {0, 1, -1}:    # is () or []  or simple text, test match
                for word in rule_item[rule_idx]["data"]:
                    if input_str.startswith(word, input_idx):                   # is text
                        #logger.debug(f'{rule_idx}, {input_idx}, {word}, {rule_item[rule_idx]}')
                        hit_path.append({"word": word, "rule": "text"})
                        is_hit, param_dict = self.reg_match(input_idx + len(word), input_str, rule_idx + 1, rule_item, extract_dict, param_dict, hit_path, debug)
                        if is_hit: break
                        hit_path.pop()
                    elif word[0] == "<" and word[-1] == ">":                    # is entity
                        extract_key =  word[1:-1]
                        pieces = extract_key.split(":")
                        extract_type = pieces[0]
                        extract_mode = int(pieces[2])
                        extract_idx = int(pieces[1])
                        new_extract_key = f'{extract_type}:{extract_idx}'
                        new_extract_key_name = f'{extract_type}'

                        if extract_type in extract_dict:
                            for extract_val in extract_dict[extract_type]:
                                if input_idx >= extract_val["start"] and input_idx <= extract_val["end"]:
                                    target_str = extract_val["wordB"] #input_str[extract_val["start"]:extract_val["end"]+1]

                                    hit_path.append({"word": word, "rule": "entity"})
                                    if f'{new_extract_key_name}' not in param_dict:  param_dict[f'{new_extract_key_name}'] = []
                                    param_dict[f'{new_extract_key_name}'].append(target_str)

                                    # 默认允许提取多个
                                    if extract_mode == 1:                # multi, test multi times
                                        is_hit, param_dict = self.reg_match(extract_val["end"] + 1, input_str, rule_idx, rule_item, 
                                                extract_dict, param_dict, hit_path, debug)
                                        if is_hit: break

                                    # 尝试提取一个
                                    is_hit, param_dict = self.reg_match(extract_val["end"] + 1, input_str, rule_idx+1, rule_item, 
                                            extract_dict, param_dict, hit_path, debug)
                                    if is_hit: break

                                    hit_path.pop()
                                    if len(param_dict[f'{new_extract_key_name}']) > 1: param_dict[f'{new_extract_key_name}'].pop()
                                    else:
                                        del param_dict[f'{new_extract_key_name}']

        return is_hit, param_dict
    
    def match(self, input_str, extract_dict, exist_var_dict, debug=False):
        logger.debug(f'reg_matcher: match start, input:{input_str}, exist_var_dict:{exist_var_dict}')
        result = {}
        for rule_data in self.rule_list:
            intent_id = rule_data['intent_id']
            param_type_dict = rule_data.get('param_list', None)
            var_list = rule_data['var_list']
            #logger.debug(f'reg_matcher, stage 1, testing config intent: {intent_id}')

            for sector_id, rule_item in enumerate(rule_data['rules']):
                old_scene_id = rule_item["old_scene_id"]
                param_default_dict = rule_item.get("param_default", None)
                fast_ret = rule_item["fast_ret"]
                new_task = rule_item["new_task"]
                var_status = rule_item["var_status"]
                pre_var_dict = {}
                if intent_id in exist_var_dict:
                    pre_var_dict = exist_var_dict[intent_id]
                    if "flow_param" in pre_var_dict:
                        for param_key, param_val in pre_var_dict["flow_param"].items():
                            pre_var_dict[param_key] = param_val
                is_hit = False
                # logger.debug(f'reg_matcher, stage 2, testing scene:{scene_id}, desc:{rule_item["desc"]}')

                hit_path = []
                for rule_idx, rule_parsed in enumerate(rule_item["rules"]):
                    # if debug:
                    #     logger.debug(f'reg_matcher, stage 3, testing sector:{sector_id}, ruleid:{rule_idx}, scene:{scene_id}, rule_str:{rule_item["rule_strs"][rule_idx]}, task:{new_task}, pre_var_dict:{pre_var_dict}')
                    is_hit, hit_var = self.reg_match(0, input_str, 0, rule_parsed, extract_dict, {}, hit_path, debug)

                    if is_hit:
                        new_task_dict = {}
                        out_param = {}
                        out_var = {}

                        # if pre_var_dict:                    # init params
                        #     out_var = copy.deepcopy(pre_var_dict)

                        if param_type_dict and hit_var:     # 从对话中抽取信息
                            # 归并params
                            # logger.debug(f'hit_var: {hit_var}, param_type:{param_type_dict}')
                            for param_name, param_val in param_type_dict.items():
                                if param_name in hit_var:
                                    if param_val == "str":
                                        if type(hit_var[param_name]) == str:
                                            out_param[param_name] = hit_var[param_name]
                                        elif type(hit_var[param_name]) == list:
                                            if hit_var[param_name]:
                                                out_param[param_name] = hit_var[param_name][0]
                                            else:
                                                out_param[param_name] = ""
                                    elif param_val == "list":
                                        if type(hit_var[param_name]) == list:
                                            out_param[param_name] = hit_var[param_name]
                                # else:
                                #     out_param[param_name] = param_val
                            # logger.debug(f'param_val:{out_param}')

                            # 归并vars
                            # logger.debug(f'var_list: {var_list}')
                            for var_name, var_pattern in var_list.items():
                                # logger.debug(f'var_name:{var_name}, var_pattern:{var_pattern}, pattern_type:{type(var_pattern)}')
                                if type(var_pattern) == str:          # param name
                                    if var_pattern in out_param:
                                        out_var[var_name] = out_param[var_pattern]
                                elif type(var_pattern) == list:
                                    # logger.debug(f'pattern len: {len(var_pattern)}')
                                    if var_pattern and len(var_pattern) == 1:           # list变量只支持str和dict
                                        var_val = var_pattern[0]
                                        # logger.debug(f'pattern item: {var_val}, item_type:{type(var_val)}')
                                        if type(var_val) == str:                # str: param名
                                            if var_val in out_param:                # push into lis
                                                if var_name not in out_var:
                                                    out_var[var_name] = [out_param[var_val]]
                                                else:
                                                    out_var[var_name].append(out_param[var_val])
                                        elif type(var_val) == dict:             # dict: 一堆param集合，只处理一层
                                            sub_var_dict = {}
                                            update_var_dict = {}
                                            has_key = True
                                            # logger.debug(f'prepare pattern item, var_val:{var_val}, ' \
                                            #         f'param_default_dict:{param_default_dict}')
                                            for sub_var_name, sub_var_val in var_val.items():
                                                if type(sub_var_val) == str:
                                                    sub_var_piceces = sub_var_val.split(":")
                                                    # logger.debug(f'var_name:{var_name}, var_pattern:{var_pattern}, ' \
                                                    #         f'sub_var_name:{sub_var_name}, sub_var_val:{sub_var_val}, ' \
                                                    #         f'sub_var_dict:{sub_var_dict}')
                                                    if len(sub_var_piceces) > 1 and sub_var_piceces[1] == "key":
                                                        if sub_var_piceces[0] in out_param and out_param[sub_var_piceces[0]]:
                                                            sub_var_dict[sub_var_name] = out_param[sub_var_piceces[0]]
                                                        else:
                                                           has_key = False
                                                    else:
                                                        if not sub_var_val:
                                                            sub_var_dict[sub_var_name] = sub_var_val
                                                        elif sub_var_val in out_param:
                                                            sub_var_dict[sub_var_name] = out_param[sub_var_val]
                                                        elif sub_var_val in param_default_dict:
                                                            sub_var_dict[sub_var_name] = param_default_dict[sub_var_val]
                                                        else:
                                                            has_key = False
                                                else:
                                                    if sub_var_name in out_param:
                                                        sub_var_dict[sub_var_name] = out_param[sub_var_name]
                                                    else:
                                                        sub_var_dict[sub_var_name] = sub_var_val

                                            if has_key:                        # push into list
                                                 if var_name not in out_var:
                                                     out_var[var_name] = [sub_var_dict]
                                                 else:
                                                     out_var[var_name].append(sub_var_dict)
                                    else:
                                        out_var[var_name] = [var_pattern]
                            # logger.debug(f'out_var: {out_var}')
                        else:
                            # 直接从历史透传变量中赋值相关参数
                            for var_name, var_pattern in var_list.items():
                                if type(var_pattern) == "str":          # param name
                                    if var_pattern in pre_var_dict:
                                        out_var[var_name] = pre_var_dict[var_pattern]

                        if pre_var_dict:                    # 补充未赋值变量
                            for pre_key, pre_val in pre_var_dict.items():
                                if pre_key not in out_var:
                                    out_var[pre_key] = pre_val

                        for var_name, status_rule in var_status.items():
                            if  status_rule == "NULL" and (var_name in pre_var_dict and pre_var_dict[var_name]):
                                is_hit = False
                            if status_rule == "NOTNULL" and (var_name not in pre_var_dict or not(pre_var_dict[var_name])):
                                is_hit = False

                        # add hit task
                        if is_hit:
                            # logger.debug(f"hit var status condition: {var_status}, exist_var:{pre_var_dict}, out_var:{out_var}")
                            #  枚举scene_id
                            for old_scene_sub_id, hit_new_task in new_task.items():
                                hit_scene_id = hit_new_task["scene_id"]
                                scene_id_key = f"{intent_id}_{old_scene_id}_{old_scene_sub_id}"
                                curr_fast_ret = ""
                                if old_scene_sub_id in fast_ret:
                                    curr_fast_ret = fast_ret[old_scene_sub_id][0]
                                elif "-1" in fast_ret:
                                    curr_fast_ret = fast_ret["-1"]

                                result[scene_id_key] = {"hit_rule": rule_item["rule_strs"][rule_idx], "intent_id":intent_id,
                                    "scene_desc": rule_item["desc"], "scene_id":hit_scene_id, "fast_ret": curr_fast_ret,
                                    "new_task": f"{hit_scene_id}", "hit_var": hit_var, "out_var": out_var}
                            break
                        # else:
                        #     logger.debug(f"miss var status condition: {var_status}, exist_var:{pre_var_dict}, out_var:{out_var}")

                if is_hit:
                    logger.debug(f'reg_matcher, HIT sector:{sector_id}, ruleid:{rule_idx}, result: {result}')
                else:
                    logger.debug(f'reg_matcher, MISS sector:{sector_id}, ruleid:{rule_idx}, desc:{rule_item["desc"]}')

        logger.debug(f'reg_matcher: match finished')
        return result


if __name__ == '__main__':
    omit_list = ["和", "或", "吗", "吧", "的", "了", "呢", "嗯", "啊", "呀", "哈", "，", "。", "um", "UM", "Um", "Em", "em", "ah", "AH", "Ah"]

    # init extractors
    ac_ext = ac_extractor()
    ac_ext.build_index("wordlist")
    test_mactcher = reg_matcher()
    test_mactcher.init_matcher("match_single", omit_list)

    # input_str = "帮我在附近的瑞幸点杯咖啡"
    # input_str = "帮我点杯抹茶拿铁和香草拿铁吧"
    # input_str = "附近有瑞幸或星巴克吗"
    # input_str = "帮我点一杯瑞幸的拿铁吧"
    # input_str = "好的，帮我支付吧"
    # input_str = "打车从康得去西直门"
    # input_str = "最近有啥新闻吗"
    input_str = "给我换成标准美式吧"
    ext_ret = ac_ext.extract(input_str)
    extract_dict = {}
    for ret_item in ext_ret:
        new_word = input_str[ret_item["start"]:ret_item["end"]+1]
        logger.debug(f'found new syn piece, start:{ret_item["start"]}, end:{ret_item["end"]}, type:{ret_item["type"]}')
        if ret_item["type"] in extract_dict:
            is_cover = False
            for exist_item in extract_dict[ret_item["type"]]:
                if ret_item["start"] <= exist_item["start"] and ret_item["end"] >= exist_item["end"]:
                    is_cover = True
                    break
            if not is_cover:
                extract_dict[ret_item["type"]].append(ret_item)
        else:
            extract_dict[ret_item["type"]] = [ret_item]
       
    logger.debug(extract_dict)
    match_ret = test_mactcher.match(input_str, extract_dict, {}, True)

    logger.debug(f"#######################, input:{input_str}")
    for ret_item in match_ret:
        logger.debug(f'hit, intent_id:{ret_item["intent_id"]}, scene_id:{ret_item["scene_id"]}, desc:{ret_item["scene_desc"]}, hit_rule: {ret_item["hit_rule"]}, fast_ret: {ret_item["fast_ret"]}, new_task: {ret_item["new_task"]}, hit_var: {ret_item["hit_var"]}, out_var: {ret_item["out_var"]}')
