import os

class text_extractor:
    def __init__(self):
        pass

    def get_files_in_directory(self, directory):
        file_paths = []
        if os.path.isdir(directory):
            for root, dirs, files in os.walk(directory):
                for file in files:
                    file_paths.append(os.path.join(root, file))
        else:
            file_paths.append(directory)

        return file_paths

    def build_index(self, file_path):
        return 0
                                                                                                                                                                                                        
    def extract(self, string):
        return {}
