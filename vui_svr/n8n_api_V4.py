import json
import time
import structlog

import requests
from urllib.parse import urljoin


logger = structlog.get_logger(__name__)


class RequestSession(requests.Session):
    def __init__(self, base_url=None):
        super().__init__()
        self.base_url = base_url

    def request(self, method, url, *args, **kwargs):
        joined_url = urljoin(self.base_url, url)
        return super().request(method, joined_url, *args, **kwargs)


class N8nAgent:
    def __init__(self, pat_auth, bot_id, bot_name, global_setting_dict):
        # self.pat_auth = f"Bearer {pat_auth}"
        self.bot_id = bot_id or "9bf8b1ae-36b8-4b01-9eb9-1c2b302a42d6"
        self.bot_name = bot_name
        self.wait_time = 1
        self.retry_cnt = 180

        self.session = RequestSession(base_url=global_setting_dict["n8n_addr"])

        self.default_headers = {'Content-Type': 'application/json'}
        if global_setting_dict.get("n8n_auth_key"):
            self.default_headers['Authorization'] = global_setting_dict['n8n_auth_key']

        logger.info(f"N8n config, bot_id:{bot_id}, bot_name:{bot_name}")

    def kill_chat(self, req_id, user_id, global_conversation_id, conversation_id, chat_id):
        pass

    def op_send_req(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, conversation_id = ""):
        pass

    def op_check_status(self, req_id, user_id, global_conversation_id, chat_id, conversation_id):
        pass

    def op_get_msg(self, req_id, user_id, global_conversation_id, chat_id, conversation_id, target = "answer"):
        pass

    def wait_chat_finish(self, req_id, user_id, global_conversation_id, conversation_id, chat_id, target, create_code):
        pass


    def chat(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, need_parse = True, conversation_id = "", embeding = False):
        start = time.time()
        code = 0
        err_msg = ""
        answer = ""
        model_name = ""
        prompt_token = 0
        output_token = 0

        content = {
            "bot_id": self.bot_id,
            "user_id": user_id,
            "stream": False,
            "auto_save_history": True,
            "additional_messages": [
                {
                    "role": "user",
                    "content": chat_msg,
                    "content_type": "text"
                }
            ],
            "input": chat_msg,
            "conversation_id": global_conversation_id,
            "req_id": req_id,
            "sessionId": global_conversation_id,
            "likes": {},
        }
        if custom_var and len(custom_var) > 0:
            content["custom_variables"] = custom_var
            if custom_var.get("hist_info"):
                try:
                    content["history_str"] = "\n".join([f"{c['role']}:{c['msg']}" for c in json.loads(custom_var["hist_info"])])
                except json.decoder.JSONDecodeError:
                    logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] history_str invalid json: {custom_var["hist_info"]}')
                    content["history_str"] = ""
        logger.debug(f'new req: {content}')

        res = self.session.post(f"/webhook/{self.bot_id}", headers=self.default_headers, json=content)
        if not res.ok or not res.text:
            code = -1
            err_msg = f"code: {res.status_code}, content: {res.text}"
        else:
            data = res.json()
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] send req, name:{self.bot_name}, resp:{data}, type:{type(data)}')
            # chat_id = data["data"]["id"]
            # conversation_id = data["data"]["conversation_id"]
            # TODO
            answer = data["output"]
        return code, err_msg, answer, model_name, prompt_token, output_token, time.time() - start

if __name__ == '__main__':
    from vui_svr.settings import global_setting_dict

    bot = N8nAgent(pat_auth="", bot_id="", bot_name="", global_setting_dict=global_setting_dict)
    r = bot.chat(
        req_id="111",
        user_id="222",
        global_conversation_id="333",
        chat_msg="hello",
    )
    print(r)
