import json
import time
import datetime
import logging
import sys

class DRAgent():
    def __init__(self, bot_id, bot_name):
        self.bot_id = bot_id
        self.bot_name = bot_name
        logging.info(f"GPT config, botid:{bot_id}, botname:{bot_name}")

        print(f'get msg: {answer}, type{type(answer)}, time:{int((end_time - start_time)*1000)/1000}s')
        return 0, answer, ""

    def op_new_conversation(self):
        pass

    def chat(self, chat_msg, custom_var = {}):
        code = 0
        answer = {}
        answer["new_task"] = chat_msg
        answer["discard_task"] = ""
        answer["out_vars"] = {}
        answer["scene_id"] = "-1"
        err_msg = ""
        return code, err_msg, answer

if __name__ == '__main__':
    pass
