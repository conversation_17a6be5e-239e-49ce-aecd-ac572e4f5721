import json
import structlog

import requests

from vui_svr.settings import global_setting_dict


logger = structlog.get_logger(__name__)

dl_intent_addr = global_setting_dict["dl_intent_addr"]


intent_id_map = {
    # 0: "未知",
    0: "10004",
    # 1: "点咖啡",
    1: "10002",
    # 2: "打车",
    2: "10007",
    # 3: "外卖",
    # 4: "新闻",
    # 4: "10001",
    # 5: "旅游",
    # 6: "购物",
    # 7: "讲故事",
    # 8: "IM助手",
    8: "10009",
    # 9: "会议助手",
    9: "10008",
    # 10: "听音乐",
    # 11: "天气预报"
}

reversed_intent_map = {v: k for k, v in intent_id_map.items()}


def dl_intent(req_id, uid, global_conversation_id, input_str, custom_var):
    history = []
    for h in json.loads(custom_var.get("hist_info", "[]")):
        if h["intent_id"] not in reversed_intent_map.keys():
            logger.error(
                f"[{req_id}][{uid}][{global_conversation_id}] reverse intent id failed."
                f"origin intent id: {h['intent_id']}"
            )

        dl_intent_id = reversed_intent_map.get(h["intent_id"], 0)
        _h = {
            "text": h["msg"],
            "intent_id": dl_intent_id,
            "role": h["role"],
            "conversation_id": h["conversation_id"],
            "create_ts": h["create_ts"]
        }
        if h["role"] == "用户":
            _h["role"] = "user"
        history.append(_h)

    body = {
        "request_id": req_id,
        "conversation_id": global_conversation_id,
        "user_id": uid,
        "conversation": {"history": history, "current_text": input_str},
    }
    res = requests.post(dl_intent_addr + "/intent", json=body)

    if not res.ok:
        logger.error(
            f"[{req_id}][{uid}][{global_conversation_id}] Error when calling dl intent, {res.status_code}, {res.text}"
        )
        return {"intent_id": "-1", "model": "", "related_conversation": {}}

    if res.json()["code"] != 0:
        logger.error(
            f"[{req_id}][{uid}][{global_conversation_id}] Error when calling dl intent, {res.json()}"
        )
        return {"intent_id": "-1", "model": "", "related_conversation": {}}

    intent_result = res.json()["data"]
    intent_result["intent_id"] = intent_id_map.get(intent_result["intent_id"], "-1")
    logger.info(
        f"[{req_id}][{uid}][{global_conversation_id}] dl intent result: {intent_result}, origin: {res.json()}"
    )
    return intent_result
