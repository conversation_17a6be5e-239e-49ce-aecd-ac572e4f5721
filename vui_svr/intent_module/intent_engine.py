from vui_svr.reg_matcher import reg_matcher
from vui_svr.pat_matcher import pat_matcher
from vui_svr.ac_extractor import ac_extractor

from vui_svr.intent_module.dl_intent import dl_intent

from vui_svr.gpt_api_V4 import GPTAgent
from vui_svr.coze_api_V4 import CozeAgent
from vui_svr.langflow_api_V4 import LangflowAgent
from vui_svr.n8n_api_V4 import N8nAgent
from vui_svr.dify_api import DifyAgent
from vui_svr.dr_api_V4 import DRAgent

from concurrent.futures import ThreadPoolExecutor
import json
import structlog
import time
import copy
import os
import sys

executor = ThreadPoolExecutor(5)
logger = structlog.get_logger(__name__)


class IntentEngine:
    def __init__(self):
        self.extractor_list = []
        self.single_matcher_list = []
        self.context_matcher_list = []
        self.instant_matcher_list = []

        self.memo_agent = None

        self.intent_all_bot = None
        self.intent_pos_bot = None
        self.chat_bot = None
        self.intent_instant_bot = None
        self.debug = False

        self.info_agent = None
        self.task_agent = None

        self.interaction_type = ""

    def set_memo_engine(self, memo_agent):
        self.memo_agent = memo_agent

    def set_task_agent(self, info_agent, task_agent):
        self.info_agent = info_agent
        self.task_agent = task_agent
    
    def init_engine(self, global_setting_dict):
        intent_file = global_setting_dict["intent_config_file"]
        omit_file = global_setting_dict["stop_word_file"]
        ext_file_path = global_setting_dict["ner_word_path"]
        match_single_file = global_setting_dict["single_intent_path"]
        match_context_file = global_setting_dict["context_intent_path"]
        instant_addr = global_setting_dict["plugin_rules"]
        coze_auth = global_setting_dict["coze_auth"]

        self.interaction_type = global_setting_dict["interaction_type"]
        self.need_intent_rule = True
        self.need_intent_llm = True
        self.need_chat_llm = True
        self.need_instantcmd_rule = True
        self.need_instantcmd_llm = True

        if "NEED_INTENT_RULE" in global_setting_dict: self.need_intent_rule =  global_setting_dict["NEED_INTENT_RULE"]
        if "NEED_INTENT_LLM" in global_setting_dict: self.need_intent_llm = global_setting_dict["NEED_INTENT_LLM"]
        if "NEED_CHAT_LLM" in global_setting_dict: self.need_chat_llm = global_setting_dict["NEED_CHAT_LLM"]
        if "NEED_INSTANTCMD_RULE" in global_setting_dict: self.need_instantcmd_rule = global_setting_dict["NEED_INSTANTCMD_RULE"]
        if "NEED_INSTANTCMD_LLM" in global_setting_dict: self.need_instantcmd_llm = global_setting_dict["NEED_INSTANTCMD_LLM"]

        logger.info(f'intent_engine, omit_file:{omit_file}, ext_file_path:{ext_file_path}, ' \
                f'match_file_path:{match_single_file}/{match_context_file}, instant_addr:{instant_addr}')
        logger.info(f'need_intent_rule:{self.need_intent_rule}, need_intent_llm:{self.need_intent_llm}, ' \
                f'need_chat_llm:{self.need_chat_llm}, need_instantcmd_rule:{self.need_instantcmd_rule}, ' \
                f'need_instantcmd_llm:{self.need_instantcmd_llm}')

        logger.debug(f'Loading local bots')
        self.intent_all_bot = GPTAgent("intent_all", "intent_all", global_setting_dict["llm_intent_all_model"], 
                global_setting_dict)
        self.intent_pos_bot = GPTAgent("intent_pos", "intent_pos", global_setting_dict["llm_intent_pos_model"], 
                global_setting_dict)
        self.chat_bot = DifyAgent("", "", "", global_setting_dict)
        # self.chat_bot = N8nAgent("", "", "chat", global_setting_dict)
        # self.chat_bot = GPTAgent("chat", "chat", global_setting_dict["llm_chat_model"], global_setting_dict)
        self.intent_instant_bot = GPTAgent("intent_instant", "intent_instant", global_setting_dict["llm_intent_instant"], 
                global_setting_dict)

        intent_info = {}
        task_bots = {}
        action_bots = {}

        logger.debug(f'Loading intent config file: {intent_file}')
        with open(intent_file, 'r', encoding='utf-8') as f:
            intent_info = json.load(f)

        for intent_id, intent_item in intent_info.items():
            task_bot_conf = intent_item["task_bot"]
            if task_bot_conf["type"] == "gpt":
                model = task_bot_conf.get("model", "gpt4o")
                task_bots[intent_id] = GPTAgent(task_bot_conf["id"], task_bot_conf["name"], model, global_setting_dict)
            else:
                task_bots[intent_id] = DRAgent(task_bot_conf["id"], task_bot_conf["name"])

            action_bot_conf = intent_item["action_bot"]

            if action_bot_conf["type"] == "coze":
                action_bots[intent_id] = CozeAgent(coze_auth, action_bot_conf["id"], action_bot_conf["name"], global_setting_dict)
            elif action_bot_conf["type"] == "langflow":
                action_bots[intent_id] = LangflowAgent(coze_auth, action_bot_conf["id"], action_bot_conf["name"], global_setting_dict)
            elif action_bot_conf["type"] == "dify":
                action_bots[intent_id] = DifyAgent("", action_bot_conf["id"], action_bot_conf["name"], global_setting_dict)
            # elif action_bot_conf["type"] == "n8n":
            #     n8n_auth = global_setting_dict["n8n_auth"]
            #     action_bots[intent_id] = N8nAgent(n8n_auth, action_bot_conf["id"], action_bot_conf["name"], global_setting_dict)

        logger.debug(f'Intent config file processed')

        omit_list = []
        with open(omit_file, 'r') as f:
            omit_list = [_line.rstrip("\n") for _line in f]

        logger.debug(f'Initing exrtactors')
        ac_ext = ac_extractor()
        ac_ext.build_index(ext_file_path)
        self.extractor_list.append(ac_ext)
        logger.debug(f'Extractors inited, cnt:{len(self.extractor_list)}')

        logger.debug(f'Initing single matchers')
        single_matcher = reg_matcher()
        single_matcher.init_matcher(match_single_file, omit_list)
        self.single_matcher_list.append(single_matcher)
        logger.debug(f'Single matchers inited')

        logger.debug(f'Initing context matchers')
        context_mactcher = reg_matcher()
        context_mactcher.init_matcher(match_context_file, omit_list)
        self.context_matcher_list.append(context_mactcher)
        logger.debug(f'Context matchers inited')

        logger.debug(f'Initing instant matcher')
        instant_matcher = pat_matcher()
        instant_matcher.init_matcher(instant_addr, omit_list)
        self.instant_matcher_list.append(instant_matcher)
        logger.debug(f'Instant matcher inited')

        self.intent_info = intent_info
        return intent_info, task_bots, action_bots

    def analyze_extract(self, input_str):
        extract_dict = {}
        for extract_op in self.extractor_list:
            extract_ret = extract_op.extract(input_str)

            for ret_item in extract_ret:
                new_word = input_str[ret_item["start"]:ret_item["end"]+1]
                logger.debug(f'found new syn piece, start:{ret_item["start"]}, end:{ret_item["end"]}, type:{ret_item["type"]}')
                if ret_item["type"] in extract_dict:        # merge extract rets
                    is_cover = False
                    for exist_item in extract_dict[ret_item["type"]]:
                        if ret_item["start"] <= exist_item["start"] and ret_item["end"] >= exist_item["end"]:
                            is_cover = True
                            break
                        if not is_cover:
                            extract_dict[ret_item["type"]].append(ret_item)
                else:
                    extract_dict[ret_item["type"]] = [ret_item]

        return extract_dict

    def analyze_context(self, input_str, extract_dict, exist_var):
        match_dict = {}
        for match_op in self.context_matcher_list:
            match_ret = match_op.match(input_str, extract_dict, exist_var)
            for ret_key, ret_item in match_ret.items():
                match_dict[ret_key] = ret_item
        return match_dict

    def analyze_single(self, input_str, extract_dict, exist_var):
        match_dict = {}
        for match_op in self.single_matcher_list:
            match_ret = match_op.match(input_str, extract_dict, exist_var, self.debug)
            for ret_key, ret_item in match_ret.items():
                match_dict[ret_key] = ret_item
        return match_dict

    def norm_time(self, val):
        norm_val = int(val*1000)/1000
        return f'{norm_val}'

    def analyze_instant(self, uid, input_str, param_blk_list):
        match_dict = {}
        match_list = []
        filt_rules = {}
        for match_op in self.instant_matcher_list:
            match_ret, new_rules = match_op.match(uid, input_str, param_blk_list)
            filt_rules.update(new_rules)
            if match_ret:
                match_list.append(match_ret)

        for rule_id, rule_item in filt_rules.items():
            filt_rules[rule_id]["ori_params"] = filt_rules[rule_id]["params"]
        logger.debug(f'match instant, match_list:{match_list}')
        if len(match_list) > 0:
            match_dict = match_list[0]
            match_dict["intent_id"] = "10006"
            #match_dict["scene_id"] = "-1"
            if "fast_ret" not in match_dict:
                match_dict["fast_ret"] = "好的，请稍等"
        return match_dict, filt_rules

    def get_chatmsg_worker(self, req_id, user_id, global_conversation_id, chat_msg, custom_var):
        #req_msg = json.dumps(custom_var, ensure_ascii=False)
        code, err_msg, instant_msg, model_name, prompt_token, output_token, time_cost = self.chat_bot.chat(req_id, user_id, 
                global_conversation_id, chat_msg, custom_var, stream=True)
        if err_msg:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] chat bot returns err_msg: {err_msg}')

        instant_reply = instant_msg
        if isinstance(instant_msg, dict) and "response" in instant_msg:
            instant_reply = instant_msg["response"]
        return instant_reply, model_name, prompt_token, output_token, time_cost

    def get_instantcmd_worker(self, req_id, user_id, global_conversation_id, chat_msg, custom_var, filt_rules):
        #req_msg = json.dumps(custom_var, ensure_ascii=False)

        # add filted rules
        logger.debug(f'filt_rules:{filt_rules}, custom_var:{custom_var}')
        rule_input = [
            {
                "rule_id": rule_item["rule_id"],
                "app_names": rule_item["app_names"],
                "rule_str": rule_item["rule_str"],
                "params": rule_item.get("ori_params", []),
                "desc": rule_item["task_desc"]
            } for rule_id, rule_item in filt_rules.items()
        ]
        rule_input_str = json.dumps(rule_input, ensure_ascii=False)
        if rule_input:
            custom_var["info"] = rule_input_str
        else:
            custom_var["info"] = ""

        logger.debug(f'filt_rules:{filt_rules}, custom_var:{custom_var}')
        # modify hist_info
        hist_chat_str = ""
        for item in json.loads(custom_var["hist_info"]):
            role = "你:"
            if item["role"] == "user": role = "用户:"
            hist_chat_str = f'{hist_chat_str}；{role}{item["msg"]}'
        hist_chat_str = f'{hist_chat_str}；用户:{chat_msg}'
        new_custom_var = copy.deepcopy(custom_var)
        new_custom_var["hist_info"] = hist_chat_str

        logger.debug(f'filt_rules:{filt_rules}, custom_var:{custom_var}')
        # add running_rule & hist_rule
        if "hist_rule" not in new_custom_var: new_custom_var["hist_rule"] = "{}"
        if "running_rule" not in new_custom_var: new_custom_var["running_rule"] = "{}"

        logger.debug(f'building instantcmd llm, input:{chat_msg}, custom:{new_custom_var}')
        code, err_msg, instant_msg, model_name, prompt_token, output_token, time_cost = self.intent_instant_bot.chat(req_id, 
                user_id, global_conversation_id, chat_msg, new_custom_var, True)

        return instant_msg, model_name, prompt_token, output_token, time_cost

    def do_analyze(self, req_id, uid, input_str, custom_var, exist_var, global_conversation_id, hist_chat):
        logger.info(f'[{req_id}][{uid}][{global_conversation_id}] Intent start, msg:{input_str}, custom_var:{custom_var}, '
                     f'exist_var:{exist_var}')

        time_start = time.time()
        run_details = {"time_cost": 0, "time_details": {}, "prompt_token":0, "output_token":0, "token_details": {}}
        single_dict = {}
        context_dict = {}
        ret_dict = {}
        instant_reply = ""
        instant_dict = {}
        filt_rules = {}
        instant_intent = None

        extract_dict = self.analyze_extract(input_str)
        param_blk_list = extract_dict.get("param_blk_list", [])
        profile_key_list = extract_dict.get("feat_name", [])
        custom_var["blk_list"] = ",".join([item["wordB"] for item in param_blk_list])

        profile_ret = self.memo_agent.get_portrait(req_id, uid, global_conversation_id, profile_key_list)
        custom_var["profile_base"] = profile_ret.get("profile_base", {})
        custom_var["profile_task"] = profile_ret.get("profile_task", {})

        # DL intent first
        dl_intent_res = dl_intent(req_id, uid, global_conversation_id, input_str, custom_var)

        _intent_obj = { "scene_id": -1, "fast_ret": "", "new_task": "", "out_var": {}, "params": {}, "cmd_str": ""}
        if dl_intent_res["intent_id"] == "10004":
            _intent_obj["cmd_str"] = "INSTANT_REPLY"
        ret_dict[dl_intent_res['intent_id']] = _intent_obj

        if self.need_chat_llm:
            chat_thread = executor.submit(
                self.get_chatmsg_worker, req_id, uid, global_conversation_id, input_str, custom_var
            )

        if self.need_instantcmd_rule:
            instant_dict, filt_rules = self.analyze_instant(uid, input_str, param_blk_list)
            logger.info(f'[{req_id}][{uid}][{global_conversation_id}] instant_dict:{instant_dict}')

        if instant_dict:
            ret_dict[instant_dict["intent_id"]] = {
                "scene_id": instant_dict["scene_id"],
                "fast_ret": instant_dict["fast_ret"],
                "new_task": instant_dict["new_task"],
                "out_var": instant_dict["out_var"],
                "params": {},
                "cmd_str": ""
            }
            # logger.debug("[{req_id}][{uid}][{global_conversation_id}] Instant Rule Hit, return")
        else:
            # blablabla
            if not filt_rules and exist_var and "10006" in exist_var:
                filt_rules = {exist_var["10006"]["rule_id"]: exist_var["10006"]}
            if self.need_instantcmd_llm and filt_rules:
                instantcmd_thread = executor.submit(
                    self.get_instantcmd_worker, req_id, uid, global_conversation_id, input_str, custom_var, filt_rules
                )

            if self.need_chat_llm:
                logger.info(f'[{req_id}][{uid}][{global_conversation_id}] chat LLM req start, msg:{input_str}, ' \
                        f'custom_var:{custom_var}')
                instant_reply, model_name, prompt_token, output_token, time_cost = chat_thread.result()
                run_details["token_details"]["chat"] = {
                    "model": model_name,
                    "prompt_token":prompt_token,
                    "output_token":output_token
                }
                run_details["time_details"]["chat"] = self.norm_time(time_cost)
                logger.info(f'[{req_id}][{uid}][{global_conversation_id}] chat LLM req end, resp:{instant_reply}')

            if self.need_instantcmd_llm and filt_rules:
                instant_intent, model_name, prompt_token, output_token, time_cost = instantcmd_thread.result()
                run_details["token_details"]["intent_instant"] = {
                    "model": model_name,
                    "prompt_token":prompt_token,
                    "output_token":output_token
                }
                run_details["time_details"]["intent_instant"] = self.norm_time(time_cost)
                for var_key, var_val in instant_intent.items():
                    if var_key not in ["running_rule", "hist_rule"]:
                        if var_val and type(var_val) != str:
                            instant_intent[var_key] = str(var_val)
                logger.debug(f'instant_intent: {instant_intent}, ret_dict: {ret_dict}')

        logger.debug("Intent finish, call task LLM later")

        need_intent = True 
        ret_msg = {"intent_id": "-1", "scene_id": "-1", "fast_ret": "", "new_task": "", "cmd_str": "", "out_var": {}, "params": {}}
        if instant_intent:
            target_rule = {}
            if "jump" in instant_intent:
                if instant_intent["jump"] == "0" and "running_rule" in instant_intent and instant_intent["running_rule"] and \
                        "rule_id" in instant_intent["running_rule"] and instant_intent["running_rule"]["rule_id"]:
                    target_rule = instant_intent["running_rule"]
                if instant_intent["jump"] == "1":
                    if "hist_rule" in instant_intent and instant_intent["hist_rule"] and \
                            "rule_id" in instant_intent["hist_rule"] and instant_intent["hist_rule"]["rule_id"]:
                        target_rule = instant_intent["hist_rule"]
            
            if "scene_id" in target_rule and "rule_id" in target_rule and "ask" in target_rule and "params" in target_rule and \
                    target_rule['scene_id'] and target_rule['rule_id'] and target_rule['ask']:
                out_vars = filt_rules[target_rule["rule_id"]]
                is_param_valid = True
                #for param_key in out_vars["params"]:
                #    norm_param_key = param_key.lstrip('<').rstrip(">")
                #    if norm_param_key not in target_rule["params"]:
                #        is_param_valid = False
                #        break

                if is_param_valid:
                    need_intent = False
                    out_vars["params"] = json.dumps(target_rule["params"], ensure_ascii=False)
                    if type(out_vars["ori_params"]) != str:
                        out_vars["ori_params"] = json.dumps(out_vars["ori_params"], ensure_ascii=False)
                    else:
                        out_vars["ori_params"] = out_vars["ori_params"]
                    out_vars["ask"] = target_rule["ask"]
                    scene_id = "1"
                    if "scene_id" in target_rule: scene_id = target_rule["scene_id"]
                    ret_msg = {
                        "intent_id": "10006",
                        "scene_id": scene_id,
                        "fast_ret": "好的，请稍等",
                        "new_task": input_str,
                        "cmd_str": "",
                        "out_var": out_vars,
                        "params": {}
                    }
            else:
                need_intent = False
                ret_msg = {
                    "intent_id": "10006",
                    "scene_id": "1",
                    "fast_ret": "",
                    "new_task": input_str,
                    "cmd_str": "INSTANT_REPLY",
                    "out_var": {},
                    "params": {},
                    "instant_reply": "出错了"
                }

        logger.debug(f'ret_msg:{ret_msg}, need_intent:{need_intent}')
        if need_intent:
            for intent_key, intent_val in ret_dict.items():
                cmd_str = intent_val["cmd_str"]
                #if intent_val["new_task"] == "CANCEL_ACT":
                #    cmd_str = "CANCEL_ACT"

                #for var_key, var_val in intent_val["out_var"].items():
                #    if type(var_val) == list: var_val = ",".join(var_val)
                #    if type(var_val) == float or type(var_val) == int: var_val = str(var_val)
                #    intent_val["out_var"][var_key] = var_val
                ret_msg = {
                    "intent_id": intent_key,
                    "scene_id": intent_val["scene_id"],
                    "fast_ret": intent_val["fast_ret"],
                    "new_task": intent_val["new_task"],
                    "cmd_str": cmd_str,
                    "out_var": intent_val["out_var"],
                    "params": intent_val["params"]
                }
                break

            if ret_msg["intent_id"] == "10004" or ret_msg["intent_id"] == "-1":
                ret_msg["instant_reply"] = instant_reply

        # merge vars 
        target_intent_id = ret_msg["intent_id"]
        if target_intent_id not in exist_var: exist_var[target_intent_id] = {}
        for var_key, var_val in ret_msg["out_var"].items():
            exist_var[target_intent_id][var_key] = var_val


        longtask_intent_id = ""
        if hist_chat:
            for chat_item in hist_chat:
                logger.debug(f'{req_id}][{uid}][{global_conversation_id}] chat_item: {chat_item}')
                chat_intent_id = chat_item["intent_id"]
                if chat_intent_id in ["10002"]:
                    longtask_intent_id = chat_intent_id

        if self.interaction_type == "simple_round":
            if longtask_intent_id and ret_msg["cmd_str"] not in ["BYEBYE", "REPLAY"]:
                old_intent_id = ret_msg.get("intent_id", "")
                if old_intent_id != longtask_intent_id:
                    out_var = {}
                    if longtask_intent_id in exist_var:
                        out_var = exist_var[longtask_intent_id]
                    logger.debug(f'[{req_id}][{uid}][{global_conversation_id}] before cover, ret:{ret_msg}')
                    ret_msg = {
                        "intent_id": longtask_intent_id,
                        "scene_id": "-1",
                        "fast_ret": "好的，稍等",
                        "new_task": "",
                        "cmd_str": "",
                        "out_var": out_var,
                        "params": {}
                    }
                else:
                    logger.debug(f'[{req_id}][{uid}][{global_conversation_id}] same intent_id, follow rules')

        time_end = time.time()
        run_details["time_cost"] = self.norm_time(time_end - time_start)
        for token_stage, token_item in run_details["token_details"].items():
            run_details["prompt_token"] += token_item["prompt_token"]
            run_details["output_token"] += token_item["output_token"]
        logger.info(f'[{req_id}][{uid}][{global_conversation_id}] Intent finished, ret:{ret_msg}, run_details:{run_details}')
        return ret_msg, run_details

#logger.basicConfig(level = logger.DEBUG)
if __name__ == '__main__':
    pass
    # logger.basicConfig(level = logger.DEBUG)
    # logger.getLogger().setLevel(logger.DEBUG)
    # file_path = sys.argv[1]

    # global_setting_dict = {}
    # with open("global_setting.cmdp.offline", 'r', encoding='utf-8') as file:
    #     global_setting_dict = json.load(file)

    # info_agent = InfoAgent(global_setting_dict)
    # memo_agent = MemoAgent(info_agent, global_setting_dict)

    # #logger.debug('fuck')

    # local_intent = IntentEngine(memo_agent)
    # intent_info, task_bots, action_bots = local_intent.init_engine(global_setting_dict)
    # task_agent = TaskAgent(global_setting_dict, task_bots, intent_info, info_agent)
    # local_intent.set_task_agent(info_agent, task_agent)

    # req_id = "111"
    # user_id = "1900437112298143744"
    # intent_id = "10002"
    # intent_name = "外卖"
    # coords = ""
    # chat_conversation_id = "123"
    # global_conversation_id = "123"

    # file_list = []
    # if os.path.isdir(file_path):
    #     for root, dirs, files in os.walk(file_path):
    #         for file in files:
    #             file_list.append(os.path.join(root, file))
    # else:
    #     file_list.append(file_path)
    #     logger.debug(f'filecnt:{len(file_list)}')

    # for file_item in file_list:
    #     logger.debug(f'Loading case file: {file_item}')
    #     exist_var = {"10002": {}}
    #     last_scene_id = 0
    #     last_scene_sub_id = 0
    #     ret_dict = {}

    #     with open(file_item, "r") as file:
    #         hist_msg_list = []
    #         hist_item_list = []
    #         for line in file:
    #             line = line.strip()
    #             pieces = line.split("\t")

    #             if pieces[0] == "user":
    #                 role = "用户"
    #                 if len(pieces) == 3:
    #                     for param in pieces[2].split(","):
    #                         param_piece = param.split(":")
    #                         if param_piece[0] == "scene_id":
    #                             last_scene_id = param_piece[1]
    #             else:
    #                 role = "你"
    #                 if len(pieces) == 3:
    #                     last_scene_sub_id = pieces[2]


    #             if pieces[0] == "user":
    #                 hist_msg_str = ",".join(hist_msg_list)

    #                 hist_chat = hist_msg_str
    #                 intent_hist_chat = hist_msg_str
    #                 ori_chat_msg = pieces[1]

    #                 if "out_var" in ret_dict:
    #                     for var_key, var_val in ret_dict["out_var"].items():
    #                         exist_var["10002"][var_key] = var_val
    #                 custom_var = {"uid": user_id, "coords": coords, "hist_info": f'[{hist_msg_str}]', "hist_intent": intent_name,
    #                         "hist_intent_id": intent_id, "running_task": ""}
    #                 ret_dict = local_intent.do_analyze(req_id, user_id, ori_chat_msg, custom_var, exist_var,
    #                         global_conversation_id, hist_item_list)
    #                 logger.debug(f'@@@@@@@@@@@@@@@@@@, ret_dict: {ret_dict}')

    #             hist_msg_list.append(f'{{"role": "{role}", "msg": "{pieces[1]}", "scene_id": "{last_scene_id}", "intent_id": "10002", "scene_sub_id": ' \
    #                     f'{last_scene_sub_id}}}')
    #             hist_item_list.append(json.loads(hist_msg_list[-1]))

    #     logger.debug(f"\n##############################################")
