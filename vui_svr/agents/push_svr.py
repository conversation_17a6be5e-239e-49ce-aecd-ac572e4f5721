# coding=utf-8

from vui_svr.coze_api_V4 import CozeAgent
import redis
import time
import json
import sys
import uuid
import structlog
from .request_api import RequestAgent

logger = structlog.get_logger(__name__)

class PushAgent:
    def __init__(self, redis_client, intent_info, client_req_agent, report_agent, global_setting_dict):
        self.db_client = redis_client

        self.listen_sleep_time = 0.01
        self.pubsub = self.db_client.pubsub()
        self.intent_info = intent_info
        self.app_intent_dict = {}
     
        self.client_req_agent = client_req_agent
        self.report_agent = report_agent
        coze_auth = global_setting_dict["coze_auth"]

        self.push_bots = {
            "10002":CozeAgent(coze_auth, "7439332567486185481", "push_process", global_setting_dict),
            "10005":CozeAgent(coze_auth, "7439332567486185481", "push_process", global_setting_dict)
        }

        # register listen channel
        for intent_id, intent_item in intent_info.items():
            for app_item in intent_item["app_list"]:
                logger.info(f'push, registing appid:{app_item["app_id"]}, intent_id:{intent_id}, channel:{app_item["channel"]}')
                self.app_intent_dict[app_item["app_id"]] = intent_id
                self.pubsub.subscribe(**{app_item["channel"]: self.handle_push_message})

        self.pubsub.run_in_thread(sleep_time = self.listen_sleep_time, daemon = True)

    def handle_push_message(self, message):
        print(f"################ Received message: {message}, type: {type(message)}")
        data_content = message["data"].decode('utf-8')
        print(f"################ Received data_content: {data_content}")
        data_content_dict = json.loads(data_content)
        #data_content_dict = json.loads(data_content_dict)
        print(f"################ Received data_content_dict: {data_content_dict}, type: {type(data_content_dict)}")
        context = data_content_dict["context"]
        print(f"################ Received context: {context}")
        context_list = context.split("&")
        context_dict = {}
        for item in context_list:
            piece = item.split("=")
            context_dict[piece[0]] = piece[1]
        print(f"################ Received context_dict: {context_dict}, type: {type(context_dict)}")
        data_content_dict = data_content_dict['data']['monitor_data']
        print(f"################ Received data_content_dict: {data_content_dict}, type: {type(data_content_dict)}")
        req_id = str(uuid.uuid4())
        
        app_name = context_dict["app_id"]
        if app_name in self.app_intent_dict:
            intent_id = self.app_intent_dict[app_name]
            if intent_id in self.push_bots:
                user_id = context_dict["uid"]
                token = context_dict.get("token", "")
                ver_id = context_dict.get("ver_id", 0)
                conversation_id = context_dict["conversation_id"]
                global_conversation_id = context_dict.get("global_conversation_id", "")
                app_id = context_dict["app_id"]
                coords = ""
                
                if data_content_dict["title"] != "" or data_content_dict["text"] != "":
                    push_msg = f'{data_content_dict["title"]} {data_content_dict["text"]}'
                    custom_var = {"uid": user_id, "coords": coords, "intent_id": intent_id, "req_id": req_id, 
                            "conversation_id":conversation_id, "input": push_msg, "app_id": app_id}
                    if "map" in data_content_dict:
                        custom_var["map"] = data_content_dict["map"]
                    combine_msg = json.dumps(custom_var, ensure_ascii=False)
    
                    code, err_msg, run_code, push_ret_msg = self.push_bots[intent_id].chat(combine_msg, user_id, "answer", 
                            custom_var)
                    if not(code == 0) or not("output" in push_ret_msg) or run_code == "fail":
                        print(f"error when processing push message")
                    else:
                        print(f"answering push message, get msg:{push_ret_msg}")
                        text_msg_str = f'{{"output":"{push_ret_msg["output"]}", "detail_info": "{{}}", "status":0}}'
                        self.report_agent.push_report(req_id, user_id, intent_id, "-1", "-1", push_ret_msg["output"], 
                                push_ret_msg["detail_info"], 0, conversation_id, global_conversation_id, "on", "off", 
                                "push", "must", -1)
                        #self.client_req_agent.send_message_to_client(req_id, user_id, intent_id, coords, push_ret_msg["output"], push_ret_msg["detail_info"], 0, {})
                else:
                    print(f"########### Empty msg, discard")

if __name__ == '__main__':
    redis_client = redis.StrictRedis(host = "127.0.0.1", port = 16379, username = "default", password = "Spotlight321@")
    intent_info = {
        "10002": {
            "name": "外卖",
            "app_list": [{"name": "外卖", "desc": "提供咖啡外卖，咖啡在线点单到店取服务", "app_name": "瑞幸", "app_id": "com.lucky.luckyclient", "channel": "luckyin"}]
        },
        "10005": {
            "name": "打车",
            "app_list": [{"name": "打车", "desc": "提供打车服务，为用户叫车", "app_name": "百度", "app_id": "com.baidu.BaiduMap", "channel": "app-monitor-com.baidu.BaiduMap"}]
        }
    }
    client_addr = {
        "WEB": "http://127.0.0.1:5000",
        "APP": "http://127.0.0.1:9002"
    }
    run_type = "WEB"
    client_req_agent = RequestAgent(ip = client_addr[run_type], run_type = run_type)

    push_agent = PushAgent(redis_client, intent_info = intent_info, client_req_agent = client_req_agent)
    print("agent loop start")

    while True:
        time.sleep(1)
