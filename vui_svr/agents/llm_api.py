# OpenAI wrapper for completion and function call
import json
import time
from typing import List, Dict

from openai import OpenAI


class LLMWrapper:
    def __init__(self, base_url: str, api_key: str = ''):
        if api_key:
            self.cli = OpenAI(base_url=base_url, api_key=api_key)
        else:
            self.cli = OpenAI(base_url=base_url)

    def get_responses(self, model: str, messages: List, tools: List = None) -> Dict:
        if not model or not messages:
            raise Exception('No model or messages provided')
        resp = self.cli.chat.completions.create(
            model=model,
            messages=messages,
            tools=tools,
            parallel_tool_calls=bool(tools),
        )

        completion = resp.choices[0].message

        tools = []
        for tool_call in (completion.tool_calls or []):
            tool_call_dict = {
                "id": tool_call.id,
                "type": tool_call.type,
                "function": {
                    "name": tool_call.function.name,
                    "arguments": tool_call.function.arguments,
                },
            }
            tools.append(tool_call_dict)
        return {
            "tools": tools,
            "message": completion.content,
        }

# llm_wrap = LLMWrapper('api_url')
llm_wrap = LLMWrapper(
        base_url="https://one-api.guangfan-ai.tech/v1",
        api_key="sk-1PkC3vEbms46lvBpBbE241FfE03848A0B0D3AcCeBf15A479"
    )
if __name__ == '__main__':
    llm_wrap = LLMWrapper(
        base_url="https://one-api.guangfan-ai.tech/v1",
        api_key="sk-1PkC3vEbms46lvBpBbE241FfE03848A0B0D3AcCeBf15A479"
    )

    messages = [
        {"role": "system", "content": "你是一个智能助手"},
        {"role": "user", "content": "你好"}
    ]
    models = [
        # "deepseek-chat",
        # "deepseek-reason",
        "doubao-seed-1.6-flash-250615",
        # "doubao-seed-1.6-250615",
        "deepseek-v3-250324"
    ]

    for model in models:
        now = time.time()
        res = llm_wrap.get_responses(model=model, messages=messages)
        print(f"cost: {time.time() - now}, {model}: {res}")
