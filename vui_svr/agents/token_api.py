import requests
import datetime
import json
import structlog
import traceback

from urllib.parse import urljoin

from vui_svr.client_message import *    #ClientMessage, MESSAGE_MODAL_VOICE
from .request_api import RequestAgent

logger = structlog.get_logger(__name__)

class TokenAgent:
    def __init__(self, ip):
        self.ip = ip
        self.req_agent = RequestAgent(ip, "WEB")

    def auth_token(self, token):
        header = {'Authorization': token}
        auth_res = self.req_agent.post_request("/agent-api/common/auth/validateToken", None, extra_headers=header, 
                pro_ver_id="0", need_resp=True)
        return auth_res

    def auth_token_wrap(self, token, req_id):
        auth_res = self.auth_token(token)

        if not auth_res or not auth_res.get("status", False) or auth_res.get("data", {}).get("userId", None) is None:
            logger.error(f'[{req_id}][][] auth_token failed')
            return False, ""

        if auth_res.get("data", {}).get("result", False) is False:
            logger.error(f'[{req_id}][][] invalid auth_token')
            return False, ""

        uid = auth_res.get("data").get("userId")
        return True, str(uid)

    def mqtt_auth_info(self, token: str, pro_ver_id: int = 0) -> dict:
        print(self.ip, type(self.ip))
        res = requests.get(
            url=urljoin(self.ip[pro_ver_id], "agent-api/iot/device/mqttAuthInfo"),
            headers={"Authorization": token},
        )
        res.raise_for_status()
        content = res.json()
        if content["code"] != 0:
            logger.error("mqtt auth failed!")
        return res.json()["data"]

    def coords_info_from_token(self, token: str, pro_ver_id: int = 0) -> str:
        res = requests.get(
            url=urljoin(self.ip[pro_ver_id], "agent-api/iot/device/device-data?dataType=GPS&extraParams=amap"),
            headers={"Authorization": token},
        )
        res.raise_for_status()
        content = res.json()
        if content["code"] != 0:
            logger.error(f"Get coords info failed! data: {content}")
            return "0.0, 0.0"
        data = res.json()["data"]
        return f"{data['longitude']},{data['latitude']}"

if __name__ == '__main__':
    token_agent = TokenAgent(ip = "http://127.0.0.1:18080")
    token_agent.auth_token_wrap("123", "123")
