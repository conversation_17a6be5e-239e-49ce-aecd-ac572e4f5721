from vui_svr.settings import global_setting_dict, test_token
from vui_svr.memo_module import info_agent, memo_agent
from vui_svr.intent_module import task_bots, intent_info

from .request_api import RequestAgent
from .token_api import TokenAgent
from .task_api import TaskAgent
from .report_api import ReportAgent
from .app_api import AppAgent
from .status_api import StatusAgent
from .push_svr import PushAgent

client_addr = {
    "WEB": global_setting_dict["web_client"],
    "APP": global_setting_dict["app_client"]
}
client_ver_dict = {
    "WEB": global_setting_dict["web_client_ver"],
    "APP": global_setting_dict["app_client_ver"]
}
run_type = global_setting_dict["run_type"]    # APP/WEB


client_req_agent = RequestAgent(ip = client_addr[run_type], run_type = run_type)
server_req_agent = RequestAgent(ip = client_addr["WEB"], run_type = "WEB")
plugin_req_agent = RequestAgent(ip = global_setting_dict["plugin_client"], run_type = run_type)
phone_req_agent = RequestAgent(ip = global_setting_dict["phone_client"], run_type = run_type)
token_agent = TokenAgent(ip = global_setting_dict["token_client"])
auto_res, test_uid = token_agent.auth_token_wrap(test_token, "web_1")

task_agent = TaskAgent(global_setting_dict, task_bots, intent_info, info_agent)
report_agent = ReportAgent(global_setting_dict, intent_info, info_agent)
appid_agent = AppAgent(global_setting_dict)
status_agent = StatusAgent(global_setting_dict)
push_agent = PushAgent(
    redis_client=info_agent.db_client,
    intent_info=intent_info,
    client_req_agent=client_req_agent,
    report_agent=report_agent,
    global_setting_dict=global_setting_dict
)
#timer_agent = TimerAgent(redis_client = info_agent.db_client, intent_info = intent_info, client_req_agent = client_req_agent,
#        task_agent = task_agent, report_agent = report_agent, info_agent = info_agent, role_dict = role_dict,
#        run_type = run_type, max_workers = global_setting_dict["TIMER_TASK_THREAD_CNT"])

# start timer daemon
#if global_setting_dict["TASK_EXP_TIME"] > 0 and global_setting_dict["NEED_TIMER"]:
#    timer_thread = threading.Thread(target=timer_agent.start_svr)
#    timer_thread.daemon = True
#    timer_thread.start()

