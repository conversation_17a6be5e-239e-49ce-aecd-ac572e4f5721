# coding=utf-8

from vui_svr.gpt_api_V4 import GP<PERSON>gent
from vui_svr.redis_lock import RedisLock

import redis
import time
import json
import sys
import structlog

logger = structlog.get_logger(__name__)

class ReportAgent:
    def __init__(self, global_setting_dict, intent_info, info_agent, need_report = False):
        self.db_client = redis.StrictRedis(host = global_setting_dict["redis_ip"], port = global_setting_dict["redis_port"], username = global_setting_dict["redis_user"], password = global_setting_dict["redis_pass"])
        #self.db_client = redis.StrictRedis(host = "127.0.0.1", port = "16379", username = "default", password = "Spotlight321@")
        self.report_expire_time = 300
        self.need_report = need_report
        self.report_merge_bot = GPTAgent("report_merge", "report_merge", "qwen-turbo", global_setting_dict)
        self.report_switch_bot = GPTAgent("report_switch", "report_switch", "qwen-turbo", global_setting_dict)
        self.run_mode  = global_setting_dict["run_mode"]
        self.source_priority_dict = {"error": 0, "push": 0, "time": 0, "info": 1, "task": 3, "active": 4, "default": 5}
        self.intent_info = intent_info
        self.info_agent = info_agent

    def get_report_list(self, user_id: str, global_conversation_id: str, intent_id: str):
        report_key = f'{user_id}_report'
        item_key = f'{global_conversation_id}_{intent_id}'
        report_list_str = self.db_client.hget(report_key, item_key)
        self.db_client.expire(report_key, self.report_expire_time)
        return json.loads(report_list_str) if report_list_str else []

    def del_report_list(self, user_id: str, global_conversation_id: str, intent_id: str):
        report_key = f'{user_id}_report'
        item_key = f'{global_conversation_id}_{intent_id}'
        self.db_client.hdel(report_key, item_key)
        #self.db_client.delete(f'{user_id}_0_report')
        #self.db_client.delete(f'{user_id}_{intent_id}_report')
        return 0

    def set_report_list(self, user_id, global_conversation_id, intent_id, report_list):
        report_key = f'{user_id}_report'
        item_key = f'{global_conversation_id}_{intent_id}'
        report_list_str = json.dumps(report_list)
        self.db_client.hset(report_key, item_key, report_list_str)
        self.db_client.expire(report_key, self.report_expire_time)
        return 0

    def pop_report(self, req_id, user_id, global_conversation_id, intent_id, fg_intent_id):
        has_sent_msg = False
        is_talk_off = False
        next_report = None
        next_idx = -1
        waiting_report_list = []
        priority_dict = {}

        intent_status = self.info_agent.get_intent_status(user_id, global_conversation_id)
        ts = time.time()

        logger.debug(f'[{req_id}] enter send message, uid:{user_id}, intentid:{intent_id}')
        lock_key = f"{user_id}_{global_conversation_id}_0_report"
        report_lock = RedisLock(self.db_client, lock_key)
        if report_lock.acquire():
            try:
                report_list = self.get_report_list(user_id, global_conversation_id, "0")
                # logger.debug(f'xxxx [{req_id}], user_id:{user_id}, intent:0, report list before filt, {report_list}')
                for report_idx, report in enumerate(report_list):
                    if report["status"] in ["finish", "cancel"]:
                        continue

                    if report["talk_cmd"] == "off":         # 遇到切断联系的消息优先播放
                        next_report = report
                        next_idx = report_idx
                        is_talk_off = True
                        break

                    if report["status"] == "sent":          # 判断是否有已发送且播放中的消息，此时不返回新report
                        sent_time = report.get("time", 0)
                        eta_voice = len(report.get("ret_output", "")) // 2 + 1
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] sent_time:{sent_time}, eta_voice:{eta_voice}, curr_time:{ts}')
                        if ts - sent_time > eta_voice:
                            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] sent_time expired')
                            report_list[report_idx]["status"] = "finish"
                        elif report.get("with_ack", True):
                        # else:
                            has_sent_msg = True
                            break

                    if report["status"] == "waitret":       # 确定待返回消息的优先级
                        curr_report_type = report["report_type"]
                        # 抛弃时效性的消息
                        if float(report["exp_time"]) > ts:
                            report_list[report_idx]["status"] = "cancel"
                        # 后台任务不再垫话
                        if curr_report_type == "omit" and report["intent_id"] != fg_intent_id:
                            report_list[report_idx]["status"] = "cancel"

                        curr_priority, curr_intent_type = self.get_report_priority(report["report_source"], report["intent_id"])
                        curr_report_time = report["time"]
                        curr_intent_id = report.get("intent_id", "-1")
                        curr_report_is_stack = 0
                        if report["intent_id"] in intent_status:
                            curr_report_is_stack = intent_status[report["intent_id"]]["in_stack"]

                        if next_report:
                            sel_priority, sel_intent_type = self.get_report_priority(next_report["report_source"], 
                                    next_report["intent_id"])
                            sel_report_type = next_report["report_type"]
                            sel_report_time = next_report["time"]
                            sel_intent_id = next_report.get("intent_id", "-1")
                            sel_report_is_stack = 0
                            if next_report["intent_id"] in intent_status:
                                sel_report_is_stack = intent_status[next_report["intent_id"]]["in_stack"]

                            is_valid = False
                            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] curr_priority:{curr_priority}, ' \
                                    f'curr_report_time:{curr_report_time}, sel_priority:{sel_priority}, ' \
                                    f'sel_report_time:{sel_report_time}, report_intent:{report["intent_id"]}, ' \
                                    f'fg_intent_id:{fg_intent_id}')
                            if curr_priority > sel_priority:
                                is_valid = True
                            elif curr_priority == sel_priority:
                                if report["intent_id"] == fg_intent_id:
                                    if curr_report_time < sel_report_time:
                                        is_valid = True
                                elif curr_intent_id == sel_intent_id:
                                    if curr_report_time < sel_report_time:
                                        is_valid = True
                                else:
                                    if curr_intent_type <= sel_intent_type and curr_intent_type != 1:
                                        if curr_intent_type == sel_intent_type:
                                            if curr_report_time < sel_report_time:
                                                is_valid = True
                                        else:
                                            is_valid = True
                        else:
                            is_valid = True

                        if is_valid:
                            next_report = report
                            next_idx = report_idx

                if not has_sent_msg:
                    if next_idx >= 0:
                        report_list[next_idx]["status"] = "sent"
                        next_report = report_list[next_idx]

                self.set_report_list(user_id, global_conversation_id, "0", report_list)
                waiting_report_list = [r for r in report_list if r["status"] in ["sent", "waitret"]]
            finally:
                report_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting report lock: {lock_key}')

        # 消息不连续，此时需要提示report的上下文
        if not is_talk_off and not has_sent_msg and next_report and next_report["intent_id"] != fg_intent_id and \
                fg_intent_id and fg_intent_id != "0":
            last_intent_type = self.intent_info[fg_intent_id]["intent_type"]
            last_intent_name = self.intent_info[fg_intent_id]["name"]
            last_intent_in_stack = 0
            if fg_intent_id in intent_status:
                last_intent_in_stack = intent_status[fg_intent_id].get("in_stack", 0)

            if next_report["intent_id"] in self.intent_info:
                intent_type = self.intent_info[next_report["intent_id"]]["intent_type"]
                intent_name = self.intent_info[next_report["intent_id"]]["name"]
                intent_in_stack = 0
                if next_report["intent_id"] in intent_status:
                    intent_in_stack = intent_status[next_report["intent_id"]].get("in_stack", 0)

                # 新任务在历史stack中，刚刚被唤醒，且不是单轮任务，则执行改写逻辑
                if intent_in_stack > 0 and last_intent_type > 0:
                    hist_report_str = ""
                    hist_report_idx = 1

                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] fg_intent:{fg_intent_id}, intent_id:' \
                            f'{next_report["intent_id"]}')
                    for report_idx, report in enumerate(report_list):
                        if report["status"] in {"sent", "finish"} and report["intent_id"] == intent_id:
                            hist_report_str = f'{hist_report_str} {hist_report_idx}.{report["ret_output"]}'
                            hist_report_idx += 1

                    custom_var = {"hist_ret": hist_report_str, "new_ret": next_report["ret_output"], "uid": user_id}
                    code, err_msg, comb_msg, model_name, prompt_token, output_token, time_cost = self.report_switch_bot.chat(
                            req_id, user_id, global_conversation_id, next_report["ret_output"], custom_var, False)
                    if code != 0 or not comb_msg:
                        logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] fetal error, combining report for ' \
                                f'user:{user_id} in intent:{intent_id}, code:{code}, err_msg:{err_msg}')
                    else:
                        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] rewrite report msg, old:{next_report["ret_output"]}, new: {comb_msg}')
                        next_report["ret_output"] = comb_msg

        return next_report, waiting_report_list

    def get_report_priority(self, report_source, report_intent_id):
        priority = self.source_priority_dict["default"]
        report_intent_type = 0
        if report_source in self.source_priority_dict:
            priority = self.source_priority_dict[report_source]
        if report_intent_id in self.intent_info:
            report_intent_type = self.intent_info[report_intent_id]["intent_type"]

        return priority, report_intent_type

    def push_report(self, req_id, coords, user_id, intent_id, scene_id, scene_sub_id, report_msg, report_detail, report_status, 
            report_conversation_id, global_conversation_id, talk_cmd, mic_cmd, report_source, report_type, exp_time, 
            need_lock = True, del_list = [], report_role = "default", with_ack=True):
        if report_msg or len(report_detail) > 2:
            accessible = False
            if need_lock:
                lock_key = f"{user_id}_{global_conversation_id}_0_report"
                report_lock = RedisLock(self.db_client, lock_key)
                accessible = report_lock.acquire()
            else: accessible = True

            if not accessible:
                logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting report lock: {lock_key}')
                return 0

            try:
                ts = time.time()
                report_list = self.get_report_list(user_id, global_conversation_id, "0")
                for report_idx, report_item in enumerate(report_list):
                    if report_item["intent_id"] in del_list and report_list[report_idx]["status"] in {"waitret"}:
                        report_list[report_idx]["status"] = "cancel"

                curr_exp_time = exp_time

                if "dialog" in report_msg:
                    output_list = json.loads(report_msg)
                    for item_idx, item in enumerate(output_list):
                        if item["dialog"]:
                            #logger.debug(f'###### item:{item}, type:{type(item)}')
                            curr_report_msg = item["dialog"]
                            curr_report_detail = ""
                            curr_report_status = "0"
                            curr_role = item["role"]
                            curr_talk_cmd = "on"
                            curr_mic_cmd = "off"
                            if item_idx == len(output_list) - 1:
                                curr_report_detail = report_detail
                                curr_report_status = report_status
                                curr_talk_cmd = talk_cmd
                                curr_mic_cmd = mic_cmd

                            report_list.append({
                                "ret_output":curr_report_msg, "ret_detail_info":curr_report_detail,
                                "ret_status":curr_report_status, "intent_id":intent_id,
                                "status":"waitret", "req_id":req_id,
                                "ret":"", "conversation_id":report_conversation_id, "coords": coords,
                                "global_conversation_id": global_conversation_id,
                                "time":ts, "role": curr_role, "talk_cmd": curr_talk_cmd,
                                "mic_cmd": curr_mic_cmd, "scene_id": scene_id,
                                "scene_sub_id": scene_sub_id, "report_type": report_type,
                                "report_source": report_source, "exp_time": curr_exp_time, "with_ack": with_ack
                            })
                else:
                    curr_report_msg = report_msg
                    curr_report_detail = report_detail
                    curr_report_status = report_status
                    curr_role = report_role
                    curr_talk_cmd = talk_cmd
                    curr_mic_cmd = mic_cmd

                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] push report in queue, msg:[{curr_report_msg}]')
                    report_list.append({
                        "ret_output":curr_report_msg, "ret_detail_info":curr_report_detail, "coords": coords,
                        "ret_status":curr_report_status, "intent_id":intent_id, "status":"waitret", "req_id":req_id,
                        "ret":"", "conversation_id":report_conversation_id, "global_conversation_id": global_conversation_id,
                        "time":ts, "role": curr_role, "talk_cmd": curr_talk_cmd, "mic_cmd": curr_mic_cmd,
                        "scene_id": scene_id, "scene_sub_id": scene_sub_id, "report_type": report_type,
                        "report_source": report_source, "exp_time": curr_exp_time, "with_ack": with_ack
                    })

                self.set_report_list(user_id, global_conversation_id, "0", report_list)
            finally:
                if need_lock: report_lock.release()
        return 0

    def push_report_with_merge(self, req_id, coords, user_id, intent_id, scene_id, scene_sub_id, report_msg, report_detail, 
            report_status, report_conversation_id, global_conversation_id, valid_task_set, talk_cmd, mic_cmd, report_type, 
            report_source, exp_time):
        comb_msg = report_msg
        del_list = []
        lock_key = f"{user_id}_{global_conversation_id}_0_report"
        report_lock = RedisLock(self.db_client, lock_key)
        if report_lock.acquire():
            try:
                if self.need_report:
                    report_list = self.get_report_list(user_id, global_conversation_id, "0")
                    #hist_report_list = []
                    hist_report_str = ""
                    hist_report_idx = 1
                    for report_idx, report in enumerate(report_list):
                        if report["req_id"] in valid_task_set and report["status"] == "waitret" and report["intent_id"] == intent_id:
                            #hist_report_list.append(report)
                            del_list.append(report["req_id"])
                            hist_report_str = f'{hist_report_str} {hist_report_idx}.{report["ret_output"]}'
                            hist_report_idx += 1

                    custom_var = {"hist_ret": hist_report_str, "uid": user_id}
                    code, err_msg, comb_msg, model_name, prompt_token, output_token, time_cost = self.report_merge_bot.chat(
                            report_msg, custom_var, False)
                    if not(code == 0) or not(comb_msg):
                        logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] fetal error, combining report for ' \
                                f'user:{user_id} in intent:{intent_id}, code:{code}, err_msg:{err_msg}')
                        return -1

                logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] push report in queue, msg:[{comb_msg}], valid_task_set:{valid_task_set}')
                #if req_id in valid_task_set:
                self.push_report(req_id, coords, user_id, intent_id, scene_id, scene_sub_id, comb_msg, report_detail,
                    report_status, report_conversation_id, global_conversation_id, talk_cmd, mic_cmd, report_type,
                    report_source, exp_time, False, del_list)
            finally:
                report_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting report lock: {lock_key}')

        return 0

    def finish_req_report(self, req_id, user_id, global_conversation_id, intent_id):
        lock_key = f"{user_id}_{global_conversation_id}_0_report"
        report_lock = RedisLock(self.db_client, lock_key)
        if report_lock.acquire():
            try:
                logger.debug(f'reqid:{req_id}, uid:{user_id}, global_conversation_id:{global_conversation_id},intent:{intent_id}')
                report_list = self.get_report_list(user_id, global_conversation_id, "0")
                for report_idx, report in enumerate(report_list):
                    # logger.debug(f'input req: {req_id}, report req: {report["req_id"]}, status: {report["status"]}, msg:{report["ret_output"]}')
                    if report["req_id"] == req_id and report["status"] == "sent":
                        report_list[report_idx]["status"] = "finish"
                self.set_report_list(user_id, global_conversation_id, "0", report_list)
            finally:
                report_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting report lock: {lock_key}')

        return 0

    def clear_req_report(self, user_id, global_conversation_id, intent_id, req_id):
        lock_key = f"{user_id}_{global_conversation_id}_0_report"
        report_lock = RedisLock(self.db_client, lock_key)
        if report_lock.acquire():
            try:
                report_list = self.get_report_list(user_id, global_conversation_id, "0")
                for report_idx, report in enumerate(report_list):
                    if report["req_id"] == req_id and report["intent_id"] == intent_id and report["status"] == "waitrun":
                        report_list[report_idx]["status"] = "cancel"
                self.set_report_list(user_id, global_conversation_id, "0", report_list)
            finally:
                report_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting report lock: {lock_key}')

        return 0

    def clear_global_report(self, user_id, global_conversation_id, intent_info):
        logger.info(f'[][{user_id}][{global_conversation_id}] All report info cleared')
        for key, val in intent_info.items():
            self.clear_intent_report(user_id, global_conversation_id, key)
        return 0

    def clear_intent_report(self, user_id, global_conversation_id, intent_id):
        # logger.debug(f'[][{user_id}][{global_conversation_id}] Clearing reports in intent:{intent_id}')
        # old_report_list = self.get_report_list(user_id, global_conversation_id, intent_id)
        self.del_report_list(user_id, global_conversation_id, intent_id)
        # new_report_list = self.get_report_list(user_id, global_conversation_id, intent_id)
        # logger.debug(f'[][{user_id}][{global_conversation_id}] intent_id:{intent_id}, report list cleared.')
        # logger.debug(f'[][{user_id}][{global_conversation_id}] intent_id:{intent_id}, new report list: {new_report_list}')
        return 0

if __name__ == '__main__':
    gsetting_dict = {"redis_ip":"127.0.0.1", "redis_port":"16379", "redis_user":"default", "redis_pass":"Spotlight321@"}
    info_agent = ReportAgent(gsetting_dict)
    task_msg = " 附近有肯德基吗？"
    user_id = sys.argv[1] #"test1"
    #user_id = "('**************', 63381)"
    #user_id = "24542782532"
    req_id = "23dshfskjdf"
    conversation_id = "2342343"
    ts = time.time()
    
    #task_list = [{"msg":task_msg, "intent_id":intent_id, "status":"running", "req_id":req_id, "conversation_id":conversation_id, "time":ts}]
    #self.set_task_list(user_id, intent_id, task_list)

    # update conversation_id & chat hist & profile
    info_agent.clear_global_report(user_id)

    print("All profile printed")
