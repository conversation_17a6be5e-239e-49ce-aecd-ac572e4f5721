# coding=utf-8

import redis
import time
import json
import sys
import structlog

logger = structlog.get_logger(__name__)

class StatusAgent():
    def __init__(self, global_setting_dict):
        self.db_client = redis.StrictRedis(host=global_setting_dict["redis_ip"], port=global_setting_dict["redis_port"], username=global_setting_dict["redis_user"], password=global_setting_dict["redis_pass"])
        self.status_expire_time = 300

    def get_status(self, user_id, global_conversation_id):
        profile_key = f'{user_id}_profile'
        item_key =  f'{global_conversation_id}_client_status'
        status_str = self.db_client.hget(profile_key, item_key)
        self.db_client.expire(profile_key, self.status_expire_time)
        speak_status = ""

        if status_str:
            status_str = status_str.decode('utf-8')
            status_dict = json.loads(status_str)
            speak_status = status_dict["speak_status"]

        return speak_status

    def set_status(self, user_id, global_conversation_id, speak_status = ""):
        old_speak_status = self.get_status(user_id, global_conversation_id)

        profile_key = f'{user_id}_profile'
        item_key = f'{global_conversation_id}_client_status'
        if not speak_status: speak_status = old_speak_status
        status_str = f'{{"speak_status": "{speak_status}"}}'

        self.db_client.hset(profile_key, item_key, status_str)
        self.db_client.expire(profile_key, self.status_expire_time)
        return 0

    def del_status(self, user_id, global_conversation_id):
        profile_key = f'{user_id}_profile'
        item_key =  '{global_conversation_id}_client_status'
        self.db_client.hdel(profile_key, item_key)
        return 0

    def clear_global_status(self, user_id, global_conversation_id):
        logger.info(f'[][{user_id}][{global_conversation_id}] All status info cleared')
        self.del_status(user_id, global_conversation_id)

if __name__ == '__main__':
    status_agent = StatusAgent(ip = "127.0.0.1", port = 16379, user = "default", pwd = "Spotlight321@")
    user_id = sys.argv[1] #"test1"
    
    status_agent.clear_global_status(user_id)
    print("All profile printed")
