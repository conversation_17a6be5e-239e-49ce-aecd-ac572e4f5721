# coding=utf-8

from vui_svr.redis_lock import RedisLock
from vui_svr.cond_matcher import cond_matcher
from vui_svr.inv_matcher import inv_matcher
from vui_svr.task_post import *
from vui_svr.gpt_api_V4 import GPTAgent

import redis
import time
import json
import sys
import structlog
import os


logger = structlog.get_logger(__name__)

class TaskAgent():
    def __init__(self, global_setting_dict, task_bots, intent_info, info_agent):
        self.db_client = redis.StrictRedis(host=global_setting_dict["redis_ip"], port=global_setting_dict["redis_port"], username=global_setting_dict["redis_user"], password=global_setting_dict["redis_pass"])
        self.task_expire_time = 300
        self.task_bots = task_bots
        self.source_priority_dict = {"push": 0, "time": 0, "task": 1, "active": 2, "default": 3}
        self.intent_info = intent_info
        self.info_agent = info_agent

        self.task_rule_matcher = cond_matcher()
        self.task_rule_matcher.init_matcher(intent_info, global_setting_dict["task_rule_path"])

        self.param_inv_matcher = inv_matcher()
        self.param_inv_matcher.init_matcher(global_setting_dict["param_inv_path"]) 

    def get_task_list(self, user_id, global_conversation_id):
        task_key = f'{user_id}_task'
        item_key = f'{global_conversation_id}_0'
        task_list_str = self.db_client.hget(task_key, item_key)
        self.db_client.expire(task_key, self.task_expire_time)
        if task_list_str:
            task_list_str = task_list_str.decode('utf-8')
            task_list = json.loads(task_list_str)
        else:
            task_list = []
        return task_list

    def set_task_list(self, user_id, global_conversation_id, task_list):
        task_key = f'{user_id}_task'
        item_key = f'{global_conversation_id}_0'
        task_list_str = json.dumps(task_list)
        self.db_client.hset(task_key, item_key, task_list_str)
        self.db_client.expire(task_key, self.task_expire_time)
        return 0

    def get_target_task(self, user_id, global_conversation_id, target_status, target_msg = ""):
        task_list = self.get_task_list(user_id, global_conversation_id)
        ret_task_list = []
    
        for item in task_list:
            if (target_msg == "" or item["msg"] == target_msg) and (item["status"] in target_status or not target_status):
                ret_task_list.append(item)
        return ret_task_list

    def push_task_list(self, user_id, intent_id, new_task, discard_task_str, global_conversation_id, req_id):
        if not discard_task_str:
            discard_task_str = ""

        discard_tasks = discard_task_str.split(",")
        discard_task_map = {item for item in discard_tasks}
        ts = time.time()

        task_lock_key = f"{user_id}_{global_conversation_id}_0_task"
        task_lock = RedisLock(self.db_client, task_lock_key)
        if task_lock.acquire():
            try:
                task_list = self.get_task_list(user_id, global_conversation_id)
                for item_idx, item in enumerate(task_list):
                    if item["req_id"] in enumerate(discard_task_map):
                        task_list[item_idx]["status"] = "cancel"
                        task_list[item_idx]["time"] = ts

                task_list.append(new_task)
                self.set_task_list(user_id, global_conversation_id, task_list)
            finally:
                task_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting task lock: {task_lock_key}')

        return 0

    def get_task_priority(self, task_source, task_intent_id):
        priority = self.source_priority_dict["default"]
        task_intent_type = 0
        if task_source in self.source_priority_dict:
            priority = self.source_priority_dict[task_source]
        if task_intent_id in self.intent_info:
            task_intent_type = self.intent_info[task_intent_id]["intent_type"]

        return priority, task_intent_type

    def get_top_task(self, user_id, req_id, global_conversation_id, fg_intent_source, fg_intent_id):
        task_list = self.get_task_list(user_id, global_conversation_id)
        has_running_task = False
        resp_msg = ""
        talk_cmd = ""
        mic_cmd = ""
        sel_task_dict = {}
        intent_status = self.info_agent.get_intent_status(user_id, global_conversation_id)

        # 每个意图内部，按task时间排序
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] old task list: {task_list}')
        for idx, task in enumerate(task_list):
            if task["status"] != "cancel":
                if task["status"] == "running": has_running_task = True
                if task["status"] == "waitrun":
                    if task["intent_id"] not in sel_task_dict or task["time"] < sel_task_dict[task["intent_id"]]["time"]:
                        sel_task_dict[task["intent_id"]] = task

        # 不同task之间，get highest priority task, 参考来源，任务栈，任务生成时间
        selected_task = None
        for task_intent_id, task_item in sel_task_dict.items():
            new_task_intent_id = task["intent_id"]
            new_task_source = task["task_source"]
            new_task_time = task["time"]
            new_task_status = task["status"]
            new_task_p, new_task_intent_type = self.get_task_priority(new_task_source, new_task_intent_id)
            is_valid = True

            if selected_task:
                curr_task_source = selected_task["task_source"]
                curr_task_time = selected_task["time"]
                curr_task_p, curr_task_intent_type = self.get_task_priority(curr_task_source, new_task_intent_id)

                if new_task_p < curr_task_p:            # 新任务 source 优先级低
                    is_valid = False
                elif new_task_p == curr_task_p:
                    if new_task_status == "wait_ret":
                        if new_task_time > curr_task_time:    # 新任务 启动时间 靠后
                            is_valid = False

            if is_valid:
                selected_task = task_item
        
        # 比对 最高优先级task 和当前 task
        if not has_running_task and selected_task:
            task_intent_id = selected_task["intent_id"]
            task_scene_id = selected_task["scene_id"]
            task_source = selected_task["task_source"]
            task_intent_name = self.intent_info[task_intent_id]["name"]
            fg_intent_name = ""
            if fg_intent_id in self.intent_info: fg_intent_name = self.intent_info[fg_intent_id]["name"]

            source_task_priority, source_task_intent_type = self.get_task_priority(fg_intent_source, fg_intent_id)
            new_task_priority, new_task_intent_type = self.get_task_priority(task_source, task_intent_id)

            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] task priority info, fg_intent:{fg_intent_id}' \
                    f'fg_source:{fg_intent_source}, fg_intent_priority:{source_task_priority}, new_intent:{task_intent_id}' \
                    f'new_source: {task_source}, new_task_priority:{new_task_priority}')

            if fg_intent_id != "0" and fg_intent_id in sel_task_dict:
                # 新task优先级更高或同级别间允许插入，则执行新任务，否则执行当前类型任务
                if new_task_priority > source_task_priority:
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new task priority is high, insert')
                elif new_task_priority == source_task_priority:
                    if new_task_intent_type <= source_task_intent_type and new_task_intent_type != 1:
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] task priority hit, can be insert')
                    else:
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] task priority hit, not insert, use fgintent')
                        selected_task = sel_task_dict[fg_intent_id]
                else:
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new task priority is low, hang')
                    sel_task_dict[fg_intent_id]

            # 如果执行新任务，则确定提示语和端侧命令
            if selected_task:
                if task_intent_id == fg_intent_id or not fg_intent_id or fg_intent_id == "0":
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new_intent is same or fg_intent is 0')
                else:
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new_intent is diff from fg_intent')
                    #resp_msg = f"好，等一下，我先帮你{task_intent_name}"
                    #if fg_intent_name:
                    #    resp_msg = f"好，我们等一下再{fg_intent_name}，我先帮你{task_intent_name}。"
                    resp_msg = ""
                    talk_cmd = "on"
                    mic_cmd = "off"
            else:
                logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] no task generate')
        else:
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] task already running, wait')
            selected_task = None

        return selected_task, resp_msg, talk_cmd, mic_cmd

    def get_task_item(self, user_id, global_conversation_id, intent_id, req_id):
        task_list = self.get_task_list(user_id, global_conversation_id)
        for task in task_list:
            if task["req_id"] == req_id and task["intent_id"] == intent_id:
                return task
        return None

    def set_task_param(self, user_id, intent_id, req_id, status, global_conversation_id, fg_intent_id, chat_id = "", retry = -1):
        task_lock_key = f"{user_id}_{global_conversation_id}_0_task"
        task_lock = RedisLock(self.db_client, task_lock_key)
        new_task = None

        if task_lock.acquire():
            try:
                task_list = self.get_task_list(user_id, global_conversation_id)
                for idx, task in enumerate(task_list):
                    if task["req_id"] == req_id and task["intent_id"] == intent_id:
                        if status:
                            task_list[idx]["status"] = status
                            if status == "running":             # 设置当前任务开始运行，把当前intent的遗留任务加入等待队列
                                task_status_dict = {intent_id: {"in_stack": 0, "is_fg": 1}, fg_intent_id: {"in_stack": 1,"is_fg": 0}}
                                self.info_agent.set_intent_status(user_id, global_conversation_id, [intent_id], [], task_status_dict)

                            elif status == "finish": pass       # 设置任务完成后结束
                            elif status == "cancel": pass       # 设置任务取消后结束
                            elif status == "fail": pass         # 设置任务失败后结束
                            elif status == "waitrun": pass      # 设置任务排队后结束
                            elif status == "pause": pass        # 设置任务暂停后结束
                            elif status == "wait_ret": pass     # 设置任务等待确认后结束
                                
                        if chat_id:
                            task_list[idx]["chat_id"] = chat_id
                        if retry >= 0:
                            task_list[idx]["retry"] = retry
                        new_task = task_list[idx]
                self.set_task_list(user_id, global_conversation_id, task_list)
            finally:
                task_lock.release()
        else:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting task lock: {task_lock_key}')
        return new_task

    def clear_global_task(self, user_id, global_conversation_id, intent_info):
        logger.info("[][{user_id}][global_conversation_id] All task info cleared")
        for key, val in intent_info.items():
            self.clear_intent_task(user_id, global_conversation_id, key)

    def clear_intent_task(self, user_id, global_conversation_id, intent_id):
        # logger.debug(f"[][{user_id}][global_conversation_id] Clearing tasks in intent:{intent_id}")
        old_task_list = self.get_task_list(user_id, global_conversation_id)
        self.set_task_list(user_id, global_conversation_id, [])
        # self.db_client.delete(f"{user_id}_{intent_id}_task")
        new_task_list = self.get_task_list(user_id, global_conversation_id)
        # logger.debug(f"old task list: {old_task_list}")
        # logger.debug(f"new task list: {new_task_list}")

    def norm_time(self, val):
        norm_val = int(val*1000)/1000
        return f'{norm_val}'

    def get_act_task(self, req_id, user_id, intent_id, coords, chat_conversation_id, ori_chat_msg, hist_chat, intent_hist_chat, 
            fast_ret, intent_task_msg, intent_task_outvar, intent_task_sceneid, global_conversation_id, fg_intent_id, 
            fg_intent_source, task_source):
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] get_act_task start, intent_id:{intent_id}, ' \
                f'conversation_id:{chat_conversation_id}, msg:{ori_chat_msg}, hist_chat:{hist_chat}, ' \
                f'intent_hist_chat:{intent_hist_chat}, fast_ret:{fast_ret}, intent_task_msg:{intent_task_msg}, ' \
                f'intent_task_outvar:{intent_task_outvar}, intent_task_sceneid:{intent_task_sceneid}, task_source:{task_source}')
        time_start = time.time()
        run_details = {"time_cost": 0, "time_details": {}, "prompt_token":0, "output_token":0, "token_details": {}}

        code = 0
        err_msg = ""
        resp_msg = ""

        last_scene_id = intent_task_outvar.get("scene_id", "-1")
        last_scene_sub_id = intent_task_outvar.get("scene_sub_id", "-1")

        scene_id = intent_task_sceneid
        new_task_msg = intent_task_msg
        discard_task_set = {}
        new_task_vars = intent_task_outvar

        logger.debug(f'[{req_id}] enter get_act_task, creating tasks for user:{user_id} in intent:{intent_id}, ' \
                f'rule_task:{intent_task_msg}, last_scene_id:{last_scene_id}, last_scene_sub_id:{last_scene_sub_id}')
        if not intent_task_msg:                         # intent no task, need LLM
            task_list = self.get_target_task(user_id, global_conversation_id, {"running"}, "")
            task_list_str = ""
            new_task_str_list = [item["msg"] for item in task_list if type(item["msg"]) == str]
            if len(new_task_str_list) > 0:
                task_list_str = ",".join(new_task_str_list)

            # merge tasks
            last_task_msg = f"用户:{ori_chat_msg}"
            task_msg = last_task_msg
            if len(intent_hist_chat) > 0:
                task_msg = f"{intent_hist_chat}"
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] Task LLM req start, task, intent_id:{intent_id}, '\
                    f'task_msg:{task_msg}')
            custom_var = {"uid": user_id, "coords": coords, "intent_id": intent_id, "running_tasks": task_list_str, 
                    "last_input": last_task_msg}

            code, err_msg, task_ret_msg, model_name, prompt_token, output_token, time_cost = self.task_bots[intent_id].chat(
                    req_id, user_id, global_conversation_id, task_msg, custom_var)

            # 更新提取出的参数
            for var_key, var_val in task_ret_msg.get("out_vars", {}).items():
                task_ret_msg["out_vars"][var_key] = self.param_inv_matcher.match(req_id, user_id, global_conversation_id, 
                        intent_id, var_key, var_val)

            # 参数后处理
            scene_id = task_ret_msg["scene_id"]
            if intent_id == "10002":
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] rule_post_luckin, LLM-scene_id:{scene_id}, '\
                    f'LLM out_vars:{task_ret_msg["out_vars"]}')
                scene_id, task_ret_msg = rule_post_luckin(scene_id, "-1", task_ret_msg)
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] rule_post_luckin, post scene_id:{scene_id}, '\
                    f'post out_vars:{task_ret_msg["out_vars"]}')
          
            # 更新scene_id
            scene_id = self.task_rule_matcher.match(req_id, user_id, global_conversation_id, intent_id, task_ret_msg["out_vars"], 
                    scene_id, last_scene_id, last_scene_sub_id)
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] cond match, cond scene_id:{scene_id}')
            ori_scene_id = task_ret_msg["scene_id"]
            task_ret_msg["scene_id"] = scene_id

            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] Task LLM req end, ori_scene_id:{ori_scene_id}, ' \
                    f'new_scene_id:{scene_id}, output:{task_ret_msg}, ' \
                    f'model_name:{model_name}, prompt_token:{prompt_token}, output_token:{output_token}, ' \
                    f'time_cost:{self.norm_time(time_cost)}')
            run_details["token_details"]["task_build"] = {"model": model_name, "prompt_token":prompt_token, 
                    "output_token":output_token}
            run_details["prompt_token"] += prompt_token
            run_details["output_token"] += output_token
            run_details["time_details"]["task_build"] = self.norm_time(time_cost)

            if not(code == 0) or (len(task_msg) == 0):
                resp_msg = "抱歉，我没理解你的要求，请再说一遍？"
                #return code, err_msg, resp_msg, {}, discard_task_set, new_task_vars
            else:
                logger.debug(f'taskbot ret: {task_ret_msg}')
                # discard task & add newtask & update
                new_task_msg = task_ret_msg["new_task"]
                discard_task_set = task_ret_msg.get("discard_task", "")
                #new_task_vars = task_ret_msg["out_vars"]
                for var_key, var_val in task_ret_msg["out_vars"].items():
                    #if type(var_val) != str:
                    new_task_vars[var_key] = var_val
                scene_id = task_ret_msg["scene_id"]
            
        ts = time.time()
        curr_task = {"msg":new_task_msg, "intent_id":intent_id, "status": "waitrun", "req_id":req_id, "coords":coords, 
                "conversation_id":chat_conversation_id, "global_conversation_id": global_conversation_id, "time":ts, 
                "hist_chat":hist_chat, "intent_hist_chat":intent_hist_chat, "retry": 0, "fast_ret": fast_ret, "chat_id": "", 
                "out_vars": new_task_vars, "scene_id": scene_id, "app_id": "", "task_source": task_source, "ask_cnt": 0} 

        time_end = ts
        run_details["time_cost"] = self.norm_time(time_end - time_start)
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] get_act_task end, intent_id:{intent_id}, ' \
                f'conversation_id:{chat_conversation_id}, curr_task:{curr_task}, run_details:{run_details}')
        return code, err_msg, resp_msg, curr_task, discard_task_set, new_task_vars, run_details

    def cancel_all_task(self, req_id, user_id, global_conversation_id, action_bots):
        all_task_list = self.get_task_list(user_id, global_conversation_id)
        cancel_task_list = [item for item in all_task_list if item["status"] == "running"]
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] kill task list:{cancel_task_list}')

        for task_item in cancel_task_list:
            intent_id = task_item["intent_id"]
            target_conversation_id = task_item["conversation_id"]
            target_chat_id = task_item["chat_id"]

            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] kill task, intentid:{intent_id}, ' 
                    f'chatid:{target_chat_id}')
            workflow_cancel_code, workflow_cancel_msg = action_bots[intent_id].kill_chat(req_id, user_id, 
                    global_conversation_id, target_conversation_id, target_chat_id)
        return 0

    def cancel_task(self, req_id, user_id, global_conversation_id, intent_id, action_bot, plugin_req_agent):
        all_task_list = info_agent.get_task_list(user_id, global_conversation_id)
        intent_task_reqid_list = [item for item in all_task_list if item["status"] == "running" and item["intent_id"] == intent_id]

        target_req_msg = intent_task_reqid_list[0]["msg"]
        target_req_id = intent_task_reqid_list[0]["req_id"]
        target_conversation_id = intent_task_reqid_list[0]["conversation_id"]
        target_chat_id = intent_task_reqid_list[0]["chat_id"]
        target_app_id = intent_task_reqid_list[0]["app_id"]
        target_coords = intent_task_reqid_list[0]["coords"]

        # do cancel, step1: cancel coze workflow
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] kill task, reqid:{target_req_id}, ' \
                f'conversation_id:{target_conversation_id}, target_chat_id:{target_chat_id}')
        workflow_cancel_code, workflow_cancel_msg = action_bot.kill_chat(req_id, user_id, global_conversation_id, 
                target_conversation_id, target_chat_id)
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] coze workflow canceled, code:{workflow_cancel_code}, ' \
                f' msg:{workflow_cancel_msg}')

        plugin_cancel_status = None
        plugin_cancel_code = 0
        ret_msg = ""
        if workflow_cancel_code == 0:
            # do cancel, step2: cancel plugin
            plugin_context = f'{{"uid": "{user_id}", "conversation_id": "{target_conversation_id}", ' \
                    f'"req_id": "{target_req_id}", "coords": "{target_coords}", "app_id":"{target_app_id}"}}'
            plugin_context_txt = json.dumps(plugin_context)
            content = {"req_id_to_stop": target_req_id, "context": f'{plugin_context_txt}'}
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] target_req_id: {target_req_id}, ' \
                    f'target_req_msg: {target_req_msg}')
            resp_info = plugin_req_agent.post_request("agent-api/phone/stop_task", content, {}, "0")
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] plugin_req_agent resp_info:{resp_info}, ' \
                    f'type:{type(resp_info)}')
            plugin_cancel_status = resp_info["status"]
            plugin_cancel_code = resp_info["code"]
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] cancel_status:{plugin_cancel_status}, ' \
                    f'cancel_code:{plugin_cancel_code}')

        if not plugin_cancel_status or workflow_cancel_code != 0:           # cancel fail
            if workflow_cancel_code != 0:
                ret_msg = "抱歉，任务已经执行完了，取消失败"
            else:
                if plugin_cancel_code == 1001:         # task complete, can not be cancel
                    ret_msg = "抱歉，任务已经执行完了，没法停止"
                elif plugin_cancel_code == 1002:       # task can not be cancel
                    ret_msg = "抱歉，这个任务已经在执行，停不下来哈"
        else:                                                               # cancel success
            ret_msg = "任务已取消"
        return workflow_cancel_code, workflow_cancel_msg, plugin_cancel_status, plugin_cancel_code, ret_msg

if __name__ == '__main__':
    global_setting_dict = {}
    intent_info = {}
    task_bots = {}
    file_path = sys.argv[1]
    logger.basicConfig(level = logger.DEBUG)
    logger = logger.getLogger(__name__)
    logger.setLevel(logger.DEBUG)
    with open("./global_setting.cmdp.offline", 'r', encoding='utf-8') as file:
        global_setting_dict = json.load(file)

    coze_auth = global_setting_dict["coze_auth"]
    task_bots["10002"] = GPTAgent("task_10002", "task_take_10002", "deepseek-vc", global_setting_dict)
    with open("./intent_config.cmdp", 'r', encoding='utf-8') as file:
        intent_info = json.load(file)
    info_agent = None

    task_agent = TaskAgent(global_setting_dict, task_bots, intent_info, info_agent)
    req_id = "111"
    user_id = "1900437112298143744"
    intent_id = "10002"
    coords = ""
    chat_conversation_id = "123"
    global_conversation_id = "123"
    
    intent_task_outvar = {}
    intent_task_sceneid = -1
    fg_intent_id = -1
    fg_intent_source = "task"
    task_source = "task"

    file_list = []
    if os.path.isdir(file_path):
        for root, dirs, files in os.walk(file_path):
            for file in files:
                file_list.append(os.path.join(root, file))
    else:
        file_list.append(file_path)
        logger.debug(f'filecnt:{len(file_list)}')

    for file_item in file_list:
        logger.debug(f'Loading case file: {file_item}')
        with open(file_item, "r") as file:
            hist_msg_list = []
            for line in file:
                line = line.strip()
                pieces = line.split("\t")

                role = "你"
                if pieces[0] == "user": role = "用户"
                hist_msg_list.append(f'{role}:{pieces[1]}')

                if pieces[0] == "user":
                    last_msg = f'{role}:{pieces[1]}'
                    hist_msg_str = "; ".join(hist_msg_list)

                    hist_chat = hist_msg_str
                    intent_hist_chat = hist_msg_str
                    ori_chat_msg = pieces[1]
                    fast_ret = "好的，稍等"
                    intent_task_msg = ""

                    code, err_msg, resp_msg, new_task_item, discard_task_set, new_task_vars, build_task_run_details = task_agent.get_act_task(req_id, user_id, intent_id, coords, chat_conversation_id, ori_chat_msg, hist_chat, 
                            intent_hist_chat, fast_ret, intent_task_msg, intent_task_outvar, intent_task_sceneid, 
                            global_conversation_id, fg_intent_id, fg_intent_source, task_source)

                    for param_key, param_val in new_task_vars.items():
                        intent_task_outvar[param_key] = param_val
                    intent_task_outvar["scene_id"] = new_task_item["scene_id"]

                    ret = "HIT"
                    rule_list = pieces[2].split(",")
                    match_list = []
                    is_hit = False
                    for rule_item in rule_list:
                        arr = rule_item.split(":")
                        answer_val = new_task_item 
                        path_list = arr[0].split("#")
                        for path_piece in path_list:
                            answer_val = answer_val[path_piece]
                        if str(answer_val) != str(arr[1]): ret = "MISS"
                        match_list.append(f'{arr[0]}:{answer_val}/{arr[1]}')

                    match_list_str = ",".join(match_list)
                    print(f'########################[{ret}]{match_list_str} file:{file_item}, input:{hist_msg_str}, ' \
                        f'last_input:{last_msg}')
                    print(f'output: {new_task_item}')

            print(f'##########################################################')
