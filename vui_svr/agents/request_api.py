from json import JSONDecodeError

import requests
import datetime
import json
import structlog
import traceback
from urllib.parse import urljoin

from vui_svr.client_message import *  # ClientMessage, MESSAGE_MODAL_VOICE

logger = structlog.get_logger(__name__)

class RequestAgent:
    def __init__(self, ip, run_type):
        self.ip = ip
        self.run_type = run_type

    def post_request(self, path, msg, extra_headers, pro_ver_id, need_resp=True):
        req_url = urljoin(self.ip[int(pro_ver_id)], path)
        headers = {"Content-Type": "application/json", "appkey": "UMhmghBLK77BJyf5"}
        for k, v in extra_headers.items():
            headers[k] = v

        logger.debug(f"pro_ver_id:{pro_ver_id}, req_url:{req_url}")
        try:
            resp = requests.post(req_url, headers=headers, json=msg, timeout=90)
        except Exception:
            logger.exception(f"error in request, addr:{req_url}, msg:{msg}")
            return None

        if not resp.ok:
            logger.error(f"request to {req_url} failed, data:{msg}, code: {resp.status_code} txt:{resp.text}")

        try:
            return resp.json()
        except JSONDecodeError:
            logger.error(f"request to {req_url} not returns json, txt:{resp.text}")
            return resp.text

    def send_msg(self, text_msg_str, extra_headers, pro_ver_id):
        resp = None
        if self.run_type == "APP":
            if pro_ver_id == "0":
                resp = self.post_request(
                    "send-message-to-bridge",
                    text_msg_str,
                    extra_headers,
                    pro_ver_id,
                    False,
                )
            elif pro_ver_id == "1":
                resp = self.post_request(
                    "v2/llm_message", text_msg_str, extra_headers, pro_ver_id, False
                )
        elif self.run_type == "WEB":
            resp = self.post_request(
                "/chat/rec", text_msg_str, extra_headers, pro_ver_id, False
            )
        return resp

    # APP - 处理信息输出
    def send_message_to_client(
        self,
        req_id,
        uid,
        intent_id,
        coords,
        output,
        detail_info,
        status,
        role,
        talk_cmd,
        mic_cmd,
        extra_headers,
        pro_ver_id,
        action=None,
        with_ack=True,
        audio_blob_id="",
    ):
        if action is None:
            action = {"text": "确定", "name": "click", "reply": "确认支付"}

        logger.debug(
            f"[{req_id}][{uid}] enter send_message_to_client, {output}, {detail_info}"
        )
        text_msg = ClientMessage(uid, coords, 101)
        card_msg = ClientMessage(uid, coords, 201)
        talk_cmd_code = 0
        mic_cmd_code = 0
        conversation = {"req_id": req_id, "intent_id": intent_id}
        if talk_cmd == "on":
            talk_cmd_code = 1
        if mic_cmd == "on" and with_ack:
            mic_cmd_code = 1
        text_resp = None
        tpl_resp = None

        # if output and output not in {"NONE", "None", "none", "NULL", "null"}:
        if output and output.lower() not in {"none", "null"}:
            # logger.debug(f'######### enter send_message_to_client, {output}, type:{type(output)}')
            text_msg.set_cmd(talk_cmd_code, mic_cmd_code)
            text_msg.set_vocal_message(
                {
                    "data": output,
                    "tts": {"role": role["voice"]["voice_name"]},
                    "type": 101,
                    "with_ack": with_ack,
                    "audio_blob_id": audio_blob_id,  # uuid4 or ""
                    "conversation": conversation,
                    "action": {
                        "mic_state": mic_cmd_code,
                        "conversation_state": talk_cmd_code,
                    },
                }
            )
            text_msg_str = text_msg.dump()
            # logger.debug(f'[{req_id}] ver_id:{pro_ver_id}, msg:{text_msg_str}')
            text_resp = self.send_msg(text_msg_str, extra_headers, pro_ver_id)

        # logger.debug(f'######### before detail:{detail_info}, type:{type(detail_info)}')
        if detail_info and isinstance(detail_info, dict):
            title = detail_info.get("title", "")
            content = detail_info.get("content", "")
            url = detail_info.get("url", list())

            if "caption" in detail_info and "reply" in detail_info:
                action["text"] = detail_info["caption"]
                action["reply"] = detail_info["reply"]
                action["name"] = "click"
        else:
            logger.error(f"invalid detail:{detail_info}, type:{type(detail_info)}")
            title, content, url = "", "", []

        card_msg.set_cmd(talk_cmd_code, mic_cmd_code)
        card_msg.set_conversation(conversation)
        card_msg.add_tpl_message(title, content, url, action)
        # print(f'######### before card:{content}')
        card_msg_str = card_msg.dump()
        # print(f'######### {card_msg_str}, {len(url)}')
        if detail_info and (len(content) > 2 or len(url) > 0):
            tpl_resp = self.send_msg(card_msg_str)

        ret_info = []
        if text_resp:
            ret_info.append({"msg": text_msg_str, "type": "text"})
        if tpl_resp:
            ret_info.append({"msg": card_msg_str, "type": "tpl"})
        return ret_info


if __name__ == "__main__":
    plugin_req_agent = RequestAgent(ip="http://127.0.0.1:18080")
    user_id = "112"
    target_conversation_id = "123"
    req_id = "1"
    coords = "1,2"
    target_req_id = "2"

    plugin_context = f'{{"uid": "{user_id}", "conversation_id": "{target_conversation_id}", "req_id": "{req_id}", "coords": "{coords}", "app_id":"com.lucky.luckyclient"}}'
    plugin_context_str = json.dumps(plugin_context)
    content = {"req_id_to_stop": req_id, "context": f"{plugin_context}"}
    content_str = json.dumps(content)
    # print(content)
    plugin_req_agent.post_request("agent-api/phone/stop_task", content, False)
