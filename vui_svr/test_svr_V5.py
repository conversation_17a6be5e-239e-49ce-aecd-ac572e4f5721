from flask import Flask, request, jsonify
import threading
import uuid
import time
import json
import requests
from queue import Queue
from collections import deque

app = Flask(__name__)

class DeviceWorker(threading.Thread):
    def __init__(self, manager, device_info):
        super().__init__(daemon=True)
        self.manager = manager
        self.device_info = device_info
        self.current_test = None
        self.worker_summary = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'details': []
        }

    def run(self):
        test_id = self.manager.pop_tests(self.device_info["id"])
        while test_id:
            self._process_test(test_id)
            time.sleep(1)                   # sleep 1s
            test_id = self.manager.pop_tests(self.device_info["id"])
            
        logging.debug(f'Device {self.device_info["id"]}, test end, success: {self.worker_summary["passed"]}/{self.worker_summary["total"]}')
            
    def _process_test(self, test_id):
        test = self.manager.get_test(test_id)
        if not test:
            return
            
        logging.debug(f"Device {self.device_info['id']} starting test {test_id}")
        case_summary = {
            'total': len(test['steps']),
            'passed': 0,
            'status': True,
            'details': []
        }
        
        for step_idx in range(len(test['steps'])):
            result = self._execute_step(test_id, step_idx)
            case_summary['details'].append(result)
            if result['success']:
                case_summary['passed'] += 1
            else:
                case_summary["status"] = False
                break
        
        self.worker_summary["total"] += 1
        if case_summary["status"]:
            self.worker_summary["passed"] += 1
        else:
            self.worker_summary["failed"] += 1
        self.worker_summary["details"].append(case_summary)

        print(f"Device {self.device_info['id']}, testid:{test_id}, case summary:", case_summary)
        self.manager.complete_test(test_id)
        
    def _execute_step(self, test_id, step_idx):
        ret = {"details": {}, "success": False}
        current_step = step_idx
        while True:
            with test_manager.lock:
                test = test_manager.tests.get(test_id)
                if not test or not test['running']:
                    break
                steps = test["steps"]
                                                                                                
                if current_step >= len(steps):
                    break

            step_data = steps[current_step]
            callback_id = f"{test_id}-{current_step}"
                                                                                                                                                    
            # 注册回调关系
            with test_manager.lock:
                test_manager.record_callback(callback_id, test_id, current_step)

            # 发送请求到待测系统
            try:
                # 拼接new_msg请求
                #payload = {**step_data['request'], 'callback_id': callback_id}
                response = requests.post(target_system_url, json=payload, time_out=(3, 10))
                response.raise_for_status()
            except Exception as e:
                logging.error(f"Test {test_id} step {current_step} failed to send: {str(e)}")
                break

            # 等待回调或超时
            test['event'].wait(timeout=120)
                                                                                                                                                                                                                                                                                                    
            # 处理结果
            expected = step_data['answer']
            actual = test_manager.fetch_ret(callback_id)
          
            # Check结果是否OK
            ret["details"] = actual
            if "output" in actual and actual["output"] in expected:
                logging.debug(f"Test {test_id} step {current_step} ✅ PASSED")
                ret["success"] = True
            else:
                logging.debug(f"Test {test_id} step {current_step} ❌ FAILED (Expected: {expected}, Actual: {actual})")
            test['event'].clear()

       return ret

class TestManager:
    def __init__(self, test_config):
        self.devices = {}
        self.tests = {}
        self.pending_tests = deque()
        self.lock = threading.Lock()
        self.callback_map = {}
        self.results_dict = {}
       
    def start(self, test_config)
        for worker_idx, worker_item in enumerate(test_config["worker_list"]):
            device_info = worker_item
            device_id = worker_item["worker_id"]
            worker = DeviceWorker(self, device_info)
            self.devices[device_id] = worker
            worker.start()

    def create_test(self, steps):
        test_id = str(uuid.uuid4())
        with self.lock:
            self.tests[test_id] = {
                'steps': steps,
                'status': 'pending',
                'created_at': time.time(),
                'event': threading.Event(),
                'current_step': 0,
                'test_ret': True
            }
            self.pending_tests.append(test_id)
        return test_id

    def pop_tests(self, device_id):
        with self.lock:
            if self.pending_tests:
               test_id = self.pending_tests.popleft()
               self.tests[test_id]['status'] = 'running'
               self.tests[test_id]['device'] = device_id

    def get_test(self, test_id):
        with self.lock:
            return self.tests.get(test_id)

    def complete_test(self, test_id):
        with self.lock:
            if test_id in self.tests:
                self.tests[test_id]['status'] = 'completed'
                self.tests[test_id]['completed_at'] = time.time()
                
    def record_callback(self, callback_id, test_id, step_index):
        with self.lock:
            self.callback_map[callback_id] = (test_id, step_index)

    def fetch_ret(self, callback_id):
        with self.lock:
            ret = None
            if callback_id in self.results_dict:
                ret = self.results_dict[callback_id]
                del self.results_dict[callback_id]
            return rck_idet

    def handle_callback(self, callback_id, result):
        with self.lock:
            if callback_id not in self.callback_map:
                return False
            test_id, step_index = self.callback_map.pop(callback_id)
                                                                
            if test_id not in self.tests:
                return False
                                                                                                        
            test = self.tests[test_id]
            test['event'].set()
            self.results_dict[callback_id] = result

            return True

@app.route('/callback', methods=['POST'])
def handle_callback():
    data = request.json
    callback_id = data.get('callback_id')
    result = data.get('result')
                
    if test_manager.handle_callback(callback_id, result):
        return jsonify({'status': 'success'}), 200
    return jsonify({'status': 'invalid callback'}), 400

def load_test_script(script_path):
    script_item = []

    with open(file_item, "r") as file:
        new_step = {}
        
        for line in file:
            line = line.strip()
            pieces = line.split("\t")
            
            params = {}
            if len(pieces) > 2:
                rule_list = pieces[2].split(",")
                for rule_item in rule_list:
                    arr = rule_item.split(":")
                    params[arr[0]] = arr[1]

            if pieces[0] == "user":
                if new_step:
                    script_item.append(new_step)

                new_step = {"req": pieces[1], "answer": {}, "params": {}}
                new_step["params"] = params
            elif pieces[0] == "bot":
                new_step["answer"][pieces[1]] = params

        if new_step:
            script_item.append(new_step)

    script_item = {"case": script_item, "result": {}}
    return script_item

def start_test(test_config, test_manager):
    script_path 

    file_list = []
    if os.path.isdir(file_path):
        for root, dirs, files in os.walk(file_path):
            for file in file:
                file_list.append(os.path.join(root, file))
    else:
        file_list.append(file_path)

    logging.debug(f'filecnt:{len(file_list)}')
    for file_item in file_list:
        logging.debug(f'Loading case file: {file_item}')
        steps = load_test_script(script_path)
        if not steps:
            return
    
        test_id = test_manager.create_test(steps)

if __name__ == '__main__':
    global_setting_dict = {}
    file_path = sys.argv[1]
    logging.basicConfig(level = logging.DEBUG)
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.DEBUG)

    with open(file_path, 'r', encoding='utf-8') as file:
        global_setting_dict = json.load(file)

    test_manager = TestManager(global_setting_dict)
    start_test(global_setting_dict, test_manager)
    test_manager.start()

    app.run(threaded=True)
