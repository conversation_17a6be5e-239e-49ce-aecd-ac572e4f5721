from request_api import RequestAgent
import requests

req_url = f'http://127.0.0.1:5000/chat/rec_app'
headers = {'Content-Type': 'application/json', 'Authorization': 'eyJhbGciOiJIUzM4NCJ9.eyJuaWNrTmFtZSI6IiIsInVzZXJJZCI6MTkxMzIyODk2ODQzNDA3MzYwMCwiZGV2aWNlSWQiOiIxMzY5MTQ5MjI4N2RldmljZSIsImF1dGhvcml0aWVzIjpbeyJhdXRob3JpdHkiOiJST0xFX0dFRUsifV0sInVzZXJuYW1lIjoidXNlcl9jZTI0MDNmMWNiODQ0NTk1YmFjMmIxZWYwZmZkNDFiZSIsImV4cCI6MTc0ODMxMzMzN30.UHxUSFR_lQyJvnrXBDTWtkZ_GLVA_rI9oouTE3WHN0uMvceXErdCy2p4zdFMweQW'}

#msg = {"message": {"type": 101,"data": "帮我打车"}, "context": {"voice_api_version": "v0.1", "timestamp": 1744957774, "did":"0f84107d-0df9-ec77-d2e8-0283287f8562",  "trace_id":"1f84107d-0df9-ec77-d2e8-0283287f8562", "tran_id":"2f84107d-0df9-ec77-d2e8-0283287f8562", "device_type":"earpods", "device_model": "", "coords":{ "latitude": "40.059029", "longitude": "116.312564"}}}

#msg = {"message": {"type": 101,"data": "帮我点杯咖啡"}, "context": {"voice_api_version": "v0.1", "timestamp": 1744957774, "did":"0f84107d-0df9-ec77-d2e8-0283287f8562",  "trace_id":"1f84107d-0df9-ec77-d2e8-0283287f8562", "tran_id":"2f84107d-0df9-ec77-d2e8-0283287f8562", "device_type":"earpods", "device_model": "", "coords":{ "latitude": "40.059029", "longitude":"116.312564"}}}
#msg = "{\"req_id\": \"ac5443a5-facf-49ce-a1dc-74792f4765bd\", \"intent_id\": \"10001\", \"uid\": \"ali_cloud1\"}"

msg = {"message": {"type": 12, "data": "帮我点杯咖啡"}, "context": {"voice_api_version": "v0.1", "timestamp": 1744957774, "did":"0f84107d-0df9-ec77-d2e8-0283287f8562",  "trace_id":"1f84107d-0df9-ec77-d2e8-0283287f8562", "tran_id":"2f84107d-0df9-ec77-d2e8-0283287f8562", "device_type":"earpods", "device_model": "", "coords":{ "latitude": "40.059029", "longitude":"116.312564"}}}

print(msg)

#req_url = f'http://spotlight.iumj.cn:5000//chat/rec_app'
#headers = {'Content-Type': 'application/json'}
#msg = "{\"context\": {\"uid\": \"96075996755\", \"coords\": {\"latitude\": \"40.04135028\", \"longitude\": \"116.29773461\"}}, \"message\": {\"type\":1002,\"data\":\"ui finish\"}}"
#msg = '{"context": {"uid": "cloud1", "coords": {"latitude": "40.04148925", "longitude": "116.29794181"}}, "message": {"type":201,"data":{"title":"title","content":"收货人：Zhen, 电话：13488789035, 地址：康得大厦5层, 菜品：陨石生椰拿铁\","pic_url":"","action":{"text":"确定","name":"click", "reply": "小光，确认支付"}}}}'
#print(msg)

response = requests.post(req_url, headers = headers, json=msg)
