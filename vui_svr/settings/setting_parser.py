import json
import os
from typing import List, Dict

from pydantic_settings import BaseSettings, SettingsConfigDict


setting_json = os.environ.get("GLOBAL_SETTING_FILE", "./vui_svr/settings/global_setting.cmdp.offline")

print(os.listdir("."))


with open(setting_json, 'r', encoding='utf-8') as f:
    global_setting_dict = json.load(f)

if not global_setting_dict:
    raise Exception(f"Invalid global setting config file: {setting_json}")


test_token = os.environ.get("WEB_TEST_TOKEN", "") or global_setting_dict.get("web_test_token", "")


if global_setting_dict["interaction_type"] == "single_round":
    global_setting_dict["NEED_TIMER"] = False
    global_setting_dict["NEED_PUSH"] = False
    global_setting_dict["NEED_INTENT_RULE"] = False
    global_setting_dict["NEED_INTENT_LLM"] = False
    global_setting_dict["NEED_CHAT_LLM"] = False
    global_setting_dict["NEED_INSTANTCMD_RULE"] = True
    global_setting_dict["NEED_INSTANTCMD_LLM"] = True
    global_setting_dict["NEED_BYEBYE_TEXT"] = False
elif global_setting_dict["interaction_type"] == "simple_round":
    global_setting_dict["NEED_TIMER"] = False
    global_setting_dict["NEED_PUSH"] = False
    global_setting_dict["NEED_INTENT_RULE"] = True
    global_setting_dict["NEED_INTENT_LLM"] = True
    global_setting_dict["NEED_CHAT_LLM"] = True
    global_setting_dict["NEED_INSTANTCMD_RULE"] = True
    global_setting_dict["NEED_INSTANTCMD_LLM"] = True
    global_setting_dict["NEED_BYEBYE_TEXT"] = True
elif global_setting_dict["interaction_type"] == "multi_round":
    global_setting_dict["NEED_TIMER"] = True
    global_setting_dict["NEED_PUSH"] = True
    global_setting_dict["NEED_INTENT_RULE"] = True
    global_setting_dict["NEED_INTENT_LLM"] = True
    global_setting_dict["NEED_CHAT_LLM"] = True
    global_setting_dict["NEED_INSTANTCMD_RULE"] = True
    global_setting_dict["NEED_INSTANTCMD_LLM"] = True
    global_setting_dict["NEED_BYEBYE_TEXT"] = True


class Settings(BaseSettings):
    model_config = SettingsConfigDict(json_file=setting_json, case_sensitive=False)

    run_type: str
    run_mode: str
    
    console_log_level: str
    interaction_type: str

    web_client: List[str]
    web_client_ver: Dict[str, str]
    app_client: List[str]
    app_client_ver: Dict[str, str]
    token_client: List[str]

    coze_addr: str
    langflow_addr: str
    n8n_addr: str

    log_dir: str
    llm_addr: str
    plugin_client: List[str]
    plugin_rules: str
    phone_client: List[str]

    role_config_file: str

    intent_config_file: str
    stop_word_file: str
    ner_word_path: str
    prompt_path: str
    single_intent_path: str
    context_intent_path: str
    task_rule_path: str
    param_inv_path: str

    memo_feature_config: str
    memo_extract_pattern: str

    redis_ip: str
    redis_port: str
    redis_user: str
    redis_pass: str

    chatdb_pool_name: str
    chatdb_pool_size: int
    mysql_ip: str
    mysql_port: int
    mysql_user: str
    mysql_pass: str
    mysql_dbname: str

    vector_ip: str
    vector_port: str
    coze_auth: str

    agent_role: str
    host_role: str
    expert_role: str

    llm_intent_all_model: str
    llm_intent_pos_model: str
    llm_chat_model: str
    llm_intent_instant: str
    embedding_model: str
    extract_model: str
    compress_model: str
