import threading
 
# 创建一个线程安全的字典
class ThreadSafeDict(dict):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.lock = threading.Lock()
                              
    def __getitem__(self, key):
        with self.lock:
            return super().__getitem__(key)
                                                           
    def __setitem__(self, key, value):
        with self.lock:
            return super().__setitem__(key, value)
                                                                                       
    def __delitem__(self, key):
        with self.lock:
            return super().__delitem__(key)
