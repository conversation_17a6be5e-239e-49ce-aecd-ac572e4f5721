from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.orm import Mapped, mapped_column
from sqlalchemy import Integer, String, JSON, TIMESTAMP
from sqlalchemy.sql import func

from datetime import datetime

# SQLALCHEMY_DATABASE_URI = "mysql+mysqlconnector://127.0.0.1:13306/chat_hist_db?user=root&password=Spotlight321@"


class Base(DeclarativeBase):
    pass


class ChatHistory(Base):
    __tablename__ = 'chat_hist'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(String, primary_key=True)
    req_id: Mapped[str] = mapped_column(String)
    conversation_id: Mapped[str] = mapped_column(String)
    msg: Mapped[str] = mapped_column(String)
    msg_type: Mapped[str] = mapped_column(String)
    ts: Mapped[int] = mapped_column(Integer)
    intent_id: Mapped[str] = mapped_column(String)
    talk_cmd: Mapped[str] = mapped_column(String)
    mic_cmd: Mapped[str] = mapped_column(String)
    msg_modal: Mapped[str] = mapped_column(String)
    role: Mapped[str] = mapped_column(String)
    topic: Mapped[str] = mapped_column(String)
    scene_id: Mapped[str] = mapped_column(String)
    scene_sub_id: Mapped[str] = mapped_column(String)
    audio_blob_id: Mapped[str] = mapped_column(String, default='')
    audio_blob_url: Mapped[str] = mapped_column(String, default='')


class HistorySummary(Base):
    __tablename__ = 'hist_summary'

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(String, primary_key=True)
    msg: Mapped[str] = mapped_column(String)
    intent_id: Mapped[str] = mapped_column(String)
    conversation_id: Mapped[str] = mapped_column(String)
    topic: Mapped[str] = mapped_column(String)
    ts: Mapped[int] = mapped_column(Integer)


class OrderHistory(Base):
    __tablename__ = "order_history"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(String)
    req_id: Mapped[str] = mapped_column(String)
    conversation_id: Mapped[str] = mapped_column(String)
    app_id: Mapped[str] = mapped_column(String)
    context: Mapped[dict] = mapped_column(JSON)
    order_data: Mapped[dict] = mapped_column(JSON)

    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"OrderHistory(id={self.id}, app={self.app_id})"


class UserNewsPreference(Base):
    __tablename__ = "user_news_preference"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    user_id: Mapped[str] = mapped_column(String, nullable=False, default='')
    conversation_id: Mapped[str] = mapped_column(String, nullable=False, default='')
    req_id: Mapped[str] = mapped_column(String, nullable=False, default='')
    keyword: Mapped[str] = mapped_column(String, nullable=False, default='')
    preference_score: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    # interaction_type: Mapped[str] = mapped_column(String, nullable=False, default='')

    created_at: Mapped[datetime] = mapped_column(TIMESTAMP, server_default=func.now())
    updated_at: Mapped[datetime] = mapped_column(TIMESTAMP, server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"UserNewsPreference(id={self.id}, keyword={self.keyword})"
