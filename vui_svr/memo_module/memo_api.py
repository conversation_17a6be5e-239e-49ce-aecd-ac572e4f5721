# coding=utf-8

from vui_svr.redis_lock import RedisLock
from elasticsearch import Elasticsearch
from elasticsearch.helpers import bulk
from sqlalchemy.orm import sessionmaker
from sqlalchemy import create_engine

from vui_svr.gpt_api_V4 import GPTAgent
from .db_model import Chat<PERSON>istory, HistorySummary, OrderHistory, UserNewsPreference
from .preference_weight import calculate_all_keyword_weights

import hashlib
import uuid
import redis
import time
import json
import sys
import structlog
from urllib.parse import quote
from collections import defaultdict
from typing import List


logger = structlog.get_logger(__name__)


class MemoAgent:
    def __init__(self, info_agent, global_setting_dict):
        self.info_agent = info_agent
        self.db_client = redis.StrictRedis(host=global_setting_dict["redis_ip"], port=global_setting_dict["redis_port"], 
                username=global_setting_dict["redis_user"], password=global_setting_dict["redis_pass"])
        # self.mysql_connect_pool = pooling.MySQLConnectionPool(pool_name = global_setting_dict["chatdb_pool_name"],
        #         pool_size = global_setting_dict["chatdb_pool_size"], port = global_setting_dict["mysql_port"],
        #         host = global_setting_dict["mysql_ip"], user = global_setting_dict["mysql_user"],
        #         password = global_setting_dict["mysql_pass"], database = global_setting_dict["mysql_dbname"])
#       self.vector_client = Elasticsearch([{'host': global_setting_dict["vector_ip"], 'port':global_setting_dict["vector_port"]}])
        self.embeding_agent = GPTAgent("embedding", "embedding", global_setting_dict["embedding_model"], global_setting_dict)
        self.extract_agent = GPTAgent("extract_info", "extract_info", global_setting_dict["extract_model"], global_setting_dict)
#       self.compress_agent = GPTAgent("compress_hist", "compress_hist", global_setting_dict["compress_model"],global_setting_dict)

        # Sqlalchemy session
        db_uri = (f"mysql+mysqlconnector://{global_setting_dict['mysql_user']}:{quote(global_setting_dict['mysql_pass'])}"
                  f"@{global_setting_dict['mysql_ip']}:{global_setting_dict['mysql_port']}/{global_setting_dict['mysql_dbname']}")
        print(db_uri)
        engine = create_engine(db_uri, pool_pre_ping=True)
        self.Session = sessionmaker(autocommit=False, autoflush=False, bind=engine)

        # 加载待提取变量
        self.memo_feature_dict = {}
        with open(global_setting_dict["memo_feature_config"], 'r') as file:
            self.memo_feature_dict = json.load(file)
        memo_feature_list = []
        for feat_key, feat_val in self.memo_feature_dict.items():
            memo_feature_list.append(f'变量名："{feat_key}"，信息描述：{feat_val["prompt"]}')
        memo_feature_str = "\n".join(memo_feature_list)

        # 生成记忆提取LLM的Prompt
        memo_extract_pattern = ""
        with open(global_setting_dict["memo_extract_pattern"], 'r') as file:
            memo_extract_pattern = file.read()
        memo_extract_pattern = memo_extract_pattern.replace(f"{{{{memo_feature}}}}", memo_feature_str)
        with open(f'{global_setting_dict["prompt_path"]}/extract_info', 'w') as file:
            file.write(memo_extract_pattern)

        self.memo_expire_time = 864000

    def __del__(self):
        pass
        #if self.mysql_connect_pool:
        #    self.mysql_connect_pool.reset()
        #    logger.info(f'All connections in the pool have been closed.')

    #def do_vector_embeding(self, user_id, global_conversation_id, intent_id, content):
    #    code, err_msg, msg_embeding, model_name, prompt_token, output_token, time_cost = self.embeding_agent.chat(content, {}, 
    #            True, global_conversation_id, True)
    #    return msg_embeding 

    #def do_vector_insert(self, user_id, global_conversation_id, intent_id, doc_msg, index_name = "hist_summary_vector"):
    #    doc_id = hashlib.md5()
    #    doc_id.update(doc_msg.encode('utf-8'))
    #    doc_id = doc_id.hexdigest()

    #    doc_emb = self.do_vector_embeding(user_id, global_conversation_id, intent_id, doc_msg)

    #    logger.debug(f'[][{user_id}][{global_conversation_id}] docid:{doc_id}, doc:{doc_msg}, vec:{doc_emb}')
    #    document = {'embedding': doc_emb}
    #    self.vector_client.index(index = index_name, id = doc_id, body=document)
    #    return doc_id

    #def do_vector_search(self, user_id, global_conversation_id, intent_id, query, field, index_name = "hist_summary_vector"):
    #    logger.debug(f'[][{user_id}][{global_conversation_id}] query:{query}, filed:{field}')
    #    query_vector = self.do_vector_embeding(user_id, global_conversation_id, intent_id, query)
    #    logger.debug(f'[][{user_id}][{global_conversation_id}] query:{query}, filed:{field}, vec:{query_vector}')
    #    script_query = {
    #        "script_score": {
    #            "query": {"match_all": {}},
    #            "script": {
    #                "source": "cosineSimilarity(params.query_vector, '{}') + 1.0".format(field),
    #                "params": {"query_vector": query_vector}
    #            }
    #        }
    #    }
    #    response = self.es.search(index = index_name, body={"size": size, "query": script_query}, _source=True)
    #    return [hit['_source'] for hit in response['hits']['hits']]
    
    # def do_mysql_query(self, req_id, user_id, global_conversation_id, intent_id, query_sql, query_data, need_ret = False):
    #     results = []

    #     connection = None
    #     try:
    #         connection = self.mysql_connect_pool.get_connection()
    #         cursor = connection.cursor()
    #         if query_data:
    #             cursor.execute(query_sql, query_data)
    #         else:
    #             cursor.execute(query_sql)
    #         if need_ret:
    #             results = cursor.fetchall()
    #         connection.commit()
    #         cursor.close()
    #         logger.debug(f'[][{user_id}][{global_conversation_id}] Finish query, ret:{results}')
    #     except Exception as e:
    #         logger.error(f'[][{user_id}][{global_conversation_id}] Error when query, sql:{query_sql}: {e}')
    #     finally:
    #         if connection:
    #             connection.close()

    #     return results

    def set_hist_chat(self, req_id, user_id, global_conversation_id, intent_id, role, msg, real_intent_id, scene_id, scene_sub_id, mic_cmd, talk_cmd, msg_type: str = "must", audio_blob_id: str = ""):
        with self.Session() as session:
            history = ChatHistory(
                user_id=user_id,
                req_id=req_id,
                conversation_id=global_conversation_id,
                intent_id=intent_id,
                topic="",
                role=role,
                msg=msg,
                msg_modal="text",
                msg_type=msg_type,
                scene_id=scene_id,
                scene_sub_id=scene_sub_id,
                talk_cmd=talk_cmd,
                mic_cmd=mic_cmd,
                ts=int(time.time()*1000),
                audio_blob_id=audio_blob_id,
            )
            session.add(history)
            session.commit()

        simp_hist_chat, hist_msg, last_msg = self.get_hist_chat(req_id, user_id, global_conversation_id, intent_id)

        return simp_hist_chat, hist_msg, last_msg

    def get_hist_chat(self, req_id, user_id, global_conversation_id, intent_id, hist_conversation_iter=10):
        with self.Session() as session:
            query = (
                session.query(ChatHistory)
                .filter(
                    ChatHistory.user_id == user_id, ChatHistory.msg_type != "padding",
                )
            )
            if global_conversation_id:
                query = query.filter(ChatHistory.conversation_id == global_conversation_id)
            else: # use a fixed time if no conversation_id
                query = query.filter(ChatHistory.ts >= int(time.time() - 600)*1000)  # 10min atm
            if intent_id != "0":
                query = query.filter(ChatHistory.intent_id == intent_id)
            res = query.order_by(ChatHistory.ts.desc()).limit(hist_conversation_iter*2).all()

        hist_msg_list, simp_hist_chat = [], []
        for history_item in reversed(res):
            if history_item.role == "user":
                role = "用户"
            else:
                role = "你"
            hist_msg_list.append(f'{role}:{history_item.msg}')
            # last_msg = item[field_dict["msg"]]
            simp_hist_chat.append(
                {
                    "msg": history_item.msg,
                    "role":history_item.role,
                    "intent_id":history_item.intent_id,
                    "scene_id":history_item.scene_id,
                    "scene_sub_id":history_item.scene_sub_id,
                    "req_id": history_item.req_id,
                    "msg_type": history_item.msg_type,
                    "create_ts": history_item.ts,
                    "conversation_id": history_item.conversation_id,
                }
            )

        hist_msg = "; ".join(hist_msg_list)
        last_msg = "" if not res else res[0].msg
        return simp_hist_chat, hist_msg, last_msg

    def get_portrait(self, req_id, user_id, global_conversation_id, mem_req):
        # profile_key = f'{user_id}_profile_detail'
        # profile_val = self.db_client.get(profile_key)
        # profile_dict = self.load_json(profile_val)
        profile_ret = {"profile_base": {}, "profile_task": {}}
        # profile_key_list = mem_req

        #if type(profile_dict) == dict and type(profile_key_list) == list:
        #    for key_item in profile_key_list:
        #        if key_item in profile_dict and profile_dict[key_item]:
        #            profile_ret["profile_base"][key_item] = profile_dict[key_item]
        return profile_ret

    def extract_portrait(self, req_id, user_id, global_conversation_id, intent_id, coords, task_vars, hist_msg):
        # process LLM extract & compress histchat
        ts = time.time()
        custom_var = {"uid": user_id, "coords": coords, "intent_id": intent_id, "req_id": req_id, "ts": f'{ts}',
                "conversation_id":global_conversation_id, "hist_chat": hist_msg}
        # code,err_msg, ret_msg, model_name, prompt_token, output_token, time_cost = self.compress_agent.chat("", custom_var, False)
        # compress_msg = ret_msg["output"]

        # ts = time.time()
        # dt_object = datetime.utcfromtimestamp(ts)
        # mysql_ts = dt_object.strftime('%Y-%m-%d %H:%M:%S')

        # insert_query = f"INSERT INTO hist_summary (user_id, conversation_id, intent_id, topic, msg, ts) VALUES ('{user_id}', " \
        #         f"'{global_conversation_id}', {intent_id}', '', '{compress_msg}', '{mysql_ts}');"
        # self.do_mysql_query(req_id, user_id, global_conversation_id, intent_id, insert_query)

        #  提取变量 & 存入redis
        # ts = time.time()
        # custom_var = {"uid": user_id, "coords": coords, "intent_id": intent_id, "req_id": req_id, "ts": f'{ts}',
        #         "conversation_id":global_conversation_id, "hist_chat": hist_msg}
        code, err_msg, ret_msg, model_name, prompt_token, output_token, time_cost = self.extract_agent.chat(req_id, user_id, 
                global_conversation_id, "", custom_var, True)
        extract_msg = ret_msg

        if extract_msg:
            profile_lock_key = f"{user_id}_profile_detail"
            #self.db_client.delete(profile_lock_key)
            profile_lock = RedisLock(self.db_client, profile_lock_key)

            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] lock_key:{profile_lock_key}, ' \
                    f'extract_msg:{extract_msg}')
            if profile_lock.acquire():
                try:
                    profile_key = f'{user_id}_profile_detail'
                    profile_val = self.db_client.get(profile_key)
                    profile_dict = json.loads(profile_val or "{}")
                    # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] old profile: {profile_dict}')

                    for feat_key, feat_val in extract_msg.items():
                        if feat_key in self.memo_feature_dict and feat_val:
                            if self.memo_feature_dict[feat_key]["type"] == "STRING":
                                profile_dict[feat_key] = feat_val
                            elif self.memo_feature_dict[feat_key]["type"] == "LIST":
                                if feat_key in profile_dict:
                                    profile_dict[feat_key].append(feat_val)
                                else:
                                    profile_dict[feat_key] = [feat_val]

                    profile_str = json.dumps(profile_dict, ensure_ascii=False)
                    # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new profile: {profile_str}')

                    self.db_client.set(profile_key, profile_str)
                finally:
                    profile_lock.release()
            else:
                logger.error(f'"[{req_id}][{user_id}][{global_conversation_id}] error when getting profile lock: ' \
                        f'{profile_lock_key}')

        # 加深任务关键实体记忆
        if intent_id != "10004" and task_vars:    # 非闲聊, 变量非空, process Task vars
            pass

        return 0

    def clear_global_context(self, req_id, user_id, global_conversation_id, intent_info):
        logger.info(f'[][{user_id}][{global_conversation_id}] All portrait cleared')
        return 0

    def clear_intent_context(self, req_id, user_id, global_conversation_id, intent_id):
        logger.debug(f"[][{user_id}][{global_conversation_id}] Clearing intent:{intent_id} portrait")
        return 0

    def add_order_history(
            self, req_id: str, user_id: str, conversation_id: str, app_id: str, context: dict, order_data_list: List[dict]
    ):
        with self.Session() as session:
            objs = []
            for order_data in order_data_list:
                if not order_data:
                    continue
                if not isinstance(order_data, dict):
                    logger.error(f"[{req_id}][{user_id}][{conversation_id}]Invalid data: {order_data}, type: {type(order_data)}. skip it.")
                    continue
                obj = OrderHistory(
                    user_id=user_id,
                    req_id=req_id,
                    conversation_id=conversation_id,
                    app_id=app_id,
                    context=context,
                    order_data=order_data,
                )
                objs.append(obj)
            session.bulk_save_objects(objs)
            session.commit()

    def get_order_history(self, user_id: str, app_id: str, limit: int) -> List[type[OrderHistory]]:
        with self.Session() as session:
            res = (
                session.query(OrderHistory)
                .filter(
                    OrderHistory.user_id == user_id,
                    OrderHistory.app_id == app_id,
                )
                .order_by(OrderHistory.created_at.desc())
                .limit(limit)
                .all()
            )
        return res


    def add_user_news_preference_history(self, req_id: str, user_id: str, conversation_id: str, likes: dict):
        with self.Session() as session:
            session.query(UserNewsPreference).filter(
                UserNewsPreference.user_id == user_id,
                UserNewsPreference.conversation_id == conversation_id
            ).delete()

            prefs = [UserNewsPreference(
                user_id=user_id,
                req_id=req_id,
                conversation_id=conversation_id,
                keyword=key,
                preference_score=score
            ) for key, score in likes.items()]
            session.bulk_save_objects(prefs)
            session.commit()

    def _user_news_preference(self, user_id):
        with self.Session() as session:
            res = (
                session.query(UserNewsPreference)
                .filter(UserNewsPreference.user_id == user_id)
                .order_by(UserNewsPreference.created_at.desc())
                .limit(1000)
                .all()
            )
        result = dict()
        for r in res:
            timestamp = int(r.created_at.timestamp())
            if r.keyword not in result.keys():
                result[r.keyword] = defaultdict(list)
            result[r.keyword]["timestamps"].append(timestamp)
            result[r.keyword]["preference_scores"].append(r.preference_score)
        return calculate_all_keyword_weights(user_keywords_data=result, current_time=int(time.time()))

    def user_news_preference_scores(self, user_id, limit):
        keyword_preferences = self._user_news_preference(user_id=user_id)
        return sorted(keyword_preferences.items(), key=lambda x: x[1], reverse=True)[:limit]

if __name__ == '__main__':
    import json
    with open('./global_setting.cmdp.offline', 'r') as f:
        global_config_dict = json.load(f)
    memo_agent = MemoAgent(info_agent=None, global_setting_dict=global_config_dict)
    # res = memo_agent.get_hist_chat(
    #     req_id="", user_id="1920769224142225408",
    #     global_conversation_id="301b01c4-453c-4023-9d83-33407d3a7502_1747206431343", intent_id="0"
    # )
    # print(res[0])
    # print(res[1])
    # print(res[2])
    from vui_svr.memo_module.db_model import UserNewsPreference

    with memo_agent.Session() as s:
        p = s.query(UserNewsPreference).filter(UserNewsPreference.id == 5).first()
        print(p, p.user_id, p.created_at.timestamp(), p.updated_at.timestamp())

        p.keyword = 'ww'
        s.commit()
