import math
import time


# 权重计算参数
LAMBDA_RECENCY = 0.1 / 86400  # 衰减率，假设0.1对应一天，值越小衰减越慢
FREQUENCY_WINDOW_SECONDS = 7 * 86400  # 统计频率的时间窗口：最近7天
W_RECENCY = 0.4
W_FREQUENCY = 0.2
W_PREFERENCE = 0.4


# --- 辅助函数 ---


def calculate_recency_score(timestamps, current_time, lambda_val):
    """计算单个关键词的新近度得分 (取最大值)"""
    if not timestamps:
        return 0.0
    max_recency = 0.0
    for ts in timestamps:
        time_diff_seconds = current_time - ts
        recency = math.exp(-lambda_val * time_diff_seconds)
        max_recency = max(recency, max_recency)
    return max_recency


def calculate_frequency_score(timestamps, current_time, window_seconds):
    """计算单个关键词在时间窗口内的频率得分"""
    if not timestamps:
        return 0.0
    count = 0
    for ts in timestamps:
        if current_time - ts <= window_seconds:
            count += 1
    return float(count)


def calculate_preference_score(preference_scores, timestamps, current_time, lambda_val):
    """计算单个关键词的偏好强度得分 (近期加权平均)"""
    if not preference_scores or not timestamps:
        return 0.0
    if len(preference_scores) != len(timestamps):
        # 数据不一致，简单取平均或返回默认值
        return (
            sum(preference_scores) / len(preference_scores)
            if preference_scores
            else 0.0
        )

    weighted_sum_prefs = 0.0
    sum_weights = 0.0
    for i, score in enumerate(preference_scores):
        ts = timestamps[i]
        time_diff_seconds = current_time - ts
        recency_weight = math.exp(
            -lambda_val * time_diff_seconds
        )  # 使用与W_recency相同的衰减
        weighted_sum_prefs += score * recency_weight
        sum_weights += recency_weight

    return weighted_sum_prefs / sum_weights if sum_weights > 0 else 0.0


def min_max_normalize(scores_dict):
    """对字典中的值进行Min-Max归一化"""
    if (
        not scores_dict or len(scores_dict) < 2
    ):  # 如果只有一个元素或为空，归一化意义不大或无法进行
        return {
            k: 0.5 if scores_dict else 0.0 for k in scores_dict
        }  # 或返回原始值/特定值

    min_val = min(scores_dict.values())
    max_val = max(scores_dict.values())

    if max_val == min_val:  # 所有值都相同
        return {k: 0.5 for k in scores_dict}  # 或都设为0或1

    # normalized_scores = {}
    # for key, value in scores_dict.items():
    #     normalized_scores[key] = (value - min_val) / (max_val - min_val)
    # return normalized_scores
    return {k: (v - min_val) / (max_val - min_val) for k, v in scores_dict.items()}


# --- 主计算函数 ---


def calculate_all_keyword_weights(user_keywords_data, current_time):
    """
    计算用户所有关键词的最终权重。
    """
    lambda_rec = LAMBDA_RECENCY
    freq_window = FREQUENCY_WINDOW_SECONDS
    w_r = W_RECENCY
    w_f = W_FREQUENCY
    w_p = W_PREFERENCE

    if not user_keywords_data:
        return {}

    recency_scores = {}
    frequency_scores = {}
    preference_scores_calculated = {}

    # 1. 计算每个关键词的原始 W_recency, W_frequency, W_preference
    for keyword, data in user_keywords_data.items():
        timestamps = data.get("timestamps", [])
        prefs = data.get("preference_scores", [])

        recency_scores[keyword] = calculate_recency_score(
            timestamps, current_time, lambda_rec
        )
        frequency_scores[keyword] = calculate_frequency_score(
            timestamps, current_time, freq_window
        )
        # 为了演示，我们让偏好分计算中的时间衰减与新近度使用相同的lambda
        preference_scores_calculated[keyword] = calculate_preference_score(
            prefs, timestamps, current_time, lambda_rec
        )

    # 2. 归一化各项得分
    # 注意：如果某项得分全部为0或只有一个关键词，归一化可能需要特殊处理
    norm_recency = min_max_normalize(recency_scores)
    norm_frequency = min_max_normalize(frequency_scores)
    norm_preference = min_max_normalize(preference_scores_calculated)

    final_weights = {}
    # 3. 计算最终权重
    for keyword in user_keywords_data.keys():
        w_final = (
            w_r * norm_recency.get(keyword, 0.0)
            + w_f * norm_frequency.get(keyword, 0.0)
            + w_p * norm_preference.get(keyword, 0.0)
        )
        final_weights[keyword] = w_final

        # 打印中间过程，方便理解
        # print(f"--- 关键词: {keyword} ---")
        # print(
        #     f"  原始新近度分: {recency_scores.get(keyword, 0.0):.4f} -> 归一化: {norm_recency.get(keyword, 0.0):.4f}"
        # )
        # print(
        #     f"  原始频率分: {frequency_scores.get(keyword, 0.0):.4f} -> 归一化: {norm_frequency.get(keyword, 0.0):.4f}"
        # )
        # print(
        #     f"  原始偏好分: {preference_scores_calculated.get(keyword, 0.0):.4f} -> 归一化: {norm_preference.get(keyword, 0.0):.4f}"
        # )
        # print(f"  最终权重: {w_final:.4f}\n")

    return final_weights


if __name__ == "__main__":
    # --- 假设的数据和参数 ---
    user_data_example = {
        "userId": "user123",
        "keywords": {
            "AI发展": {
                "timestamps": [
                    int(time.time()) - 3 * 86400,
                    int(time.time()) - 1 * 86400,
                    int(time.time()) - 0.5 * 86400,
                ],
                # 3天前, 1天前, 半天前
                "preference_scores": [0.7, 0.9, 0.85],
            },
            "全球经济": {
                "timestamps": [
                    int(time.time()) - 10 * 86400,
                    int(time.time()) - 2 * 86400,
                ],  # 10天前, 2天前
                "preference_scores": [0.5, 0.6],
            },
            "新能源汽车": {
                "timestamps": [int(time.time()) - 0.2 * 86400],  # 约5小时前
                "preference_scores": [0.95],
            },
            "电影推荐": {  # 很久以前，且偏好一般
                "timestamps": [int(time.time()) - 30 * 86400],
                "preference_scores": [0.4],
            },
        },
    }
    current_timestamp = int(time.time())

    # --- 执行计算并打印结果 ---
    print(f"当前时间戳: {current_timestamp}\n")

    final_keyword_weights = calculate_all_keyword_weights(
        user_data_example["keywords"], current_timestamp
    )
    #     LAMBDA_RECENCY,
    #     FREQUENCY_WINDOW_SECONDS,
    #     W_RECENCY,
    #     W_FREQUENCY,
    #     W_PREFERENCE
    # )

    print("--- 最终关键词权重排序 ---")
    sorted_weights = sorted(
        final_keyword_weights.items(), key=lambda item: item[1], reverse=True
    )
    for k, v in sorted_weights:
        print(f"{k}: {v:.4f}")
