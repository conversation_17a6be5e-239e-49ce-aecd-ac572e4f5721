import requests
import json
import time
import datetime
import structlog
import sys
import os
from requests.exceptions import RequestException

logger = structlog.get_logger(__name__)

class GPTAgent():
    def __init__(self, bot_id, bot_name, model, global_setting_dict):
        self.bot_id = bot_id
        self.bot_name = bot_name
        self.wait_time = 1
        self.retry_cnt = 180
        self.api_addr = global_setting_dict["llm_addr"]
        self.proxy = None
        self.prompt_addr = global_setting_dict["prompt_path"]
        self.model = model
        logger.info(f"GPT config, api:{self.api_addr}, botid:{bot_id}, botname:{bot_name}")

        self.model_dict = {
                "gpt4o": {"provider": "openai", "model": "gpt-4o"},
                "gpt4o-mini": {"provider": "openai", "model": "gpt-4o-mini"},
                "doubao-lite-4k": {"provider": "doubao", "model": "ep-20241224113201-s4sn2"},
                "doubao-pro-4k": {"provider": "doubao", "model": "ep-20241223150005-6s2k4"},
		        "doubao-pro-4km": {"provider": "doubao", "model": "ep-20250116194009-2k9mm"},
                "doubao-pro-4kmv2": {"provider": "doubao", "model": "ep-20250117110204-h7tdc"},
                "doubao-lite-32k": {"provider": "doubao", "model": "ep-20241217194155-wqbhl"},
                "doubao-pro-32k": {"provider": "doubao", "model": "ep-20241206114453-lxnmz"},
                "doubao-pro-32km": {"provider": "doubao", "model": "ep-20250117114432-pfqvq"},
                "qwen-max": {"provider": "aliyun", "model": "qwen-max"},
                "qwen-plus": {"provider": "aliyun", "model": "qwen-plus"},
                "qwen-turbo": {"provider": "aliyun", "model": "qwen-turbo"},
                "deepseek-v3": {"provider": "aliyun", "model": "deepseek-v3"},
                "deepseek-v3-official": {"provider": "deepseek", "model": "deepseek-chat"},
                "deepseek-vc": {"provider": "huoshan", "model": "deepseek-v3-250324"},

                "bailian-ft-250614": {"provider": "bailian", "model": "qwen3-8b-ft-202506061428-8390"}
        }

    @staticmethod
    def check_resp(response):
        try:
            if response.json():
                return True
        except ValueError:
            logger.error(f'Error when converting 2 json or resp is Empty')
        return False

    def api_post(self, url, retries=1, timeout=(3, 15), **kwargs):
        for attempt in range(retries + 1):
            try:
                response = requests.post(url, timeout=timeout, **kwargs)
                if not response.ok:
                    raise ValueError(f"Invalid status code: {response.status_code}")
                if not self.check_resp(response):
                    raise ValueError("Response validation failed")
                return response.json()
            except (RequestException, ValueError) as e:
                logger.error(f"Attempt {attempt + 1} failed: {str(e)}, retrying...")
                if attempt >= retries:
                    break
        return None

    @staticmethod
    def load_prompt(file_name):
        with open(file_name, "r") as f:
            return f.read()

    def op_get_msg(self, req_id, user_id, global_conversation_id, msg, custom_var, model, conversation_id, provider, need_parse):
        #addr = f"http://{self.api_addr}/common-api/util/gen_content_llm"
        addr = f"{self.api_addr}/common-api/util/gen_content_llm"
        headers = {'appkey': 'UMhmghBLK77BJyf5', 'Content-Type': 'application/json'}

        #print(f"prompt file path: {self.prompt_addr}/{self.bot_id}")
        prompt = self.load_prompt(f"{self.prompt_addr}/{self.bot_id}")
        custom_var['input'] = msg
        #print(prompt)
        if len(custom_var) > 0:
            for key, val in custom_var.items():
                #print(f'key:{key}, val:{val}')
                replace_key = f"{{{{{key}}}}}"
                if type(val) != str:
                    val = json.dumps(val, ensure_ascii=False)
                prompt = prompt.replace(replace_key, val)
        content = {"prompt": prompt, "modelType": model, "provider": provider}

        model_name = ""
        prompt_token = 0
        output_token = 0
        start_time = time.time()
        # print(f'content:{content}')
        # logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] sending bot req, botid:{self.bot_id}, ' \
        #         f'botname:{self.bot_name}, info:{msg}, custom_var:{custom_var}')
        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] sending bot req, botid:{self.bot_id}, ' \
                f'botname:{self.bot_name}, content:{content}')
        resp_info = self.api_post(addr, headers = headers, json=content, proxies=self.proxy)
        if not resp_info:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] Post Request error, ')
            return -1, {}, "Wrong json format", model_name, prompt_token, output_token

        end_time = time.time()
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] model name: {self.bot_name}, origin llm resp: {resp_info}')
        answer = resp_info['data']
        # logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] model name: {self.bot_name}, origin llm resp: {answer}')

        try:
            answer = json.loads(answer)
            model_name = answer["model"]
            if "usage" in answer:
                prompt_token = answer["usage"]["prompt_tokens"]
                output_token = answer["usage"]["completion_tokens"]
            answer = answer["choices"]
            if len(answer) > 0:
                answer = answer[0]["message"]["content"]
            answer = answer.replace("```json", "")
            answer = answer.replace("```", "")

            if need_parse:
                answer = json.loads(answer)
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] answer: {answer}, model: {model_name}, ' \
                    f'time:{int((end_time - start_time)*1000)/1000}s')
        except:
            logger.exception(f'[{req_id}][{user_id}][{global_conversation_id}] Wrong json format, resp:{answer}, ' \
                    f'model: {model_name}, time:{int((end_time - start_time)*1000)/1000}s')
            return -1, {}, "Wrong json format", model_name, prompt_token, output_token 
        return 0, answer, "", model_name, prompt_token, output_token

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        pass

    def op_get_emb(self, req_id, user_id, global_conversation_id, msg, model_name, provider):
        addr = f"http://{self.api_addr}/common-api/util/gen_content_llm"
        headers = {'appkey': 'UMhmghBLK77BJyf5', 'Content-Type': 'application/json'}
        response = requests.post(addr, headers = headers, json=content, proxies=self.proxy)
        resp_info = response.json()

        answer = resp_info['data']
        model_name = ""
        prompt_token = 0 
        output_token = 0

        try:
            answer = json.loads(answer)
            model_name = answer["model"]
            if "usage" in answer:
                prompt_token = answer["usage"]["prompt_tokens"]
                output_token = answer["usage"]["completion_tokens"]
            answer = answer["choices"]
            if len(answer) > 0:
                answer = answer[0]["message"]["content"]

            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] answer: {answer}, model: {model_name}, ' \
                    f'time:{int((end_time - start_time)*1000)/1000}s')
        except:
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] Wrong json format, resp:{answer}, ' \
                    f'model: {model_name}, time:{int((end_time - start_time)*1000)/1000}s')
            return -1, {}, "Wrong json format", model_name, prompt_token, output_token

        return 0, answer, "", model_name, prompt_token, output_token

    def chat(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = {}, need_parse = True, conversation_id = "", embeding = False):
        start_time = time.time()
        code = 0
        err_msg = ""
        answer = ""
        model_name = ""
        prompt_token = 0
        output_token = 0

        model = self.model
        if model in self.model_dict:
            provider = self.model_dict[model]["provider"]
            model_name = self.model_dict[model]["model"]
            if embeding:
                code, answer, err_msg, model_name, prompt_token, output_token = self.op_get_emb(req_id, user_id, 
                        global_conversation_id, msg, model_name, provider)
            else:
                code, answer, err_msg, model_name, prompt_token, output_token = self.op_get_msg(req_id, user_id, 
                        global_conversation_id, chat_msg, custom_var, model_name, conversation_id, provider, need_parse)
        else:
            code = -1
            err_msg = f"Unknown model name : {model}"
        end_time = time.time()

        return code, err_msg, answer, model_name, prompt_token, output_token, end_time-start_time

if __name__ == '__main__':
    file_path = sys.argv[1]
    agent = GPTAgent("task_10002", "task_10002", "deepseek-vc", {"llm_addr": "127.0.0.1:18081", "prompt_path": "./prompts.cmdp/"})
    custom_var = {"uid": "cloud1", "coords": "116.312564,40.059029", "intent_id": "10002", "conversation_id": "234"}

    file_list = []
    if os.path.isdir(file_path):
        for root, dirs, files in os.walk(file_path):
            for file in files:
                file_list.append(os.path.join(root, file))
    else:
        file_list.append(file_path)
    logger.debug(f'filecnt:{len(file_list)}')

    for file_item in file_list:
        logger.debug(f'Loading case file: {file_item}')

        with open(file_item, "r") as file:
            hist_msg_list = []

            for line in file:
                line = line.strip()
                pieces = line.split("\t")

                role = "你"
                if pieces[0] == "user": role = "用户"
                hist_msg_list.append(f'{role}:{pieces[1]}')

                if pieces[0] == "user":
                    last_msg = f'{role}:{pieces[1]}'
                    hist_msg_str = "; ".join(hist_msg_list)
                
                    custom_var["last_input"] = last_msg
                    code, err_msg, answer, model_name, prompt_token, output_token, time_cost = agent.chat("111", "222", "333", 
                        hist_msg_str, custom_var)

                    ret = "HIT"
                    rule_list = pieces[2].split(",")
                    match_list = []
                    is_hit = False
                    for rule_item in rule_list:
                        arr = rule_item.split(":")

                        answer_val = answer
                        path_list = arr[0].split("#")
                        for path_piece in path_list:
                            answer_val = answer_val[path_piece]
                        if str(answer_val) != str(arr[1]): ret = "MISS"
                        match_list.append(f'{arr[0]}:{answer_val}/{arr[1]}')

                    match_list_str = ",".join(match_list)
                    print(f'########################[{ret}]{match_list_str} file:{file_item}, input:{hist_msg_str}, ' \
                        f'last_input:{last_msg}')
                    print(f'output: {answer}')

        print(f'##########################################################')
