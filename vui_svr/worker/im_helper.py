import logging
import json
import time
import uuid

import requests
from redis.exceptions import Lock<PERSON>rror
from rocketmq.v5.model.message import Message

from vui_svr.agent_svr_V4 import redis_client as redis_cli
from vui_svr.langflow_api_V4 import LangflowAgent
from vui_svr.settings import global_setting_dict
from vui_svr.worker.common_tools import user_id_from_token, mqtt_auth_from_token, send_message_to_vui
from vui_svr.worker.llm_producer import publish_mqtt_command_v5


SESSION_EX = 30 * 60

def user_session_id(user_id: str) -> str:
    fetch_session_lock = f"im_helper:fetch:session:lock:{user_id}"
    try:
        with redis_cli.lock(fetch_session_lock, timeout=10, blocking_timeout=3):
            user_session_k = f"im_helper:session:{user_id}"
            session_id = str(redis_cli.get(user_session_k) or "")
            if not session_id:
                session_id = str(uuid.uuid4())
                redis_cli.set(user_session_k, session_id, ex=SESSION_EX)
            else:
                redis_cli.expire(user_session_k, SESSION_EX)
        return session_id
    except LockError:
        raise


def consume_im_msg(msg: Message):
    data = json.loads(msg.body)

    access_token = data['user_info'].get("user_token")
    if not access_token:
        logging.error(f"Get access_token failed from msg, skip it! {data}")
        return False

    user_id = user_id_from_token(access_token)
    session_id = user_session_id(user_id)

    mqtt_auth = mqtt_auth_from_token(access_token)

    logging.debug(user_id, session_id, mqtt_auth)

    if data['app_id'] != 'com.tencent.mm':
        logging.error("Invalid app id, skip.")
        return False

    # balabala...
    langflow_inp = {"source": "push", "uid": str(user_id), "session_id": session_id, "data": data}
    code, _, _, _, res = langflow_do_action_polling(input_data=langflow_inp)
    if int(code) != 0:
        logging.info(f"[{user_id}] flow returns err, skip it. code: {code}, res: {res}")
        return False
    if res['output'] == "":
        logging.info("Flow returns empty msg, skip it.")
        return True

    # TODO: maybe a duration for cli to wakeup is required.
    expire_time = int(time.time()) + res["expire"] + 5
    push_info = {
        "intent_id": "10009",
        "message": res["output"],
        "expire_time": expire_time,
        "talk_status": "on",
        "mic_status": "on"
    }
    vui_data = {"uid": str(user_id), "push_info": push_info}

    send_res = send_message_to_vui(data=vui_data)
    if not send_res:
        logging.error(f"[{user_id}]send to vui failed!")
        return False

    # PUSH MESSAGE TO MQTT
    mqtt_data = {"type": "wake", "params": {}}
    res_bool = publish_mqtt_command_v5(connection_info=mqtt_auth, message_body=mqtt_data)
    if not res_bool:
        logging.error(f"[{user_id}]send to mqtt failed!")
        return False

    return True


def langflow_do_action_polling(input_data: dict):
    flow_id = "88c9c7f9-18e8-40df-9ffc-d71309ef5400"

    agent = LangflowAgent(pat_auth="", bot_id=flow_id, bot_name="", global_setting_dict=global_setting_dict)
    code, err_msg, job_id, conversation_id = agent.create_chat(
        req_id="",
        user_id=input_data["uid"],
        global_conversation_id="",
        chat_msg=json.dumps(input_data),
        target="",
        extra_header=dict(),
        ver_id=0,
    )
    # print(code, err_msg, job_id, conversation_id)

    code, status, err_msg, run_code, result = agent.wait_chat_finish(
        req_id="",
        user_id=input_data["uid"],
        global_conversation_id="",
        conversation_id=conversation_id,
        chat_id=job_id,
        target="",
        create_code=""
    )
    # print(code, status, err_msg, run_code, result)
    return code, status, err_msg, run_code, result


def langflow_do_action(input_data: dict) -> dict:
    # api_key = "sk-v_dFQoRMR3znZJ76uOt4l-mMSRH9b4g3Uoag3djqJYs"
    # url = "http://172.16.3.190:7860/api/v1/run/1c958c5e-595d-43b9-9c2c-e0bebd8e0906"  # The complete API endpoint URL for this flow
    api_key = "sk--M7XmS-D0lnC3x4EvyBIUkGjPkVEjlqbwIUs59WRj3E"
    url = "http://172.16.3.187:7860/api/v1/run/88c9c7f9-18e8-40df-9ffc-d71309ef5400"
    # Request payload configuration
    payload = {
        "input_value": json.dumps(input_data),
        "output_type": "chat",  # Specifies the expected output format
        "input_type": "chat"  # Specifies the input format
    }
    # Request headers
    headers = {
        "Content-Type": "application/json",
        "x-api-key": api_key  # Authentication key from environment variable
    }
    try:
        # Send API request
        response = requests.post(url, json=payload, headers=headers, timeout=(10, 40))
    except Exception as e:
        logging.exception(f"Error making langflow request: {e}")
        return {"status": -1, "output": f"{e}"}

    if response.status_code != 200:
        logging.error(f"Invalid status, code: {response.status_code}, data: {response.text}")
        return {"status": -1, "output": f"{response.text}"}

    # print(response.json())
    res = response.json()['outputs'][0]['outputs'][0]['results']['message']['text']
    msg = json.loads(res)
    return {"status": 0, "output": msg["output"], "expire": msg["expire"]}


if __name__ == '__main__':
    data = {}
    res = langflow_do_action_polling(input_data=data)
    print(res)
