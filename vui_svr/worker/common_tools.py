import logging
from urllib.parse import urljoin

import requests

from vui_svr.agents import token_agent


VUI_ADDR = "http://127.0.0.1:5000"

def mqtt_auth_from_token(token: str) -> dict:
    return token_agent.mqtt_auth_info(token)


def user_id_from_token(token: str) -> str:
    res = token_agent.auth_token(token)
    if res["code"] != 0:
        logging.error(f"Invalid token, res: {res}")
        raise
    return res["data"]["userId"]


def send_message_to_vui(data: dict):
    url = urljoin(VUI_ADDR, "/aibot/add_push")
    res = requests.post(url=url, json=data)
    if res.status_code != 200:
        logging.error(f"send message to vui failed. code: {res.status_code}, data: {res.text}")
        return False
    return True

