# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

import json
import logging
import threading
import time
from collections.abc import Callable
from concurrent.futures import ThreadPoolExecutor

from pydantic import BaseModel
from rocketmq import ClientConfiguration, Credentials, SimpleConsumer

LOG_FORMAT = '[%(asctime)s] [%(name)s] [%(levelname)s] [%(funcName)s] %(message)s'
logging.basicConfig(level=logging.DEBUG, format=LOG_FORMAT)

from vui_svr.worker.car_consumer import consume_car_msg
from vui_svr.worker.im_helper import consume_im_msg

class ChannelConfig(BaseModel):
    endpoints: str
    consumer_group: str
    topic: str
    tag: str


car_channel_config = ChannelConfig(
    endpoints="rmq-cn-8t84962mk07-vpc.cn-beijing.rmq.aliyuncs.com:8080",
    consumer_group="llm-consumer-dev",
    topic="sl-cp-order-notify-dev-topic",
    tag=""
)

im_channel_config = ChannelConfig(
    endpoints = "rmq-cn-8t84962mk07-vpc.cn-beijing.rmq.aliyuncs.com:8080",
    consumer_group = "user-consumer-push2ai-dev2",
    topic = "sl-agent-push2ai-dev-topic",
    tag = "push2ai"
)


executor = ThreadPoolExecutor(max_workers=16)

def handle_future_result(future):
    """
    这是附加到 Future 对象的“完成回调”。
    它会在后台线程中被执行。
    """
    try:
        # 如果任务成功执行，.result() 会返回 None (因为 my_callback 没有 return)
        # 如果任务抛出异常，.result() 会在这里重新抛出该异常
        future.result()
    except Exception as e:
        # 在这里集中处理所有 callback 抛出的异常
        logging.exception(f"caught exception: {e.__class__.__name__}, {e}")
        # 在这里你可以做很多事, 例如:
        # 1. 记录到专门的错误日志
        # 2. 将失败的消息推送到一个“死信队列”以便后续重试
        # 3. 更新监控指标


def subscribe_to_channel(chan_cfg: ChannelConfig, callback: Callable):
    credentials = Credentials()
    # if auth enable
    # credentials = Credentials("ak", "sk")
    config = ClientConfiguration(chan_cfg.endpoints, credentials)
    # with namespace
    # config = ClientConfiguration(endpoints, credentials, "namespace")
    # in most case, you don't need to create too many consumers; a singleton pattern is recommended
    # close the simple consumer when you don't need it anymore
    simple_consumer = SimpleConsumer(config, chan_cfg.consumer_group)
    try:
        simple_consumer.startup()
        try:
            simple_consumer.subscribe(chan_cfg.topic)
            # use tag filter
            # simple_consumer.subscribe(topic, FilterExpression("tag"))
            while True:
                try:
                    # max message num for each long polling and message invisible duration after it is received
                    messages = simple_consumer.receive(32, 15)
                    if messages is not None:
                        logging.debug(f"{simple_consumer.__str__()} receive {len(messages)} messages.")
                        for msg in messages:
                            # logging.info(json.loads(msg.body))
                            # if data["subStatus"] == 2:
                            #     print(json.loads(data["additionalInfo"]))
                            simple_consumer.ack(msg)
                            logging.debug(f"{simple_consumer.__str__()} ack message:[{msg.message_id}].")
                            # callback(msg)  # execute successfully
                            f = executor.submit(callback, msg)
                            f.add_done_callback(handle_future_result)
                except Exception as e:
                    logging.exception(f"receive or ack message raise exception: {e}")
        except Exception as e:
            logging.exception(f"{simple_consumer.__str__()} subscribe topic:{chan_cfg.topic} raise exception: {e}")
            simple_consumer.shutdown()
    except Exception as e:
        logging.exception(f"{simple_consumer.__str__()} startup raise exception: {e}")
        simple_consumer.shutdown()


def subscribe_by_thread():
    car_thread, im_thread = None, None

    while True:
        if not car_thread or not car_thread.is_alive():
            if car_thread:
                logging.error(f"car_thread exited, try restart it!")
            car_thread = threading.Thread(
                target=subscribe_to_channel,
                args=(car_channel_config, consume_car_msg),
                name="car_thread"
            )
            car_thread.start()
        if not im_thread or not im_thread.is_alive():
            if im_thread:
                logging.error(f"im_thread exited, try restart it!")
            im_thread = threading.Thread(
                target=subscribe_to_channel,
                args=(im_channel_config, consume_im_msg),
                name="im_thread"
            )
            im_thread.start()

        # check every 5 seconds.
        time.sleep(5)


if __name__ == '__main__':
    # subscribe_to_channel(chan_cfg=im_channel_config, callback=consume_im_msg)
    subscribe_by_thread()
