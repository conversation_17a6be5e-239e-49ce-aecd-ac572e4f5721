import time
import json
import logging
import uuid # 用于生成唯一的客户端ID后缀
import threading

import paho.mqtt.client as mqtt

def publish_mqtt_command_v5(connection_info: dict, message_body) -> bool:
    """
    使用 paho-mqtt (MQTTv5) 连接到 MQTT Broker 并发布一条消息到 commandTopic。

    参数:
    connection_info (dict): 包含 MQTT 连接参数的字典。
        期望键: 'broker', 'mqttUsername', 'mqttToken',
                 'clientId' (将作为前缀), 'commandTopic'
    message_body (str | dict | list | bytes): 要发布的消息体。
        如果是 dict 或 list，会自动转换为 JSON 字符串。

    返回:
    bool: 如果消息成功排队等待发布 (QoS 0) 或已确认发布 (QoS > 0)，则返回 True，否则返回 False。
    """
    broker_address = connection_info['broker']
    mqtt_username = connection_info['mqtt_username']
    mqtt_password = connection_info['mqtt_token'] # mqttToken 是密码
    publisher_client_id = connection_info['client_id']
    command_topic = connection_info['command_topic']
    port = 1883  # 阿里云 MQTT 通常使用 1883 端口并启用 TLS
    qos_level = 1 # 使用 QoS 1 以获得送达 broker 的确认

    payload_to_send = json.dumps(message_body)

    connected_event = threading.Event()
    published_event = threading.Event()
    publish_success = False

    # MQTTv5 的回调函数签名通常包含 properties 参数
    def on_connect(client, userdata, flags, reason_code, properties=None): # MQTTv5 使用 reason_code 和 properties
        nonlocal publish_success
        if reason_code == mqtt.MQTT_ERR_SUCCESS: # mqtt.MQTT_ERR_SUCCESS 也是 0
            logging.info(f"发布者 (v5) {publisher_client_id} 成功连接到 MQTT Broker: {broker_address}")
            # MQTTv5 的 publish 方法可以接受 properties 参数，如果需要的话
            # publish_properties = mqtt.Properties(mqtt.PacketTypes.PUBLISH)
            # publish_properties.UserProperty = [("my-prop", "my-value")]
            # result, mid = client.publish(command_topic, payload_to_send, qos=qos_level, properties=publish_properties)
            result, mid = client.publish(command_topic, payload_to_send, qos=qos_level) # 简单发布
            if result == mqtt.MQTT_ERR_SUCCESS:
                logging.info(f"消息已成功排队 (MID: {mid}) 到主题 '{command_topic}'")
                if qos_level == 0:
                    published_event.set()
                publish_success = True
            else:
                logging.error(f"发布消息失败，错误码: {result}")
                publish_success = False
                published_event.set()
        else:
            logging.error(f"发布者 (v5) 连接失败，原因码: {reason_code} ({mqtt.connack_string(reason_code)})")
            publish_success = False
            published_event.set()
        connected_event.set()

    def on_disconnect(client, userdata, reason_code, properties=None): # MQTTv5 使用 reason_code 和 properties
        logging.info(f"发布者 (v5) {publisher_client_id} 已从 MQTT Broker 断开连接，原因码: {reason_code}")
        if not published_event.is_set():
            published_event.set()

    def on_publish(client, userdata, mid): # on_publish 签名在 paho-mqtt 中对于 v5 似乎没有改变
        logging.info(f"消息 (MID: {mid}) 已成功发布到 Broker。")
        published_event.set()

    # 1. 创建 MQTT 客户端实例，指定 MQTTv5 协议
    publisher_client = mqtt.Client(client_id=publisher_client_id, protocol=mqtt.MQTTv5)

    # 2. 设置用户名和密码
    publisher_client.username_pw_set(mqtt_username, mqtt_password)

    # 3. 配置 TLS/SSL
    # publisher_client.tls_set() # 阿里云 MQTT 通常需要 TLS

    # 4. 绑定回调函数
    publisher_client.on_connect = on_connect
    publisher_client.on_disconnect = on_disconnect
    if qos_level > 0:
        publisher_client.on_publish = on_publish

    # 5. 启动网络循环
    publisher_client.loop_start()

    # 6. 连接到 MQTT Broker
    #    MQTTv5 的 connect 方法也可以接受 properties 参数，如果需要的话
    #    connect_properties = mqtt.Properties(mqtt.PacketTypes.CONNECT)
    #    connect_properties.SessionExpiryInterval = 3600 # 例如，设置会话过期间隔
    #    publisher_client.connect(broker_address, port, keepalive=60, properties=connect_properties)
    try:
        logging.info(f"发布者 (v5) {publisher_client_id} 正在连接到 {broker_address}:{port}...")
        publisher_client.connect(broker_address, port, keepalive=60) # 简单连接
    except Exception as e:
        logging.exception(f"发布者 (v5) 连接时发生异常: {e}")
        return False

    # 7. 等待连接和发布
    connection_timed_out = not connected_event.wait(timeout=10)
    if connection_timed_out:
        logging.error(f"发布者 (v5) 连接超时。")
        publish_success = False
    elif publish_success:
        publish_timed_out = not published_event.wait(timeout=10)
        if publish_timed_out:
            logging.error(f"等待消息 (v5) 发布确认超时 (QoS {qos_level})。")
            if qos_level > 0:
                 publish_success = False

    # 8. 停止网络循环并断开连接
    publisher_client.loop_stop()
    # MQTTv5 的 disconnect 方法也可以接受 properties 参数
    # disconnect_properties = mqtt.Properties(mqtt.PacketTypes.DISCONNECT)
    # disconnect_properties.ReasonString = "Client initiated disconnect"
    # publisher_client.disconnect(properties=disconnect_properties)
    publisher_client.disconnect() # 简单断开
    logging.info(f"发布者 (v5) {publisher_client_id} 已清理并退出。")

    return publish_success

if __name__ == '__main__':
    connection_params_v5 = {
        'broker': 'mqtt-cn-to349uby601.mqtt.aliyuncs.com',
        'mqttUsername': 'Signature|LTAI5tGEmDcdfJyLpP3GXUZf|mqtt-cn-to349uby601',
        'mqttToken': 'mhVImy/+kCVUOy7koWx1ahA6B+4=',
        'clientId': 'GID_iot_dev@@@1904790616680693760-jTd8gL',
        'topic': 'spotlight-message-dev/1904790616680693760/device-data',
        'commandTopic': 'spotlight-message-dev/1904790616680693760/device-command'
    }

    message_dict_v5 = {
        "command_id": f"cmd-v5-{uuid.uuid4().hex[:4]}",
        "protocol_version": 5,
        "action": "trigger_event",
        "payload": {"source": "python_v5_publisher"},
        "timestamp": time.time()
    }
    logging.info(f"\n--- 尝试使用 MQTTv5 发布字典消息 ---")
    success_v5 = publish_mqtt_command_v5(connection_params_v5, message_dict_v5)
    logging.info(f"MQTTv5 字典消息发布结果: {'成功' if success_v5 else '失败'}")
