import json
import logging

from rocketmq.v5.model.message import Message

from vui_svr.worker.common_tools import user_id_from_token, mqtt_auth_from_token, send_message_to_vui
from vui_svr.worker.llm_producer import publish_mqtt_command_v5

CAR_STATUS_MAP = {
    2: "有司机接单了",
    3: "当前订单改派中",
    4: "订单改派成功",
    5: "订单改派失败",
    #  20: "订单服务完成",
    99: "服务取消",
}


def consume_car_msg(msg: Message):
    data = json.loads(msg.body)
    token = data.get("accessToken") or data['user_info'].get("user_token")
    if not token:
        logging.error(f"Get accecctoken failed from msg, skip it! {data}")
        return True
    return _do_push_and_send(token=token, data=data)


def _do_push_and_send(token: str, data: dict):
    user_id = user_id_from_token(token)
    mqtt_auth = mqtt_auth_from_token(token)

    print(user_id, mqtt_auth)

    if data.get("type") == "calendar":
        push_info = f"{data['remind_content']}"
        if data["place"]:
            push_info += f", 地点在 {data['place']}"
    else:
        if data["channel"] != "caocao":
            logging.error(f"skip unknown channel, data: {data}")
            return True

        code: int = data["subStatus"]
        # code = 20
        if code == 2:
            push_info = CAR_STATUS_MAP[code]

            additional = json.loads(data["additionalInfo"])
            driver_info = additional.get("driverInfoVo", {})
            if driver_info and driver_info.get("card"):
                push_info += f", {driver_info.get('color', '')}{driver_info.get('carBrand', '')}, 车牌号{driver_info['card']}"
            if car_status := additional.get('car_status'):
                # if rd := car_status.get("remain_distance"):
                #     push_info += f", 距您{round(rd/1000, 1):g}公里" if rd > 1000 else "距您不到一公里"
                if rt := car_status.get("remain_time"):
                    push_info += f"大约{round(rt / 60, 1):g}分钟到" if rt >= 60 else "一分钟内到"
        else:
            if code == 10 and data["serviceStatus"] == 2:
                push_info = "司机已经到达上车点, 别迟到了"
            elif code == 20 and data["serviceStatus"] == 4:
                push_info = "到地儿了,车费我先帮你付了"
            else:
                push_info = CAR_STATUS_MAP.get(code, "")

        logging.info(f"cur push_info, {push_info}")
        if not push_info:
            logging.error(f"skip unknown sub_status, data: {data}")
            return True

    # SEND MESSAGE TO VUI
    vui_data = {"uid": str(user_id), "push_info": {"intent_id": "10007", "message": push_info}}
    send_res = send_message_to_vui(data=vui_data)
    if not send_res:
        logging.error(f"[{user_id}]send to vui failed!")
        return False

    # PUSH MESSAGE TO MQTT
    mqtt_data = {"type": "wake", "params": {}}
    res_bool = publish_mqtt_command_v5(connection_info=mqtt_auth, message_body=mqtt_data)
    if not res_bool:
        logging.error(f"[{user_id}]send to mqtt failed!")
        return False

    return True


if __name__ == '__main__':
    token = ""
    res = _do_push_and_send(token=token, data=dict())
    print(f"send result: {res}")
