import json
import time
import datetime
import logging
import sys

class DRAgent():
    def __init__(self, bot_id, bot_name):
        self.bot_id = bot_id
        self.bot_name = bot_name
        logging.info(f"GPT config, botid:{bot_id}, botname:{bot_name}")

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        pass

    def chat(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = {}): 
        code = 0
        answer = {}
        answer["new_task"] = chat_msg
        answer["discard_task"] = ""
        answer["out_vars"] = {}
        answer["scene_id"] = "-1"
        err_msg = ""
        return code, err_msg, answer, "dr", 0, 0, 0 

if __name__ == '__main__':
    pass
