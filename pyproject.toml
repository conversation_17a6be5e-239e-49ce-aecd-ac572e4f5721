[project]
name = "ai-svr"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "elasticsearch>=9.0.1",
    "emoji>=2.14.1",
    "fastapi[standard]>=0.115.13",
    "flask>=3.1.0",
    "gunicorn>=23.0.0",
    "mysql-connector-python>=9.3.0",
    "openai>=1.92.2",
    "opentelemetry-exporter-otlp>=1.34.1",
    "paho-mqtt>=2.1.0",
    "pillow>=11.2.1",
    "pyahocorasick>=2.1.0",
    "pydantic-settings>=2.9.1",
    "redis[hiredis]>=6.0.0",
    "requests>=2.32.3",
    "rocketmq-python-client>=5.0.6",
    "schedule>=1.2.2",
    "sqlalchemy>=2.0.40",
    "structlog>=25.4.0",
]

[dependency-groups]
dev = []
