body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f4f4f4;
}

.chat-container {
    max-width: 600px;
    margin: 50px auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.chat-window {
    height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    padding: 10px;
    margin-bottom: 20px;
}

.message {
    margin-bottom: 10px;
}

.user {
    font-weight: bold;
}

.text {
    margin-left: 10px;
}

.time {
    color: #999;
    margin-left: 10px;
    font-size: 0.85em;
}

form {
    display: flex;
}

input[type="text"] {
    flex-grow: 1;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    padding: 10px 20px;
    border: none;
    background-color: #28a745;
    color: white;
    cursor: pointer;
    border-radius: 4px;
}

button:hover {
    background-color: #218838;
}

