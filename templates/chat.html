<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Page</title>
    <link rel="stylesheet" href="/static/style.css">
</head>
<body>
    <div class="chat-container">
        <div class="chat-window" id="chat-window">
            <!-- 聊天记录显示 -->
            {% for entry in chat_history %}
            <div class="message">
                <span class="user">{{ entry.user }}:</span> 
                <span class="text">{{ entry.message }}</span> 
                <span class="time">[{{ entry.time }}]</span>
            </div>
            {% endfor %}
        </div>
        <!-- 等待效果占位符 -->
        <div id="loading-message" style="display:none;" class="loading">
            Getting response, please wait...
        </div>
        <form id="chat-form">
            <input type="text" id="message-input" placeholder="Type your message here" required>
            <button type="submit">Send</button>
        </form>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
	function fetchMessages() {
	    $.get('/chat/history', function(data) {
	        $('#chat-window').empty(); // 清空现有的聊天记录
	        data.forEach(function(entry) {
	            $('#chat-window').append(`
	                <div class="message">
	                <span class="user">${entry.user}:</span> 
	                <span class="text">${entry.message}</span> 
	                <span class="time">[${entry.time}]</span>
	                </div>
	            `);
	        });
	    });
	}

	// 每5秒检查一次新消息
        setInterval(fetchMessages, 1000);

	// 处理发送消息的请求
        $('#chat-form').on('submit', function(e) {
            e.preventDefault();
            let message = $('#message-input').val();
            if (message) {
                // 显示“正在发送...”的效果
                $('#loading-message').show();

                $.post('/chat/send', {message: message}, function(response) {
                    $('#message-input').val('');
                    // 隐藏“正在发送...”的效果
                    $('#message-input').val('');
                    location.reload(); // 重新加载页面以更新聊天记录
                });
            }
        });
    </script>
</body>
</html>

