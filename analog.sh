LOG_FILE=$1

grep "request start, reqid" $LOG_FILE | awk -F']' '{ time_info = substr($1, 2, length($1)-5); req_id=substr($5,3); uid=substr($6,2); arr_size = split($8,pieces,/, /); for (i = 1; i< arr_size; i ++) { block_size = split(pieces[i], blocks, ": "); if (blocks[block_size-1] ~ /msg/) msg = blocks[block_size]; } OFS="\t"; print req_id, uid, msg, time_info; }' > reqid.req


grep "Intent finished, ret" $LOG_FILE | grep rule_id | awk -F']' '{ req_id=substr($5,3); arr_size = split($8,pieces,/, /); rule_id = ""; rule_name=""; app_name = ""; for (i = 1; i< arr_size; i ++) { block_size = split(pieces[i], blocks, ": "); if (blocks[block_size-1] ~ /rule_id/) rule_id = blocks[block_size]; if (blocks[block_size-1] ~ /rule_name/) rule_name = blocks[block_size]; if (blocks[block_size-1] ~ /app_name/) { app_name = split(blocks[block_size], app_list, /'\''/); app_name = app_list[2]; app_ori = blocks[block_size]; } } OFS="\t"; print req_id, substr(rule_id, 2, length(rule_id)-2), substr(rule_name, 2, length(rule_name) - 2), app_name; }' > reqid.reco


grep "op_get_msg" $LOG_FILE | grep "origin resp" | awk -F'[' '{ req_id=substr($6, 0, length($6)-1); arr_size = split($9, pieces, /", "/); status=0; err_msg=pieces[3]; if (pieces[2] ~ /success/) { status=1; err_msg = "NULL"; } OFS="\t"; print req_id, status, err_msg;}' > reqid.success


awk -F'\t' 'BEGIN { while ((getline < "reqid.success") > 0) { sucMap[$1]=$2"\t"$3; } while ((getline < "reqid.reco") > 0) { recoMap[$1]=$2"\t"$3"\t"$4; } } { reqid=$1; uid=$2; query=$3; time_info=$4; OFS="\t"; reco_info = "NULL\tNULL\tNULL"; if (reqid in recoMap) {reco_info=recoMap[reqid]; } suc_info = "NULL\tNULL"; if (reqid in sucMap) suc_info=sucMap[reqid]; day_info = substr(time_info, 1,10); hour_info=substr(time_info, 12, 2); print day_info, hour_info, reqid, uid, query, reco_info, suc_info }' reqid.req > reqid.details
