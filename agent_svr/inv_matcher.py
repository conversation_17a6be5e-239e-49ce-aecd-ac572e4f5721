from .text_matcher import text_matcher
from typing import Any, Dict
from collections import defaultdict

import json
import re
import requests
import structlog

logger = structlog.get_logger(__name__)

class inv_matcher(text_matcher):
    def __init__(self):
        pass

    def build_inverted_index(self, filename):
        inverted_index = {}
        lines = []
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f):
                line_content = line.rstrip('\n')
                lines.append(line_content)
                for c in line_content:
                    if c not in inverted_index:
                        inverted_index[c] = set()
                    inverted_index[c].add(line_num)

        logger.debug(f'inv_matcher, loading file:{filename}, doc_cnt:{len(lines)}')
        return inverted_index, lines

    def init_matcher(self, file_path):
        self.index_dict = {}
        self.summary_dict = {}

        file_list = self.get_files_in_directory(file_path)
        logger.debug(f'inv_matcher, path:{file_path}, filecnt:{len(file_list)}')

        for file_item in file_list:
            file_name = file_item.split("/")[-1]
            logger.debug(f'inv_matcher, Loading inv table file: {file_item}, name:{file_name}')
            self.index_dict[file_name], self.summary_dict[file_name] = self.build_inverted_index(file_item)

            logger.debug(f'inv_matcher, file_name:{file_name}, inv_cnt:{len(self.index_dict[file_name])}, doc_cnt:{len(self.summary_dict[file_name])}')

        logger.debug(f'inv_matcher, all invert index built')
        return 0

    def search(self, inverted_index, lines, query, threshold):
        unique_chars = set(query)
        total_chars = len(unique_chars)
        if total_chars == 0:
            return []
                                    
        # 使用字典统计每个文档的命中次数
        doc_hits = defaultdict(int)
                                            
        # 单次遍历构建命中计数
        for char in unique_chars:
            if char in inverted_index:
                for doc_id in inverted_index[char]:
                    doc_hits[doc_id] += 1
                                                                                                        
        # 预计算阈值对应的最小命中数（考虑浮点精度）
        min_hits = threshold * total_chars - 1e-9  # 处理浮点精度问题
                                                                                                                    
        # 并行完成过滤和结果生成
        results = [
            (hits / total_chars, lines[doc_id])
            for doc_id, hits in doc_hits.items()
            if hits >= min_hits
        ]
                                                                                                                                
        # 稳定排序：按命中率降序，相同命中率按原文升序
        results.sort(key=lambda x: (-x[0], x[1]))
        return results

    def match(self, req_id, user_id, global_conversation_id, intent_id, key_name, key_val, debug=False):
        index_name = f'{intent_id}_{key_name}'
        # logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] inv_matcher, match start, key_name:{key_name}, key_val:{key_val}, index_name:{index_name}')
        inv_table = self.index_dict.get(index_name, {})
        summary_table = self.summary_dict.get(index_name, {})

        ret_val = key_val 
        if inv_table and summary_table and type(key_val) == str:
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] inv_matcher, index cnt: {len(summary_table)}')
            ret_list = self.search(inv_table, summary_table, key_val, 0.5)
            
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] inv_matcher, match ret: {ret_list}')
            if ret_list and ret_list[0][0] > 0.7:
                ret_val = ret_list[0][1][0:len(key_val)]

        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] inv_matcher, val:{ret_val}')
        return ret_val

if __name__ == '__main__':
    logger.getLogger('root').setLevel(logger.DEBUG)

    test_matcher = inv_matcher()
    test_matcher.init_matcher("./wordlist.inv")

    req_id = "111"
    user_id = "222"
    conversation_id = "333"
    intent_id = "10002"
    key_name = "shop_name"
    key_val = "金鱼嘉华大厦"

    ret_val = test_matcher.match(req_id, user_id, conversation_id, intent_id, key_name, key_val)
    logger.info(f'key_val: {ret_val}')
