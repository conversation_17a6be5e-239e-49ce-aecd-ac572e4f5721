from typing import Annotated

from fastapi import APIRouter, Depends, File, Header, UploadFile
from pydantic import BaseModel

from agent_svr.agents import task_bots, task_agent


class SceneModelTestInput(BaseModel):
    req_id: str
    user_id: str
    conversation_id: str

    intent_id: str
    coords: str
    hist_chat: str
    input_msg: str
    intent_task_outvar: dict


internal_test_router = APIRouter()


@internal_test_router.post("/scene")
def internal_scene_model_test(inp: SceneModelTestInput):
    last_task_msg = f"用户:{inp.input_msg}"
    task_msg = f"{inp.hist_chat}" if inp.hist_chat else last_task_msg

    code, err_msg, task_ret_msg, model_name, prompt_token, output_token, time_cost = task_agent.task_llm_build_scene(
        user_id=inp.user_id,
        req_id=inp.req_id,
        global_conversation_id=inp.conversation_id,
        intent_id=inp.intent_id,
        coords=inp.coords,
        task_msg=task_msg,
        last_task_msg=last_task_msg,
        intent_task_outvar=inp.intent_task_outvar
    )
    return {
        "code": code,
        "err_msg": err_msg,
        "task_ret_msg": task_ret_msg,
        "model_name": model_name,
        "prompt_token": prompt_token,
        "output_token": output_token,
        "time_cost": time_cost,
    }
