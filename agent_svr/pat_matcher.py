from .text_matcher import text_matcher
from .ac_extractor import ac_extractor
import json
import re
import requests
import structlog

logger = structlog.get_logger(__name__)


class pat_matcher(text_matcher):
    def __init__(self):
        pass
                                                        
    def init_matcher(self, addr, omit_list):
        self.addr = f"{addr}"
        self.omit_list = omit_list
        return 0

    def parse_rule(self, rule_list):
        ret = []
        for rule_idx, rule_item in enumerate(rule_list):
            match_list =  re.finditer(r"<.*?>", rule_item)
            rule_pieces = []
            param_pieces = []

            last_pos = 0
            for match_item in match_list:
                match_pos = match_item.span()
                logger.debug(f"rule_str:{rule_item}, len:{len(rule_item)}, hitpos:{match_pos}")
                rule_pieces.append(rule_item[last_pos:match_pos[0]].lower())
                param_pieces.append(rule_item[match_pos[0]+1:match_pos[1]-1])
                last_pos = match_pos[1]

            if last_pos < len(rule_item):
                rule_pieces.append(rule_item[last_pos:].lower())

            ret.append({"patterns": rule_pieces, "params": param_pieces, "str": rule_item})
            #logger.debug(f'New rule insert: {ret[-1]}')

        return ret

    def get_rule(self, uid):
        headers = {'appkey': 'UMhmghBLK77BJyf5', 'Content-Type': 'application/json'}
        content = {"userId": uid}

        url = f'{self.addr}/{uid}'
        response = requests.post(url, headers = headers, json=content)
        rule_info = []
        if response:
            resp_info = response.json()
            if "data" in resp_info:
                rule_info = resp_info["data"]

        #rule_info = [{"id": "111", "name": "听音乐", "list": ["我想听<param1>", "播放<param1>", "给我播放<param1>吧"], "default_var":{}}]

        #logger.debug(f'url: {url}, origin rule: {rule_info}, type:{type(rule_info)}')
        ret = []
        for rule_item in rule_info:
            if "triggerPattern" in rule_item and rule_item["triggerPattern"]:
                rule_dict = json.loads(rule_item["triggerPattern"])
                if "pattern" in rule_dict and rule_dict["pattern"]:
                    parsed_rule_list = self.parse_rule(rule_dict["pattern"])
                    app_name = rule_item["appNames"]
                    if not app_name: app_name = ""
                    ret.append({"id": rule_item["id"], "name": rule_item["name"], "list": parsed_rule_list, "default_var": rule_dict["default_var"], "task_desc": rule_item["description"], "data_sample": rule_item["dataSample"], "answer_sample": rule_item["answerSample"], "answer_type": str(rule_item["answerType"]), "app_name": app_name})

        #logger.debug(f'instant rule: {ret}')
        return ret

    def key_match(self, input_str, pat_list):
        ch_set = set()
        for item in pat_list:
            for ch in item:
                ch_set.add(ch)

        rule_len = len(ch_set)
        hit_rule_len = 0
        hit_set = set()
        for ch in input_str:
            if ch in ch_set and ch not in hit_set:
                hit_rule_len += 1
                hit_set.add(ch)

        if rule_len == 0:
            score = 0
        else:
            score = hit_rule_len/rule_len

        is_hit = False
        if score > 0.5: is_hit = True

        return is_hit, score

    def del_punc(self, src):
        dest_list = []
        for ch in src:
            if ch not in {",", ".", "，", "。", "！", "!", "?", "？"}:
                dest_list.append(ch)

        if dest_list:
            src = ''.join(dest_list)
        else:
            src = ""
        return src

    def trim_punc(self, src):
        if src:
            start_pos = 0
            end_pos = len(src) - 1

            while start_pos <= end_pos and src[start_pos] in {",", ".", "，", "。", "！", "!", "?", "？"}:
                start_pos += 1
            while start_pos <= end_pos and src[end_pos] in {",", ".", "，", "。", "！", "!", "?", "？"}:
                end_pos -= 1
            if start_pos <= end_pos:
                src = src[start_pos:end_pos + 1]
        else:
            src = ""

        return src

    def pat_match(self, input_idx, input_str, rule_idx, rule_item, param_item, hit_path, debug):
        is_hit = False
        var_dict = {}

        # if debug:
        #     logger.debug(f'{hit_path}, {input_idx}/{len(input_str)}, {rule_idx}/{len(rule_item)}')

        if rule_idx <= len(rule_item):
            if input_idx >= len(input_str) and rule_idx >= len(rule_item): is_hit = True
            if not is_hit:
                for word in self.omit_list:                  # try omit words
                    if input_str.startswith(word, input_idx):
                        hit_path.append({"word": word, "rule": "omit"})
                        is_hit, var_dict = self.pat_match(input_idx + len(word), input_str, rule_idx, rule_item, param_item, hit_path, debug)
                        if is_hit: break
                        hit_path.pop()

            if rule_idx == len(rule_item):
                if input_idx >= len(input_str): is_hit = True
                if rule_idx == len(param_item):
                    var_dict[param_item[rule_idx - 1]] = input_str[input_idx:]
                    is_hit = True

            if not is_hit and rule_idx < len(rule_item):    # is () or []  or simple text, test match
                word = rule_item[rule_idx]
                match_start = input_str.find(word, input_idx)
                if (input_idx == 0 and match_start == 0) or (input_idx > 0 and match_start > input_idx):
                    logger.debug(f'input_str:{input_str}, word:{word}, start_idx:{input_idx}, hit_pos:{match_start}')
                    end_pos = match_start + len(word)
                    hit_path.append({"word": word, "rule": "text"})

                    is_hit, var_dict = self.pat_match(end_pos, input_str, rule_idx + 1, rule_item, param_item, hit_path, debug)
                    if is_hit:
                        if rule_idx >= 1:
                            param = input_str[input_idx:match_start]
                            var_dict[param_item[rule_idx - 1]] = param
                    hit_path.pop()

        return is_hit, var_dict
    
    def match(self, uid, input_str, blk_list, debug=False):
        input_str = self.del_punc(input_str)
        logger.debug(f'pat_matcher: match start, input:{input_str}')
        scene_list = self.get_rule(uid)
        result = []
        filt_rules = {}

        for scene_item in scene_list:
            rule_list = scene_item["list"]
            rule_id = str(scene_item["id"])
            rule_name = scene_item["name"]
            app_name = scene_item["app_name"]
            default_var = scene_item["default_var"]

            for rule_item in rule_list:
                hit_path = []
                is_hit, var_dict = self.pat_match(0, input_str, 0, rule_item["patterns"], rule_item["params"], hit_path, True)
                is_keyword_hit, hit_score = self.key_match(input_str, rule_item["patterns"])
                if is_keyword_hit:
                    filt_rules[rule_id] = {"rule_id": rule_id, "rule_name": rule_name, "rule_str": rule_item["str"], 
                            "app_names": app_name, "params": rule_item["params"], "task_desc": scene_item["task_desc"], 
                            "data_sample": scene_item["data_sample"], "answer_sample": scene_item["answer_sample"], 
                            "answer_type": scene_item["answer_type"]}

                logger.debug(f'rule_name:{rule_name}, score:{hit_score}')
                if is_hit:
                    for var_key, var_val in default_var.items():
                        if var_key  not in var_dict:
                            var_dict[var_key] = var_val
                    hit_blk_item = None
                    for var_key, var_val in var_dict.items():
                        for blk_item in blk_list:
                            if blk_item["wordB"] in var_val:
                                is_blk_hit = True
                                hit_blk_item = blk_item
                        var_dict[var_key] = self.trim_punc(var_val)

                    if not hit_blk_item:
                        param_dict_str = json.dumps(var_dict, ensure_ascii=False)
                        out_var = {"rule_id": rule_id, "rule_name": rule_name, "params": param_dict_str, 
                                "task_desc": scene_item["task_desc"], "data_sample": scene_item["data_sample"], 
                                "answer_sample": scene_item["answer_sample"], "answer_type": scene_item["answer_type"], 
                                "app_name": app_name, "ask": ""}
                        hit_ret = {"hit_rule": rule_name, "rule_id":rule_id, "new_task": input_str, "out_var": out_var, 
                                "scene_id": "1"}
                        if "fast_ret" in scene_item:
                            hit_ret["fast_ret"] = scene_item["fast_ret"]
                        result.append(hit_ret)
                        logger.debug(f'hit rule, ret:{result[-1]}')
                        break
                    else:
                        logger.debug(f'hit blk param, miss rule: {rule_item["patterns"]}, blk: {hit_blk_item}')
        
        final_hit = {}
        if len(result) > 0:
            final_hit = result[0]
        logger.debug(f'pat_matcher: match finished')
        return final_hit, filt_rules 


if __name__ == '__main__':
    test_mactcher = pat_matcher()
    test_mactcher.init_matcher("127.0.0.1/onecmd")
    default_vars = {}
    default_fastret = "稍等，我看看啊"

    input_str = "我想听明天会更好"
    #input_str = "给我播放明天会更好吧"
    match_ret = test_mactcher.match("cloud3", input_str, True)

    logger.debug(f"#######################, input:{input_str}")
    if len(match_ret) > 0:
        if "fast_ret" not in match_ret: match_ret["fast_ret"] = default_fastret
        logger.debug(f'hit, hit_rule:{match_ret["hit_rule"]}, rule_id:{match_ret["rule_id"]}, fast_ret: {match_ret["fast_ret"]}, new_task: {match_ret["new_task"]}, out_var: {match_ret["out_var"]}')
    else:
        logger.debug(f'miss')
