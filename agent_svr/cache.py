import redis

from agent_svr.settings import settings


class RedisClient:
    cache = None

    def __init__(self):
        self.cache = redis.from_url(
            settings.REDIS_URL,
            max_connections=1024,
            socket_keepalive=True,
            health_check_interval=30,
        )

    def __getattr__(self, name):
        return getattr(self.cache, name)

    def __getitem__(self, name):
        return self.cache[name]

    def __setitem__(self, name, value):
        self.cache[name] = value

    def __delitem__(self, name):
        del self.cache[name]


# redis_cli = RedisClient()
redis_cli = redis.from_url(
    settings.redis_dsn,
    max_connections=1024,
    socket_keepalive=True,
    health_check_interval=30,
)
