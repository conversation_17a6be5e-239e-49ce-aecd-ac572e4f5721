# coding=utf-8

import redis
import time
import json
import sys
import structlog
import re

from agent_svr.cache import redis_cli

logger = structlog.get_logger(__name__)

class AppAgent:
    def __init__(self):
        self.db_client = redis_cli
        self.app_expire_time = 300

    def get_appid(self, user_id, global_conversation_id, intent_id):
        profile_key = f"{user_id}_profile"
        item_key = f"{global_conversation_id}_{intent_id}_appid"
        profile_str = self.db_client.hget(profile_key, item_key)
        self.db_client.expire(profile_key, self.app_expire_time)
        if profile_str:
            profile_str = profile_str.decode("utf-8")

        if not profile_str:
            profile_str = ""
        return profile_str

    def set_appid(self, user_id, global_conversation_id, intent_id, appid):
        profile_key = f"{user_id}_profile"
        item_key = f"{global_conversation_id}_{intent_id}_appid"
        self.db_client.hset(profile_key, item_key, appid)
        self.db_client.expire(profile_key, self.app_expire_time)
        return 0

    def del_appid(self, user_id, global_conversation_id, intent_id):
        profile_key = f"{user_id}_profile"
        item_key = f"{global_conversation_id}_{intent_id}_appid"
        self.db_client.hdel(profile_key, item_key)
        return 0

    def match_appid(
        self,
        req_id,
        user_id,
        intent_id,
        intent_info,
        new_task_vars,
        global_conversation_id,
    ):
        app_id = self.get_appid(user_id, global_conversation_id, intent_id)
        default_app_id = ""
        hit_app_id = []
        need_change = True  # appid的dynamic rule一条都不中，就需要换

        # logger.debug(f'intent_config, id:{intent_id}, config:{intent_info[intent_id]}')
        # logger.info(
        #     f"[{req_id}][{user_id}][{global_conversation_id}] select appid start, appid:{app_id}, intent_id:{intent_id}, "
        #     f"intent_app_list:{intent_info[intent_id]['app_list']}"
        # )
        for app_idx, app_item in enumerate(intent_info[intent_id]["app_list"]):
            # logger.debug(
            #     f"[{req_id}][{user_id}][{global_conversation_id}] app_idx:{app_idx}, app_item:{app_item}"
            # )
            if app_idx == 0:
                default_app_id = app_item["app_id"]
            else:
                rule_list = app_item["rule_list"]
                hit_rule = False  # 任意Rule命中就算命中
                hit_dynamic = False  # 所有Dynamic都不中，才算Miss
                for rule_item in rule_list:
                    logger.debug(
                        f"[{req_id}][{user_id}][{global_conversation_id}] new rule_item:{rule_item}"
                    )
                    if rule_item["scene_id"] == "ALL":
                        hit_rule = True
                    else:
                        hit_pattern = True  # 所有pattern都符合才算命中
                        for pattern_key, pattern_val in rule_item["vars"].items():
                            logger.debug(
                                f"[{req_id}][{user_id}][{global_conversation_id}] pattern_key:{pattern_key}, "
                                f"pattern_val:{pattern_val}, new_task_vars:{new_task_vars}"
                            )
                            if pattern_key in new_task_vars:
                                if not re.match(
                                    pattern_val, new_task_vars[pattern_key]
                                ):
                                    hit_pattern = False
                            else:
                                hit_pattern = False

                    logger.debug(
                        f"[{req_id}][{user_id}][{global_conversation_id}] hit_pattern:{hit_pattern}"
                    )
                    if (
                        "dynamic" in rule_item
                        and rule_item["dynamic"] == "True"
                        and hit_pattern
                    ):
                        hit_dynamic = True
                    if hit_pattern:
                        hit_rule = True

                logger.debug(
                    f"[{req_id}][{user_id}][{global_conversation_id}] app_item:{app_item}"
                )
                if hit_rule:
                    hit_app_id.append(app_item["app_id"])
                if app_item["app_id"] == app_id and hit_dynamic:
                    need_change = False
                logger.debug(
                    f"[{req_id}][{user_id}][{global_conversation_id}] hit_rule:{hit_rule}, hit_dynamic:{hit_dynamic}, "
                    f"need_change:{need_change}"
                )

        logger.debug(
            f"[{req_id}][{user_id}][{global_conversation_id}] old appid:{app_id}, default_appid:{default_app_id}, "
            f"hit_app_id:{hit_app_id}, need_change:{need_change}"
        )
        if (app_id and need_change) or (not app_id):
            if hit_app_id:
                app_id = hit_app_id[0]
            else:
                app_id = default_app_id
        self.set_appid(user_id, global_conversation_id, intent_id, app_id)
        logger.info(
            f"[{req_id}][{user_id}][{global_conversation_id}] select appid end, final appid:{app_id}"
        )
        return app_id

    def clear_global_appid(self, user_id, global_conversation_id, intent_info):
        logger.info(f"[][{user_id}][{global_conversation_id}] All appid info cleared")
        for key, val in intent_info.items():
            self.clear_intent_appid(user_id, global_conversation_id, key)

    def clear_intent_appid(self, user_id, global_conversation_id, intent_id):
        logger.debug(
            f"[][{user_id}][{global_conversation_id}] Clearing context for user:{user_id}, intent:{intent_id}"
        )
        # update appid
        old_app_id = self.get_appid(user_id, global_conversation_id, intent_id)
        self.del_appid(user_id, global_conversation_id, intent_id)
        new_app_id = self.get_appid(user_id, global_conversation_id, intent_id)
        # logger.debug(f"old appid:{old_app_id}, new appid:{new_app_id}")


if __name__ == "__main__":
    app_agent = AppAgent()
    task_msg = " 附近有肯德基吗？"
    user_id = sys.argv[1]  # "test1"
    # user_id = "('36.112.191.173', 63381)"
    # user_id = "24542782532"
    req_id = "23dshfskjdf"
    conversation_id = "2342343"
    ts = time.time()

    # task_list = [{"msg":task_msg, "intent_id":intent_id, "status":"running", "req_id":req_id, "conversation_id":conversation_id, "time":ts}]
    # self.set_task_list(user_id, intent_id, task_list)

    # update conversation_id & chat hist & profile
    app_agent.clear_global_appid(user_id)

    print("All profile printed")
