import json

from agent_svr.settings import settings
from agent_svr.info_api import InfoAgent

from agent_svr.gpt_api_V4 import GPTAgent
from agent_svr.coze_api_V4 import CozeAgent
from agent_svr.langflow_api_V4 import LangflowAgent
from agent_svr.dify_api import Dify<PERSON>gent
from agent_svr.dr_api_V4 import DRAgent

from .request_api import RequestAgent
from .task_api import TaskAgent
from .app_api import AppAgent
from .status_api import StatusAgent

client_addr = {"WEB": settings.web_client, "APP": settings.app_client}
client_ver_dict = {"WEB": settings.web_client_ver, "APP": settings.app_client_ver}
run_type = settings.run_type  # APP/WEB


info_agent = InfoAgent()

client_req_agent = RequestAgent(ip=client_addr[run_type], run_type=run_type)
server_req_agent = RequestAgent(ip=client_addr["WEB"], run_type="WEB")
plugin_req_agent = RequestAgent(ip=settings.plugin_client, run_type=run_type)
phone_req_agent = RequestAgent(ip=settings.phone_client, run_type=run_type)
# token_agent = TokenAgent(ip = settings.token_client)

task_bots = {}
action_bots = {}

with open(settings.intent_config_file, "r", encoding="utf-8") as f:
    intent_info = json.load(f)

for intent_id, intent_item in intent_info.items():
    task_bot_conf = intent_item["task_bot"]
    if task_bot_conf["type"] == "gpt":
        model = task_bot_conf.get("model", "gpt4o")
        agent = GPTAgent(task_bot_conf["id"], task_bot_conf["name"], model)
    else:
        agent = DRAgent(task_bot_conf["id"], task_bot_conf["name"])
    task_bots[intent_id] = agent

    action_bot_conf = intent_item["action_bot"]
    if action_bot_conf["type"] == "coze":
        agent = CozeAgent(settings.coze_auth, action_bot_conf["id"], action_bot_conf["name"])
    elif action_bot_conf["type"] == "langflow":
        agent = LangflowAgent(settings.coze_auth, action_bot_conf["id"], action_bot_conf["name"])
    elif action_bot_conf["type"] == "dify":
        agent = DifyAgent("", action_bot_conf["id"], action_bot_conf["name"])
    else:
        raise ValueError(f"Invalid config type: {action_bot_conf['type']}")
    action_bots[intent_id] = agent

task_agent = TaskAgent(task_bots, intent_info, info_agent)
appid_agent = AppAgent()
status_agent = StatusAgent()
# timer_agent = TimerAgent(redis_client = info_agent.db_client, intent_info = intent_info, client_req_agent = client_req_agent,
#        task_agent = task_agent, report_agent = report_agent, info_agent = info_agent, role_dict = role_dict,
#        run_type = run_type, max_workers = global_setting_dict["TIMER_TASK_THREAD_CNT"])

# start timer daemon
# if global_setting_dict["TASK_EXP_TIME"] > 0 and global_setting_dict["NEED_TIMER"]:
#    timer_thread = threading.Thread(target=timer_agent.start_svr)
#    timer_thread.daemon = True
#    timer_thread.start()
