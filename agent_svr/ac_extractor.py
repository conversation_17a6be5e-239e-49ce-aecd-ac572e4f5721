from .text_extractor import text_extractor
import ahocorasick
import logging


class ac_extractor(text_extractor):
    def __init__(self):
        self.ac_operator = ahocorasick.Automaton()
        self.forword_index_id = {}
        self.forword_index_data = []

    # 插入词到Trie树
    def add_word(self, wordA, wordB, params, table_name):
        word_key = wordA
        forword_index = -1

        word_info = {
            "type": table_name,
            "word_len": len(wordA),
            "wordB": wordB,
            "params": params,
        }
        if word_key in self.forword_index_id:
            forword_index = self.forword_index_id[word_key]
            self.forword_index_data[forword_index].append(word_info)
        else:
            self.forword_index_data.append([word_info])
            forword_index = len(self.forword_index_data) - 1
            self.forword_index_id[word_key] = forword_index
            self.ac_operator.add_word(wordA, forword_index)

        return 0

        # 构建失败链接

    def build_index(self, file_path):
        file_list = self.get_files_in_directory(file_path)
        logging.debug(f"ac_extractor: Loading files in path:{file_path}")
        for file_item in file_list:
            logging.debug(f"Loading file: {file_item}")
            word_cnt = 0
            with open(file_item, "r") as file:
                for line in file:
                    line = line.rstrip("\n")
                    pieces = line.split("\t")
                    if len(pieces) >= 3:
                        wordA = pieces[0].lower()
                        wordB = pieces[1].lower()
                        params = pieces[2]

                        file_name = file_item.split("/")[-1]
                        self.add_word(wordA, wordB, params, file_name)
                        word_cnt += 1
            logging.debug(f"File loaded: {file_item}, wordcnt:{word_cnt}")

        self.ac_operator.make_automaton()
        logging.debug(f"ac_extractor: Tri index built")

    # 匹配字符串并输出结果
    def extract(self, string):
        logging.debug(f"ac_extractor: extract start, input:{string}")
        result = []

        for end, word_idx in self.ac_operator.iter(string):
            for word_info in self.forword_index_data[word_idx]:
                start = end - word_info["word_len"] + 1
                logging.debug(
                    f"ac_extractor: hit new word, start:{start}, end:{end}, wordB:{word_info['wordB']}, type:{word_info['type']}"
                )
                result.append(
                    {
                        "start": start,
                        "end": end,
                        "wordB": word_info["wordB"],
                        "params": word_info["params"],
                        "type": word_info["type"],
                    }
                )
        logging.debug(f"ac_extractor: extract finished")
        return result


if __name__ == "__main__":
    # 示例词表和字符串
    test_extractor = ac_extractor()
    test_extractor.build_index("wordlist")

    input_str = "帮我在附近的瑞幸点杯咖啡"
    ret = test_mactcher.extract(input_str)

    for ret_item in ret:
        hit_word = input_str[ret_item["start"] : ret_item["end"] + 1]
        logging.debug(
            f"hit, start:{ret_item['start']}, end:{ret_item['end']}, wordA:{hit_word}, wordB:{ret_item['wordB']}, type:{ret_item['type']}, params:{ret_item['params']}"
        )
