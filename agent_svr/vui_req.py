import requests
from urllib.parse import urljoin

from agent_svr.settings import settings


# simple agent service func wrapper for local func proxy.
def agent_wrapper(func):
    def wrapper(*args, **kwargs):
        body = {"func": func.__name__, "args": args, "kwargs": kwargs}

        url = urljoin(settings.vui_svc_url, "/internal/vui_func/exec")
        resp = requests.post(url=url, json=body)
        resp.raise_for_status()
        return resp.json()["result"]

    return wrapper


@agent_wrapper
def extract_memo_portrait(*args, **kwargs):
    pass


@agent_wrapper
def set_message_hist(*args, **kwargs):
    pass


@agent_wrapper
def message_push_and_send(*args, **kwargs):
    pass


@agent_wrapper
def message_push_with_merge_and_send(*args, **kwargs):
    pass
