import os
import json
from typing import List, Dict

from pydantic import BaseModel


class Settings(BaseModel):
    run_type: str
    run_mode: str

    console_log_level: str
    interaction_type: str

    web_client: List[str]
    web_client_ver: Dict[str, str]
    app_client: List[str]
    app_client_ver: Dict[str, str]
    token_client: List[str]

    coze_addr: str
    coze_auth: str

    langflow_addr: str
    n8n_addr: str
    dify_addr: str

    vui_svc_url: str

    log_dir: str
    llm_addr: str
    plugin_client: List[str]
    plugin_rules: str
    phone_client: List[str]

    role_config_file: str

    intent_config_file: str
    stop_word_file: str
    ner_word_path: str
    prompt_path: str
    single_intent_path: str
    context_intent_path: str
    task_rule_path: str
    param_inv_path: str

    memo_feature_config: str
    memo_extract_pattern: str

    redis_dsn: str

    # chatdb_pool_name: str
    # chatdb_pool_size: int
    # mysql_ip: str
    # mysql_port: int
    # mysql_user: str
    # mysql_pass: str
    # mysql_dbname: str

    vector_ip: str
    vector_port: str

    agent_role: str
    host_role: str
    expert_role: str

    llm_intent_all_model: str
    llm_intent_pos_model: str
    llm_chat_model: str
    llm_intent_instant: str
    embedding_model: str
    extract_model: str
    compress_model: str

    need_byebye_text: bool
    fail_retry_cnt: int

    def __getitem__(self, key: str):
        if not hasattr(self, key):
            raise KeyError(key)
        return getattr(self, key)


_setting_json = os.environ.get(
    "GLOBAL_SETTING_FILE", "./agent_svr/global_settings.json"
)

with open(_setting_json, "r", encoding="utf-8") as f:
    _settings_dict = json.load(f)

settings = Settings(**_settings_dict)
