import requests
import json
import time
import datetime
import structlog
import sys

from agent_svr.settings import settings


logger = structlog.get_logger(__name__)


class CozeAgent:
    def __init__(self, pat_auth, bot_id, bot_name):
        self.pat_auth = f"Bearer {pat_auth}"
        self.bot_id = bot_id
        self.bot_name = bot_name
        self.wait_time = 1
        self.retry_cnt = 180
        self.api_addr = settings.coze_addr
        # self.proxy = {"http":"127.0.0.1:7890"}
        self.proxy = None
        logger.info(
            f"Coze config, api:{self.api_addr}, botid:{bot_id}, botname:{bot_name}"
        )

    def kill_chat(
        self, req_id, user_id, global_conversation_id, conversation_id, chat_id
    ):
        addr = f"https://{self.api_addr}/v3/chat/cancel"
        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}
        content = {"chat_id": chat_id, "conversation_id": conversation_id}

        logger.info(
            f"[{req_id}][{user_id}][{global_conversation_id}] task cancel, conversatioin_id:{conversation_id}, "
            f"chat_id:{chat_id}"
        )
        response = requests.post(
            addr, headers=headers, json=content, proxies=self.proxy
        )
        resp_info = response.json()

        return resp_info["code"], resp_info["msg"]

    def op_send_req(
        self,
        req_id,
        user_id,
        global_conversation_id,
        chat_msg,
        custom_var={},
        conversation_id="",
    ):
        addr = f"https://{self.api_addr}/v3/chat?conversation_id={conversation_id}"
        if conversation_id is None or not conversation_id:
            addr = f"https://{self.api_addr}/v3/chat"

        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}
        content = {
            "bot_id": self.bot_id,
            "user_id": user_id,
            "stream": False,
            "auto_save_history": True,
            "additional_messages": [
                {"role": "user", "content": chat_msg, "content_type": "text"}
            ],
        }
        if len(custom_var) > 0:
            content["custom_variables"] = custom_var

        logger.debug(f"new req: {content}")
        response = requests.post(
            addr, headers=headers, json=content, proxies=self.proxy
        )
        resp_info = response.json()
        logger.debug(
            f"[{req_id}][{user_id}][{global_conversation_id}] send req, name:{self.bot_name}, resp:{resp_info}, "
            f"type:{type(resp_info)}"
        )
        chat_id = resp_info["data"]["id"]
        conversation_id = resp_info["data"]["conversation_id"]

        return resp_info["code"], resp_info["msg"], chat_id, conversation_id

    def op_check_status(
        self, req_id, user_id, global_conversation_id, chat_id, conversation_id
    ):
        addr = f"https://{self.api_addr}/v3/chat/retrieve?chat_id={chat_id}&conversation_id={conversation_id}"
        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}

        response = requests.get(addr, headers=headers, proxies=self.proxy)
        resp_info = response.json()
        status = resp_info["data"]["status"]
        # logger.debug(f'check status, resp:{resp_info}')

        return resp_info["code"], resp_info["msg"], status

    def op_get_msg(
        self,
        req_id,
        user_id,
        global_conversation_id,
        chat_id,
        conversation_id,
        target="answer",
    ):
        addr = f"https://{self.api_addr}/v3/chat/message/list?chat_id={chat_id}&conversation_id={conversation_id}"
        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}

        response = requests.get(addr, headers=headers, proxies=self.proxy)
        resp_info = response.json()
        answer = resp_info["data"]
        logger.debug(
            f"[{req_id}][{user_id}][{global_conversation_id}] self.bot_name, origin resp: {resp_info}"
        )
        #        logger.debug(f'get msg, code:{resp_info["code"]}, msg:{resp_info["msg"]}, resp:{answer}, type:{type(answer)}')

        for item in answer:
            if item["type"] == target and not "error" in item["content"]:
                return resp_info["code"], resp_info["msg"], item["content"]
        return resp_info["code"], resp_info["msg"], ""

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        addr = f"https://{self.api_addr}/v1/conversation/create"
        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}

        response = requests.get(addr, headers=headers, proxies=self.proxy)
        resp_info = response.json()
        # print(resp_info)
        print(response)
        print(resp_info)
        conversation_id = resp_info["data"]["id"]

        return resp_info["code"], resp_info["msg"], conversation_id

    def op_list_conversation(self, conversation_id):
        addr = f"https://{self.api_addr}/v1/conversation/message/list?conversation_id={conversation_id}"
        headers = {"Authorization": self.pat_auth, "Content-Type": "application/json"}

        response = requests.get(addr, headers=headers, proxies=self.proxy)
        resp_info = response.json()
        content_list = resp_info["data"]
        # print(resp_info)

        return resp_info["code"], resp_info["msg"], content_list

    def create_chat(
        self,
        req_id,
        user_id,
        global_conversation_id,
        chat_msg,
        target,
        extra_header,
        ver_id,
        custom_var={},
        conversation_id="",
    ):
        for item_key in custom_var:
            if type(custom_var[item_key]) != str:
                custom_var[item_key] = json.dumps(
                    custom_var[item_key], ensure_ascii=False
                )

        code, err_msg, chat_id, conversation_id = self.op_send_req(
            req_id,
            user_id,
            global_conversation_id,
            chat_msg,
            custom_var,
            conversation_id,
        )
        return code, err_msg, chat_id, conversation_id

    def wait_chat_finish(
        self,
        req_id,
        user_id,
        global_conversation_id,
        conversation_id,
        chat_id,
        target,
        create_code,
    ):
        answer = ""
        status = "in_progress"

        # print(f"wait for chat response, chatid:{chat_id}")
        if create_code == 0:
            stage = "retrive"
            code = 0
            run_code = "fail"
            retry_id = 0

            while (
                not (status == "completed") and retry_id < self.retry_cnt and code == 0
            ):
                time.sleep(self.wait_time)
                code, err_msg, status = self.op_check_status(
                    req_id, user_id, global_conversation_id, chat_id, conversation_id
                )
                logger.debug(
                    f"[{req_id}][{user_id}][{global_conversation_id}] check status, botid:{self.bot_name}, "
                    f"stage:{stage}, code:{code}, msg:{err_msg}, status:{status}, retry:{retry_id}"
                )
                retry_id += 1

                if status == "failed" or status == "canceled":
                    break

            if code == 0 and status == "completed":
                stage = "message_list"
                try:
                    code, err_msg, answer = self.op_get_msg(
                        req_id,
                        user_id,
                        global_conversation_id,
                        chat_id,
                        conversation_id,
                        target,
                    )
                    answer = json.loads(f"{answer}")
                    run_code = answer["code"]
                    if "err_msg" not in answer:
                        answer["err_msg"] = ""
                except:
                    code = -1
                    err_msg = "Err when parsing answer from resp"
                    logger.error(
                        f"[{req_id}][{user_id}][{global_conversation_id}] bot finish, error when getting resp, "
                        f"botid:{self.bot_name}, stage:{stage}, code:{code}, msg:{err_msg}, answer:{answer}, "
                        f"type:{type(answer)}"
                    )
                    answer = {
                        "output": "",
                        "check": "None",
                        "status": -1,
                        "code": "fail",
                        "err_msg": "not json format",
                    }
            else:
                logger.error(
                    f"[{req_id}][{user_id}][{global_conversation_id}] bot finish, error when waiting, "
                    f"botid:{self.bot_name}, stage:{stage}, code:{code}, status:{status}, err msg:{err_msg}"
                )
        else:
            status = "failed"
            logger.error(
                f"[{req_id}][{user_id}][{global_conversation_id}] Coze task sumbit err, stage:{stage}, code:{code}, "
                f"err_msg:{err_msg}, status:{status}"
            )

        return code, status, err_msg, run_code, answer

    def chat(
        self,
        req_id,
        user_id,
        global_conversation_id,
        chat_msg,
        target,
        custom_var={},
        conversation_id="",
    ):
        stage = "chat"
        code, err_msg, chat_id, conversation_id = self.op_send_req(
            req_id,
            user_id,
            global_conversation_id,
            chat_msg,
            user_id,
            custom_var,
            conversation_id,
        )
        answer = ""
        # print(f"stage:{stage}, code:{code}, msg:{err_msg}, chat_id:{chat_id}, conversation_id:{conversation_id}")

        if code == 0:
            stage = "retrive"
            code = 0
            run_code = "fail"
            status = "in_progress"
            retry_id = 0
            # logger.debug(f"stage:{stage}, code:{code}, msg:{err_msg}, status:{status}, retry:{retry_id}")

            while (
                not (status == "completed") and retry_id < self.retry_cnt and code == 0
            ):
                time.sleep(self.wait_time)
                code, err_msg, status = self.op_check_status(
                    req_id, user_id, global_conversation_id, chat_id, conversation_id
                )
                # print(f'check status, process status:{status}, idx:{retry_id}')
                logger.debug(
                    f"[{req_id}][{user_id}][{global_conversation_id}] check status, botid:{self.bot_name}, ' \
                        f'stage:{stage}, code:{code}, msg:{err_msg}, status:{status}, retry:{retry_id}"
                )
                retry_id += 1

                if status == "failed":
                    break

            if code == 0:
                stage = "message_list"
                try:
                    code, err_msg, answer = self.op_get_msg(
                        req_id,
                        user_id,
                        global_conversation_id,
                        chat_id,
                        conversation_id,
                        target,
                    )
                    answer = json.loads(f"{answer}")
                    run_code = answer["code"]
                    if "err_msg" not in answer:
                        answer["err_msg"] = ""
                except:
                    code = -1
                    err_msg = "Err when parsing answer from resp"
                    logger.error(
                        f"[{req_id}][{user_id}][{global_conversation_id}] bot finish, error when getting resp, "
                        f"botid:{self.bot_name}, stage:{stage}, code:{code}, msg:{err_msg}, answer:{answer}, "
                        f"type:{type(answer)}"
                    )
                    answer = {
                        "output": "",
                        "check": "None",
                        "status": -1,
                        "code": "fail",
                    }
        else:
            logger.error(
                f"[{req_id}][{user_id}][{global_conversation_id}] Coze req err, api:{stage}, code:{code}, msg:{err_msg}"
            )

        return code, err_msg, run_code, answer


if __name__ == "__main__":
    agent = CozeAgent(
        "pat_Ocabs6nDQEujO2VEOkiWMTFdg4RjFTsFGOU1ovijbJklsO5mbTrTOu3wOT1XOxAB",
        "7447798606897201187",
        "act_take",
    )
    code, msg, conversation_id = agent.op_new_conversation()
    user_id = sys.argv[1]

    input = '{"uid": "cloud3", "coords": "116.312564,40.059029", "intent_id": "10002", "req_id": "345a6d42-c718-467f-bfec-974673a35a7a", "conversation_id": "7448112610576384027", "input": "点一杯瑞幸的茉莉花拿铁", "hist_chat": "用户:帮我点杯咖啡吧; 你:附近700米有家瑞幸，还是点茉莉花拿铁吗？", "app_id": "com.lucky.luckyclient", "ts": "1734148881.2957933", "scene_id": 2, "shop_name": "瑞幸", "dish_name": "茉莉花拿铁", "dish_prefer": "", "shop_prefer": ""}'
    # custom_var = {'uid': 'cloud3', 'coords': '116.312564,40.059029', 'intent_id': '10002', 'req_id': '345a6d42-c718-467f-bfec-974673a35a7a', 'conversation_id': '7448112610576384027', 'input': '点一杯瑞幸的茉莉花拿铁', 'hist_chat': '用户:帮我点杯咖啡吧; 你:附近700米有家瑞幸，还是点茉莉花拿铁吗？', 'app_id': 'com.lucky.luckyclient', 'ts': '1734148881.2957933', 'scene_id': 2, 'shop_name': '瑞幸', 'dish_name': '茉莉花拿铁', 'dish_prefer': '', 'shop_prefer': ''}
    custom_var = {
        "uid": "cloud3",
        "coords": "116.312564,40.059029",
        "intent_id": "10002",
        "req_id": "345a6d42-c718-467f-bfec-974673a35a7a",
        "conversation_id": "7448112610576384027",
        "input": "点一杯瑞幸的茉莉花拿铁",
        "hist_chat": "用户:帮我点杯咖啡吧; 你:附近700米有家瑞幸，还是点茉莉花拿铁吗？",
        "app_id": "com.lucky.luckyclient",
        "ts": "1734148881.2957933",
        "scene_id": "2",
    }
    code, msg, answer = agent.chat(
        input, user_id, "answer", custom_var, conversation_id
    )
    print(
        f"input:{input}, output: {answer['output']}, detail: {answer['detail_info']}, status:{answer['status']}"
    )

#   input = "酒仙桥，2个人"
#   code, msg, answer = agent.chat(input, uid, conversation_id)
#   print(f'input:{input}, ans: {answer}')

#    code, msg, content_list = agent.op_list_conversation(conversation_id)
#    for item in reversed(content_list):
#        time_format = datetime.datetime.fromtimestamp(item["created_at"]).strftime('%Y%m%d %H:%M:%S')
#        print(f'conversation, msg:{item["content"]}, type:{item["content_type"]}, role:{item["role"]}, time:{time_format}')
