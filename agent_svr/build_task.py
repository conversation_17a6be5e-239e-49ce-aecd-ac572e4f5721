import json
import random
import time
import structlog

from concurrent.futures import ThreadPoolExecutor
from redis.exceptions import LockError

from agent_svr.settings import settings
from agent_svr.cache import redis_cli
from agent_svr.agents import (
    info_agent,
    task_agent,
    appid_agent,
    server_req_agent,
)
from agent_svr.user_timer import UserTimerScheduler
from agent_svr.vui_req import (
    message_push_and_send,
    message_push_with_merge_and_send
)

from agent_svr.agents import intent_info, action_bots

logger = structlog.get_logger(__name__)

# load agent style setting
with open(settings.role_config_file, 'r', encoding='utf-8') as file:
    role_config_dict = json.load(file)
for key in role_config_dict.keys():
    role_config_dict[key]["id"] = key
role_dict = {
    "default": role_config_dict[settings.agent_role],
    "host": role_config_dict[settings.host_role],
    "guest": role_config_dict[settings.expert_role]
}

user_timer = UserTimerScheduler(server_req_agent)

executor = ThreadPoolExecutor(max_workers=32)


def merge_details(src, dst, proc_name):
    if dst:
        src["time_details"].update(dst["time_details"])
        src["token_details"].update(dst["token_details"])
        src["prompt_token"] += dst["prompt_token"]
        src["output_token"] += dst["output_token"]
        src["time_details"][proc_name] = dst["time_cost"]

    return src

def build_task(req_id, intent_id, task_msg, user_id, chat_conversation_id, coords, hist_chat, intent_hist_chat, fast_ret, intent_task_msg, intent_task_outvar, intent_task_sceneid, global_conversation_id, fg_intent_id, fg_task_source, extra_header, pro_ver_id, token):
    time_start = time.time()
    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] building task start, msg:{task_msg}, intent:{intent_id}, '
                 f'conversation_id:{chat_conversation_id}, intent_msg:{intent_task_msg}, intent_vars:{intent_task_outvar}, '
                 f'intent_sceneid:{intent_task_sceneid}, hist_chat:{hist_chat}')
    run_details = {"time_cost": '0', "time_details": {}, "prompt_token" :0, "output_token" :0, "token_details": {}}

    if not info_agent.has_global_conversation_id(user_id):
        resp_msg = 'No Conversation'
        run_details["time_cost"] = f"{(time.time() - time_start):.3f}"
        ret_info = {'output': resp_msg, "code": -1, "info": resp_msg, "status" :0, 'detail_info': run_details}
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] building task end, code:{ret_info["code"]}, '
                     f'info:{ret_info["info"]}, intent:{intent_id}, conversation_id:{chat_conversation_id}, '
                     f'run_details:{run_details}')
        return ret_info, run_details

    curr_task = task_agent.get_task_item(user_id, global_conversation_id, intent_id, req_id)
    if not curr_task:
        try:
            with redis_cli.lock(f"{user_id}_{global_conversation_id}_0_build_task", timeout=20, blocking_timeout=3):
                try:
                    task_llm_tips = role_dict["default"]["script"].get("task_llm_tips", [])
                    timer_param = {"uid": user_id, "intent_id": intent_id, "coords": coords, "token": token,
                                   "tip_list": task_llm_tips, "pro_ver_id": pro_ver_id}
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] timer_param:{timer_param}')
                    user_timer.set_task(user_id, timer_param, 5)
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] after set')
                    user_timer.start(user_id)
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] before task llm, task_llm_tips:{task_llm_tips}')

                    # 增加垫话
                    resp_msg = task_llm_tips[random.randint(0, len(task_llm_tips) - 1)]
                    push_args = (req_id, coords, user_id, intent_id, "0", "-1", resp_msg, "", 0, chat_conversation_id,
                                 global_conversation_id, "on", "off", "info", "padding", -1)
                    send_args = (req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
                    message_push_and_send(push_args=push_args, send_args=send_args)

                    code, err_msg, resp_msg, new_task_item, discard_task_set, new_task_vars, build_task_run_details = \
                        task_agent.get_act_task(req_id, user_id, intent_id, coords, chat_conversation_id, task_msg, hist_chat,
                                                intent_hist_chat, fast_ret, intent_task_msg, intent_task_outvar,
                                                intent_task_sceneid, global_conversation_id, fg_intent_id, fg_task_source, "task")

                    # user_timer.stop(user_id)
                    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] stop task llm timer')
                    merge_details(run_details, build_task_run_details, "get_act_task")
                except Exception as e:
                    err_msg = "get task error"
                    logger.exception(f'[{req_id}][{user_id}][{global_conversation_id}] get task error')
                    code = 1
                    resp_msg = "出错了，请联系客服"
                    new_task_item = ""
                    discard_task_set = {}
                    new_task_vars = {}
                    build_task_run_details = None
                finally:
                    user_timer.stop(user_id)
        except LockError:
            logger.exception(f'[{req_id}][{user_id}][{global_conversation_id}] Error getting build task lock.')
            err_msg = "get build task lock error."
            code = 1
            resp_msg = "出错了，请联系客服"
            new_task_item = ""

        if code != 0: # or not(new_task_item["msg"]):
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error creating tasks, intent:{intent_id}'
                          f', code:{code}, err_msg:{err_msg}, task_item:{new_task_item}')
            push_args = (req_id, coords, user_id, intent_id, "0", "-1", resp_msg, "", 0, chat_conversation_id,
                                     global_conversation_id, "off", "off", "error", "must", -1)
            send_args = (req_id, coords, user_id, intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            message_push_and_send(push_args=push_args, send_args=send_args)

            run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
            ret_info = {'output': resp_msg, "code": -2, "info": "build task fail", "status" :0, 'detail_info': run_details}
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] building task end, code:{ret_info["code"]}, '
                         f'info:{ret_info["info"]}, intent:{intent_id}, conversation_id:{chat_conversation_id}, '
                         f'run_details:{run_details}')
            return ret_info, run_details

        app_id = appid_agent.match_appid(req_id, user_id, intent_id, intent_info, new_task_vars, global_conversation_id)
        new_task_item["app_id"] = app_id
        task_agent.push_task_list(user_id, intent_id, new_task_item, discard_task_set, global_conversation_id, req_id)
        logger.debug(f"[{req_id}][{user_id}][{global_conversation_id}] push new task into list: {new_task_item}, task list updated")
    else:
        logger.debug(f"[{req_id}][{user_id}][{global_conversation_id}] task already in list, {curr_task}")

    logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] Start do act for user:{user_id} in intent:{intent_id}')

    # make all do_act background tasks
    executor.submit(do_act, user_id, coords, global_conversation_id, fg_intent_id, fg_task_source, None, extra_header, pro_ver_id, token,
                  req_id, chat_conversation_id)
    # merge_details(run_details, resp["run_details"], "do_act")

    run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
    ret_info = {'output': "", "code": 0, "info": "sample req", "status" :0, 'detail_info': run_details}
    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] building task end, code:{ret_info["code"]}, '
                 f'info:{ret_info["info"]}, intent:{intent_id}, conversation_id:{chat_conversation_id}, run_details:{run_details}')
    return ret_info, run_details

def do_act(user_id, coords, global_conversation_id, fg_intent_id, fg_task_source, target_task, extra_header, pro_ver_id, token, req_id = "", chat_conversation_id = ""):
    logger.info(f'[0][{user_id}][{global_conversation_id}] do_action start, user:{user_id}')
    time_start = time.time()
    run_details = {"time_cost": '0', "time_details": {}, "prompt_token" :0, "output_token" :0, "token_details": {}}
    token = extra_header.get("Authorization", "")

    if not info_agent.has_global_conversation_id(user_id):
        resp_msg = 'No Conversation'
        ret_info = {"output": resp_msg, "code": -1, "info": resp_msg, "status" :0, "detail_info": run_details}
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action end, code:{ret_info["code"]}, '
                     f'info:{ret_info["info"]}, intent:0, conversation_id:{chat_conversation_id}, '
                     f'run_details:{run_details}')
        return ret_info

    # 获取优先级最高的task
    task_lock_key = f"{user_id}_{global_conversation_id}_0_do_act"
    # task_lock = RedisLock(redis_client, task_lock_key)
    try:
        with redis_cli.lock(task_lock_key, timeout=20, blocking_timeout=3):
            resp_msg = ""
            curr_task = target_task

            # 优先执行target_task
            if not target_task:
                curr_task, resp_msg, talk_cmd, mic_cmd = task_agent.get_top_task(
                    user_id, "0", global_conversation_id, fg_task_source, fg_intent_id
                )

            new_intent_id = "0"
            if curr_task: new_intent_id = curr_task.get("intent_id", "0")

            if resp_msg:
                push_args = (req_id, coords, user_id, new_intent_id, "-1", "-1", resp_msg, "", -1, chat_conversation_id,
                                         global_conversation_id, talk_cmd, mic_cmd, "info", "must", -1)
                send_args = (req_id, coords, user_id, new_intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] get top task, intent_id:{new_intent_id}, '
                             f'resp_msg:{resp_msg}, talk_cmd:{talk_cmd}, mic_cmd:{mic_cmd}')
                message_push_and_send(push_args=push_args, send_args=send_args)

            if not curr_task:
                logger.debug(f"[{req_id}][{user_id}][{global_conversation_id}] no valid task, return")
                run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
                ret_info = {"output": "" ,"code": 1, "info": "refuse, no task", "status" :0, "detail_info": run_details}
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action end, code:{ret_info["code"]}, '
                             f'info:{ret_info["info"]}, intent:{new_intent_id}, conversation_id:{chat_conversation_id}, '
                             f'run_details:{run_details}')
                return ret_info        # FUCK

            new_intent_source = curr_task["task_source"]
            req_id = curr_task["req_id"]
            chat_conversation_id = curr_task["conversation_id"]
            coords = curr_task["coords"]
            hist_chat = curr_task["hist_chat"]
            intent_hist_chat = curr_task["intent_hist_chat"]
            new_task_msg = curr_task["msg"]
            new_task_appid = curr_task["app_id"]
            intent_task_sceneid = curr_task["scene_id"]
            fast_ret = curr_task["fast_ret"]
            logger.debug(f"[{req_id}][{user_id}][{global_conversation_id}] new task for running: {curr_task}")

            # start build task
            if settings.interaction_type in {"multi_round", "single_round"}:
                if not resp_msg and fast_ret:
                    push_args = (req_id, coords, user_id, new_intent_id, "-1", "-1", fast_ret, "", -1,
                                             chat_conversation_id, global_conversation_id, "on", "off", "task", "omit", -1)
                    send_args = (req_id, coords, user_id, new_intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
                    message_push_and_send(push_args=push_args, send_args=send_args)

            # pop task out
            task_agent.set_task_param(user_id, new_intent_id, req_id, "running", global_conversation_id, fg_intent_id)
            # task_agent.pop_task(user_id, new_intent_id, req_id, global_conversation_id)
    except LockError:
        code = -2
        resp_msg = "获取Task信息失败"
        logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting task lock: {task_lock_key}')
        run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
        ret_info = {'output': resp_msg, "code": code, "info": "refuse message", "status" :0, 'detail_info': run_details}
        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] building task end, code:{ret_info["code"]}, '
                     f'info:{ret_info["info"]}, conversation_id:{chat_conversation_id}, '
                     f'run_details:{run_details}')
        return ret_info

    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action params, '
                 f'new_intent:{new_intent_id}, new_task_msg:{new_task_msg}, task:{curr_task}, app_id:{new_task_appid}')
    if curr_task and curr_task["status"] != "finish":                           # do action, wait & fail can run
        if curr_task["retry"] < settings.fail_retry_cnt:
            # start action task
            ts = time.time()
            if type(new_task_msg) != str:
                new_task_msg = json.dumps(new_task_msg, ensure_ascii=False)

            pieces = chat_conversation_id.split("_")
            conversation_ts = ""
            if len(pieces) > 1: conversation_ts = pieces[1]
            custom_var = {}
            for task_var_key, task_var_val in curr_task["out_vars"].items():
                custom_var[task_var_key] = task_var_val
            new_custom_var = {
                "uid": user_id,
                "coords": coords,
                "intent_id": new_intent_id,
                "req_id": req_id,
                "conversation_id" :chat_conversation_id,
                "input": new_task_msg,
                "hist_chat": intent_hist_chat,
                "app_id": new_task_appid,
                "ts": f'{ts}',
                "scene_id": curr_task["scene_id"],
                "ori_chat_msg": curr_task.get("ori_chat_msg", ""),
                "context": {
                    "req_id": req_id,
                    "uid": user_id,
                    "intent_id": new_intent_id,
                    "conversation_id": chat_conversation_id,
                    "coords" :coords,
                    "app_id": new_task_appid,
                    "conversation_ts": conversation_ts,
                    "token": token,
                    "pro_ver_id": pro_ver_id
                }
            }
            for custom_var_key, custom_var_val in new_custom_var.items():
                custom_var[custom_var_key] = custom_var_val
            combine_msg = json.dumps(custom_var, ensure_ascii=False)
            combine_msg = combine_msg.replace('\\n', '')

            act_lock_key = f"{user_id}_0_do_act_flow"
            # vars_lock = RedisLock(redis_client, vars_lock_key)
            try:
                with redis_cli.lock(act_lock_key, timeout=60, blocking_timeout=3):
                    try:
                        time_action_start = time.time()

                        # fetch vars from the last round
                        intent_vars = info_agent.get_vars(user_id, global_conversation_id, "0")
                        if intent_vars and new_intent_id in intent_vars:
                            custom_var["flow_param"] = intent_vars[new_intent_id].get("flow_param", dict())

                        logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action-flow req start, '
                                     f'new_intent:{new_intent_id}, comb_task_msg:{combine_msg}')
                        code, err_msg, chat_id, conversation_id = action_bots[new_intent_id].create_chat(
                            req_id, user_id, global_conversation_id, combine_msg, "answer", extra_header, pro_ver_id,
                            custom_var, chat_conversation_id
                        )
                        if code != 0:  # create chat err.
                            logger.error(f"[{req_id}][{user_id}][{global_conversation_id}] create chat failed, code: {code}, msg: {err_msg}")

                        logger.info(
                            f'[{req_id}][{user_id}][{global_conversation_id}] action-flow req submit, code:{code}, err_msg:{err_msg}')
                        run_code = "fail"

                        task_agent.set_task_param(user_id, new_intent_id, req_id, "running", global_conversation_id, fg_intent_id, chat_id)
                        flow_tips = role_dict["default"]["script"].get("flow_tips", [])
                        timer_param = {
                            "uid": user_id,
                            "intent_id": new_intent_id,
                            "coords": coords,
                            "token": token,
                            "tip_list": flow_tips,
                            "pro_ver_id": pro_ver_id
                        }
                        # user_timer.set_task(user_id, timer_param, 5)
                        # user_timer.start(user_id)
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] before flow timer, flow_tips:{flow_tips}')

                        code, status, err_msg, run_code, action_ret_msg = action_bots[new_intent_id].wait_chat_finish(
                            req_id, user_id, global_conversation_id, conversation_id, chat_id, "answer", code
                        )

                        # user_timer.stop(user_id)
                        logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] stop flow timer')

                        action_ret_msg["scene_sub_id"] = "-1"
                        action_ret_msg["scene_id"] = custom_var["scene_id"]
                        if isinstance(action_ret_msg.get("params"), dict):
                            if new_intent_id not in intent_vars:
                                intent_vars[new_intent_id] = {}

                            intent_vars[new_intent_id]["scene_id"] = custom_var["scene_id"]
                            if "scene_sub_id" in action_ret_msg["params"]:
                                action_ret_msg["scene_sub_id"] = action_ret_msg["params"]["scene_sub_id"]
                                intent_vars[new_intent_id]["scene_sub_id"] = action_ret_msg["params"]["scene_sub_id"]

                            if "flow_param" in intent_vars[new_intent_id]:
                                for param_key, param_val in action_ret_msg["params"].items():
                                    intent_vars[new_intent_id]["flow_param"][param_key] = param_val
                                # intent_vars[new_intent_id]["flow_param"].update(action_ret_msg["params"])
                            else:
                                intent_vars[new_intent_id]["flow_param"] = action_ret_msg["params"]

                        info_agent.set_vars(user_id, global_conversation_id, "0", intent_vars)
                    except Exception as e:
                        # user_timer.stop(user_id)
                        logger.exception(f'[{req_id}][{user_id}][{global_conversation_id}] error when running workflow')
                        code = -1
                        status = "fail"
                        run_code = "fail"
                        err_msg = f'{e}'
                        action_ret_msg = None
                    # finally:
                    #     user_timer.stop(user_id)
            except LockError:
                logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] error when getting vars lock: {act_lock_key}')

            time_action_end = time.time()
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action-flow req finished, code:{code}, '
                         f'status:{status}, run_code:{run_code}, err_msg:{err_msg}, '
                         f'time_cost:{(time_action_end - time_action_start):.3f}')

            if status == "canceled":                                # 任务取消，异步执行后续任务
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] action canceled')
                task_agent.set_task_param(user_id, new_intent_id, req_id, "cancel", global_conversation_id, fg_intent_id)
                executor.submit(do_act, user_id, coords, new_intent_id, global_conversation_id, fg_intent_id, None,
                                extra_header, pro_ver_id, token, req_id, chat_conversation_id)

                run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
                ret_info = {'output': "", "code": 2, "info": "cancel task", "status" :0, 'detail_info': run_details}
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] do_action end, code:{ret_info["code"]}, '
                             f'info:{ret_info["info"]}, new_intent:{new_intent_id}, '
                             f'conversation_id:{chat_conversation_id}, run_details:{run_details}')
                return ret_info

            logger.debug(f'code:{code}, chat_id:{chat_id}, run_code:{run_code}, action_ret_msg:{action_ret_msg}')
            if code != 0 or chat_id == "-1" or not action_ret_msg or "output" not in action_ret_msg or run_code == "fail":
                if not chat_id: chat_id = ""
                if curr_task["retry"] < settings.fail_retry_cnt - 1:
                    code = -3
                    resp_msg = "我操作手机还不熟练，再试一次。"
                    err_detail = ""
                    if  action_ret_msg and "err_msg" in action_ret_msg:
                        err_detail = action_ret_msg["err_msg"]
                    logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] task failed when doing action, retry, '
                                  f'chatid:{chat_id}, new_intent:{new_intent_id}, code:{code}, '
                                  f'err_msg:{err_msg}, detail_msg:{err_detail}, retry:{curr_task["retry"]}')
                    task_agent.set_task_param(user_id, new_intent_id, req_id, "waitrun", global_conversation_id, fg_intent_id,
                                              chat_id, curr_task["retry"] + 1)
                    push_args = (req_id, coords, user_id, new_intent_id, "-1", "-1", resp_msg, "", -1,
                                 chat_conversation_id, global_conversation_id, "on", "off", "error", "must", -1)
                    err_info = "task fail retry"
                else:
                    code = -4
                    resp_msg = "嗯，这个我暂时还不会呢。"
                    logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] task failed when doing action, '
                                  f'code:{code}, resp_msg:{resp_msg}')
                    task_agent.set_task_param(user_id, new_intent_id, req_id, "fail", global_conversation_id, fg_intent_id, chat_id,
                                              curr_task["retry"] + 1)
                    push_args = (req_id, coords, user_id, new_intent_id, "0", "-1", resp_msg, "", 0,
                                 chat_conversation_id, global_conversation_id, "off", "off", "error", "must", -1)
                    err_info = "task fail final"

                send_args = (req_id, coords, user_id, new_intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
                message_push_and_send(push_args=push_args, send_args=send_args)

                executor.submit(do_act, user_id, coords, new_intent_id, global_conversation_id, fg_intent_id, None, extra_header,
                                pro_ver_id, token, req_id, chat_conversation_id)
                run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
                ret_info = {'output': resp_msg, "code": code, "info": err_info, "status" :0, 'detail_info': run_details}
                logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] do_action end, code:{ret_info["code"]}, '
                             f'info:{ret_info["info"]}, new_intent:{new_intent_id}, '
                             f'conversation_id:{chat_conversation_id}, run_details:{run_details}')
                return ret_info

            # update chat history
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] set task status finish updated: {curr_task}')
            task_agent.set_task_param(user_id, new_intent_id, req_id, "finish", global_conversation_id, fg_intent_id, chat_id,
                                      curr_task["retry"] + 1)

            # update report list
            talk_cmd = "on"
            mic_cmd = "on"
            # 流程结束则关通话，关mic，否则全开
            new_intent_type = intent_info.get(new_intent_id, {}).get("intent_type", 0)
            # if new_intent_id in {"10002", "10005", "10006", "10007"} and action_ret_msg["status"] == 0:
            if new_intent_type == 1 and action_ret_msg["status"] == 0:
                talk_cmd = "off"
                mic_cmd = "off"

            task_list = task_agent.get_task_list(user_id, global_conversation_id)
            valid_task_set = [item["req_id"] for item in task_list if item["status"] in ["finish", "waitrun"]]
            push_args = (req_id, coords, user_id, new_intent_id, action_ret_msg["scene_id"], action_ret_msg["scene_sub_id"],
                         action_ret_msg["output"], action_ret_msg["detail_info"], action_ret_msg["status"], chat_conversation_id,
                         global_conversation_id, valid_task_set, talk_cmd, mic_cmd, "task", "must", -1)
            logger.debug(f'[{req_id}][{user_id}][{global_conversation_id}] sending report to client')
            send_args = (req_id, coords, user_id, new_intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            message_push_with_merge_and_send(push_args=push_args, send_args=send_args)
        else:
            code = -5
            err_msg = "任务执行失败"
            resp_msg = "抱歉，系统出问题了，请提交问题反馈。"
            logger.error(f'[{req_id}][{user_id}][{global_conversation_id}] task failed when doing action, code:{code}, '
                          f'resp_msg:{resp_msg}')

            task_agent.set_task_param(user_id, new_intent_id, req_id, "fail", global_conversation_id, fg_intent_id,
                                      "", curr_task["retry"] + 1)
            push_args = (req_id, coords, user_id, new_intent_id, "0", "-1", resp_msg, "", -1, chat_conversation_id,
                         global_conversation_id, "off", "off", "error", "must", -1)
            send_args = (req_id, coords, user_id, new_intent_id, global_conversation_id, fg_intent_id, extra_header, pro_ver_id)
            message_push_and_send(push_args=push_args, send_args=send_args)

            # 任务失败，异步调起后续任务
            executor.submit(do_act, user_id, coords, global_conversation_id, new_intent_id, fg_task_source, None, extra_header,
                            pro_ver_id, token, req_id, chat_conversation_id)
            run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
            ret_info = {'output': resp_msg, "code": code, "info": "task fail final", "status" :0, 'detail_info': run_details}
            logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] do_action end, code:{ret_info["code"]}, '
                         f'info:{ret_info["info"]}, new_intent:{new_intent_id}, '
                         f'conversation_id:{chat_conversation_id}, run_details:{run_details}')
            return ret_info

    # 任务完成，异步调起后续任务
    task_agent.set_task_param(user_id, new_intent_id, req_id, "finish", global_conversation_id, fg_intent_id)
    executor.submit(do_act, user_id, coords, global_conversation_id, new_intent_id, fg_task_source, None, extra_header, pro_ver_id,
                    token, req_id, chat_conversation_id)
    run_details["time_cost"] = f'{(time.time() - time_start):.3f}'
    ret_info = {'output': "", "code": 0, "info": "sample req", "status" :0, 'detail_info': run_details}
    logger.info(f'[{req_id}][{user_id}][{global_conversation_id}] do_action end, code:{ret_info["code"]}, '
                 f'info:{ret_info["info"]}, new_intent:{new_intent_id}, '
                 f'conversation_id:{chat_conversation_id}, run_details:{run_details}')
    return ret_info
