import re
import json
import uuid
import time
import logging
from typing import Iterator
from datetime import datetime
from zoneinfo import ZoneInfo

import emoji
import requests
from urllib.parse import urljoin

from agent_svr.thread_safe_dict import ThreadSafeDict
from agent_svr.settings import settings

def remove_emoji(text: str) -> str:
    """
    使用 emoji 库移除字符串中的所有 emoji。
    """
    return emoji.replace_emoji(text, replace='')


def remove_content_in_closed_parentheses(text: str) -> str:
    """
    移除字符串中闭合的括号（半角或全角）及其内部的内容。
    该方法会迭代处理，以正确处理嵌套括号。

    例如:
    "你好(这是(一段)测试)世界" -> "你好世界"
    "你好（这是（一段）全角测试）世界" -> "你好世界"
    "你好(混合（测试）结束)世界" -> "你好世界"
    "你好(abc)def(ghi)" -> "你好def"

    Args:
        text: 原始字符串

    Returns:
        处理后的字符串
    """
    # 正则表达式匹配：
    # \(     -> 匹配半角左括号 (
    # [^()（）]* -> 匹配任意不是括号的字符零次或多次 (这是最内层的内容)
    # \)     -> 匹配半角右括号 )
    # |      -> 或者
    # （     -> 匹配全角左括号 （
    # [^()（）]* -> 匹配任意不是括号的字符零次或多次
    # ）     -> 匹配全角右括号 ）
    #
    # 这个模式会优先匹配不包含其他括号的括号对 (即最内层的)
    pattern = r"\([^()（）]*\)|（[^()（）]*）"

    # 持续替换，直到字符串中不再有符合模式的匹配项
    # 这样可以处理嵌套的情况，例如 "a (b (c) d) e"
    # 第一次会移除 "(c)" -> "a (b  d) e"
    # 第二次会移除 "(b  d)" -> "a  e"
    while re.search(pattern, text):
        text = re.sub(pattern, "", text)
        # print(f"中间步骤: {text}") # 如果需要调试，可以取消注释这行

    return text

def merge_pieces(chunks: Iterator[str]) -> Iterator[str]:
    marks = {"。", "？", "！", ",", ".", "?", ":", "，", " "}

    s = ""
    for chunk in chunks:
        chunk = chunk.strip()
        if not chunk:
            continue

        for c in chunk:
            s += c
            if c in marks and len(s) >= 5:
                yield remove_content_in_closed_parentheses(remove_emoji(s))
                s = ""
    if s:
        yield remove_content_in_closed_parentheses(remove_emoji(s))


class RequestSession(requests.Session):
    def __init__(self, base_url=None):
        super().__init__()
        self.base_url = base_url

    def request(self, method, url, *args, **kwargs):
        joined_url = urljoin(self.base_url, url)
        return super().request(method, joined_url, *args, **kwargs)


class DifyAgent:
    def __init__(self, pat_auth, bot_id, bot_name):
        # self.pat_auth = f"Bearer {pat_auth}"
        self.bot_id = bot_id
        self.bot_name = bot_name
        self.wait_time = 1
        self.retry_cnt = 180

        self.session = RequestSession(base_url=settings.dify_addr)

        self.default_headers = {
            'Content-Type': 'application/json',
            'Authorization': bot_id or "Bearer app-7CmMZKFzUF7to6aGY2MmQNJG"
        }

        self._resp_map = ThreadSafeDict()

    def kill_chat(self, req_id, user_id, global_conversation_id, conversation_id, chat_id):
        pass

    def op_new_conversation(self, req_id, user_id, global_conversation_id):
        return 0, "", global_conversation_id

    def op_send_req(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, conversation_id = ""):
        pass

    def create_chat(
        self,
        req_id,
        user_id,
        global_conversation_id,
        chat_msg,
        target,
        extra_header,
        ver_id,
        custom_var=None,
        conversation_id="",
    ):
        if not custom_var:
            logging.error(f"[{req_id}][{user_id}][{global_conversation_id}] no valid custom var, check it!")

        local_now = datetime.now(tz=ZoneInfo("Asia/Shanghai"))
        content = {
            "inputs": {
                "scene_id": custom_var["scene_id"],
                "query_hist": custom_var["hist_chat"],
                "conversation_id": custom_var["conversation_id"],
                "token": custom_var["context"]["token"],
                "current_time": local_now.strftime("%Y%m%d %H:%M:%S"),
                "conversation_hist": json.dumps(custom_var.get("flow_param", dict()))
                    # "has_check_conflict": 1,
                    # "check_start_time": "20250618 21:00:00",
                    # "check_end_time": "20250618 22:00:00",
                    # "conflict_rslt": []
                # }
            },
            "response_mode": "streaming",
            "auto_generate_name": True,
            "query": custom_var.get('ori_chat_msg') or custom_var['input'],
            # "conversation_id": "75b203ff-2c56-4d5e-ba70-34b1c897f7ee",
            "user_id": user_id,
            "user": user_id
        }
        res = self.session.post(f"/v1/chat-messages", headers=self.default_headers, json=content, stream=True)
        if res.status_code != 200:
            logging.error(f"[{req_id}][{user_id}][{global_conversation_id}] create chat failed, code: {res.status_code}, content: {res.text}")
            return "-1", "create_chat_failed", "", ""
        chat_id = str(uuid.uuid4())
        self._resp_map[chat_id] = res
        return "0", "", chat_id, ""

    def op_check_status(self, req_id, user_id, global_conversation_id, chat_id, conversation_id):
        pass

    def op_get_msg(self, req_id, user_id, global_conversation_id, chat_id, conversation_id, target = "answer"):
        pass

    def wait_chat_finish(self, req_id, user_id, global_conversation_id, conversation_id, chat_id, target, create_code):
        resp = self._resp_map[chat_id]

        res = dict()
        for line in resp.iter_lines():
            if not line.strip():
                continue

            try:
                data = json.loads(line[6:])
            except json.JSONDecodeError:
                logging.error(f"[{req_id}][{user_id}] json parse err: {line}")
                continue

            logging.info(data)
            if "answer" in data.keys():
                res = json.loads(data["answer"])

        if not res:
            logging.error(f"[{req_id}][{user_id}][{global_conversation_id}] cannot get answer!")
            return -1, "fail", "get msg error", "fail", dict()

        result = {
            "code": "0",
            "status": res.get("status_code", 1),  # 0 means end it.
            "output": res["reply_content"],
            "detail_info": "",
            "params": res.get("conversation_hist", dict())
        }
        return 0, "success", "", "success", result


    def chat(self, req_id, user_id, global_conversation_id, chat_msg, custom_var = None, need_parse = True, stream = False):
        start = time.time()
        code = 0
        err_msg = ""
        answer = ""
        model_name = ""
        prompt_token = 0
        output_token = 0

        content = {
            "user_id": user_id,
            "user": user_id,
            "response_mode": "streaming" if stream else "blocking",
            "query": chat_msg,
            "req_id": req_id,
            "inputs": {
                "input": chat_msg,
                "user_id": user_id,
                "history_str": "",
                "conversation_id": global_conversation_id,
            }
        }
        if custom_var and len(custom_var) > 0:
            # content["custom_variables"] = custom_var
            if "hist_info" in custom_var.keys():
                format_msg = lambda x: f"{x['role'] if x['role'] != 'bot' else 'assistant'}:{x['msg']}"
                content["inputs"]["history_str"] = "\n".join([format_msg(c) for c in json.loads(custom_var["hist_info"])])
                content["inputs"]["history"] = custom_var["hist_info"]
        logging.debug(f'[{req_id}][{user_id}][{global_conversation_id}] new dify req: {content}')

        res = self.session.post(f"/v1/chat-messages", headers=self.default_headers, json=content, stream=stream)
        if not res.ok:
            code = -1
            err_msg = f"code: {res.status_code}, content: {res.text}"
        elif not stream:
            data = res.json()
            logging.debug(f'[{req_id}][{user_id}][{global_conversation_id}] send req, name:{self.bot_name}, resp:{data}, type:{type(data)}')
            answer = remove_content_in_closed_parentheses(remove_emoji(data["answer"]))
        else:
            # stream mode.
            def resp_iter(stream_resp):
                for line in stream_resp.iter_lines(decode_unicode=True):
                    if line.startswith("data: "):
                        try:
                            data_str = line[len("data: "):]
                            data_json = json.loads(data_str)
                            if "answer" in data_json:
                                logging.debug(f'debug answer data :{data_json["answer"]}')
                                yield data_json["answer"]
                        except json.JSONDecodeError:
                            logging.error(f"[{req_id}][{user_id}][{global_conversation_id}] parse err: {line}")
            answer = merge_pieces(resp_iter(stream_resp=res))
        return code, err_msg, answer, model_name, prompt_token, output_token, time.time() - start


if __name__ == '__main__':
    from vui_svr.settings import global_setting_dict

    bot = DifyAgent(pat_auth="", bot_id="", bot_name="", global_setting_dict=global_setting_dict)
    now = time.time()
    r = bot.chat(
        req_id="111",
        user_id="222",
        global_conversation_id="",
        chat_msg="给我讲讲今天的新闻",
        stream=True
    )
    for line in r[2]:
        print(time.time() - now)
        print(line)

