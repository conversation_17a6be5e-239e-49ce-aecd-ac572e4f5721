from typing import Union

from fastapi import FastAPI
from pydantic import BaseModel

from agent_svr.logging_config import logger
from agent_svr.build_task import (
    build_task,
    do_act,
)
from agent_svr.views.internal_test import internal_test_router

app = FastAPI()

# views
app.include_router(internal_test_router, prefix="/internal_test")


@app.get("/")
def read_root():
    return {"Hello": "World"}


@app.get("/items/{item_id}")
def read_item(item_id: int, q: Union[str, None] = None):
    return {"item_id": item_id, "q": q}


@app.get("/depth")
async def async_depth(d: int):
    import httpx

    print(f"current depth: {d}")

    async with httpx.AsyncClient() as client:
        r = await client.get("http://127.0.0.1:8000/depth" + f"?d={int(d) + 1}")
    return {"depth": d}


func_map = {
    "build_task": build_task,
    "do_act": do_act,
}


class AgentFuncParam(BaseModel):
    func: str
    args: list
    kwargs: dict


@app.post("/agent_task/exec")
def execute_agent_func(req: AgentFuncParam):
    if req.func not in func_map:
        return {"error": f"Function {req.func} not found"}, 400

    result = func_map[req.func](*req.args, **req.kwargs)
    logger.info(f"func {req.func} agent task exec result: {result}")
    return {"result": result}
