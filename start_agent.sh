#!/usr/bin/env bash

# --- 环境变量 ---
ENV_VARS=(
  "GLOBAL_SETTING_FILE=./agent_svr/global_settings.json"
  "GUNICORN_CMD_ARGS=--bind=0.0.0.0:5001 --worker-class=uvicorn.workers.UvicornWorker --access-logfile=-"
)

# --- 配置 ---
TARGET_SCRIPT="gunicorn agent_svr.main:app"
LOG_FILE="logs_agent.std"

# 确保脚本名不为空
if [ -z "$TARGET_SCRIPT" ]; then
  echo "错误：TARGET_SCRIPT 未配置。"
  exit 1
fi

# 如果 SCRIPT_PATH 未设置，则假定脚本在当前目录或 SCRIPT_NAME 包含路径
# 如果 SCRIPT_PATH 设置了，就用它，否则用 SCRIPT_NAME
# TARGET_SCRIPT=${SCRIPT_PATH:-$SCRIPT_NAME}

# 构造用于 pgrep/pkill 的模式
# 注意：这里我们匹配包含 Python 解释器和脚本名的命令行
# 例如 "python3 your_python_script.py"
# 如果你的脚本启动方式特殊（例如通过特定的wrapper），可能需要调整此模式
# 为了更精确，可以考虑匹配脚本的绝对路径（如果已知）
# PROCESS_PATTERN="${PYTHON_EXECUTABLE} ${TARGET_SCRIPT}"
PROCESS_PATTERN=$TARGET_SCRIPT
# 如果担心 PROCESS_PATTERN 可能匹配到 pgrep/pkill 自身或其他不相关进程，
# 可以使用更精确的 pgrep 技巧，例如：
# pgrep -f "[p]ython3 ${TARGET_SCRIPT}"
# 但对于大多数情况，上述模式已足够。

echo "正在检查进程: ${PROCESS_PATTERN}"

# 使用 pgrep 检查进程是否存在
# -f 选项表示匹配完整命令行参数
# 将输出重定向到 /dev/null，我们只关心退出状态
if pgrep -f "${PROCESS_PATTERN}" > /dev/null; then
  echo "检测到程序 (${TARGET_SCRIPT}) 正在运行。正在尝试终止..."
  # 使用 pkill 终止进程
  # -f 选项表示匹配完整命令行参数
  # SIGTERM (默认) 是一个礼貌的终止信号
  pkill -f "${PROCESS_PATTERN}"

  # 等待一段时间确保进程被终止
  for i in $(seq 0 1 5); do
    printf "\r\033[K等待: %s 秒" "$i" # \r 回到行首, \033[K 清除到行尾, 然后打印新内容
    sleep 1
  done

  echo # 换行

  # 再次检查，如果仍在运行，可能需要更强的信号
  if pgrep -f "${PROCESS_PATTERN}" > /dev/null; then
    echo "程序未能通过 SIGTERM 终止。尝试使用 SIGKILL (强制终止)..."
    pkill -9 -f "${PROCESS_PATTERN}"
    sleep 1
    if pgrep -f "${PROCESS_PATTERN}" > /dev/null; then
        echo "错误：终止失败。可能需要手动干预。"
    else
        echo "程序已通过 SIGKILL 成功终止。"
    fi
  else
    echo "程序已成功终止。"
  fi
else
  echo "程序 (${TARGET_SCRIPT}) 未在运行。"
fi

# 检查第一个参数是否为 "k" 或者 "kill"
if [ "$1" = "k" ] || [ "$1" = "kill" ]; then
  echo "当前为kill, 直接退出"
  exit 0
fi

echo "----------------------------------------"
echo "正在后台启动程序: ${TARGET_SCRIPT}"
# 使用 nohup 在后台启动 Python 程序
# > "${LOG_FILE}" 2>&1 表示将标准输出和标准错误都重定向到日志文件
nohup env "${ENV_VARS[@]}" ${TARGET_SCRIPT} > "${LOG_FILE}" 2>&1 &

# 检查启动后的状态 (可选但推荐)
# &! 获取最后一个后台作业的PID
# START_PID=$!
sleep 1 # 给程序一点启动时间

# shellcheck disable=SC2005

# 再次使用pgrep确认，因为 $! 只表示nohup命令的PID，而不是Python进程的PID（尽管通常它们是一致的）
if pgrep -f "${PROCESS_PATTERN}" > /dev/null; then
  echo "程序 (${TARGET_SCRIPT}) 已成功启动。"
  echo "PID(s): $(pgrep -f "${PROCESS_PATTERN}" | tr '\n' ' ')" # 显示所有匹配的PID
  echo "日志文件: ${LOG_FILE}"
else
  echo "错误：程序 (${TARGET_SCRIPT}) 启动失败。请检查 ${LOG_FILE} 获取详细信息。"
fi

exit 0
