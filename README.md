# ai_svr

本文档旨在帮助您快速设置和运行 `ai_svr` 项目。

## 依赖管理：UV

本项目采用 [UV](https://github.com/astral-sh/uv) 作为 Python 包管理器和虚拟环境管理器。

### 常用命令

*   **添加依赖项：**
    ```bash
    uv add <package-name>
    ```
    例如，添加 `requests` 包：`uv add requests`

*   **同步依赖项：**
    根据 `pyproject.toml` 或 `requirements.txt` 文件同步当前环境中的依赖包。
    ```bash
    uv sync
    ```

## 激活虚拟环境

在项目根目录下，执行以下命令以激活由 UV (或 virtualenv/venv) 创建的虚拟环境：
```bash
source .venv/bin/activate
```
*提示：如果您尚未创建虚拟环境，UV 通常会在首次执行 `uv pip install` 或 `uv sync` 时自动创建。您也可以使用 `uv venv` 手动创建。*

## 应用启动方式

您可以根据需求选择以下任一方式启动应用：

1.  **Gunicorn (推荐用于生产环境):**
    使用 Gunicorn WSGI 服务器启动应用，具有较好的并发处理能力。
    ```bash
    gunicorn vui_svr.main:app --workers 4 --threads 2 --bind 0.0.0.0:8000
    ```
    *   `vui_svr.main:app` 指向 `vui_svr/main.py` 文件中的 `app` 应用实例。
    *   `--workers 4` 指定了 4 个工作进程。
    *   `--threads 2` 为每个工作进程指定了 2 个线程 (总并发能力 = workers * threads)。
    *   `--bind 0.0.0.0:8000` 使服务监听在所有网络接口的 8000 端口。
    *   更多配置选项和详细信息，请参考项目中的 `start.sh` 脚本或 [Gunicorn](https://docs.gunicorn.org/en/21.2.0/settings.html) 官方文档。

2.  **Flask CLI (推荐用于开发环境):**
    使用 Flask 内置的开发服务器启动应用，方便调试。
    ```bash
    flask -A vui_svr.main:app run --host=0.0.0.0 --port=5000
    ```
    *   `-A vui_svr.main:app` 指定应用实例。
    *   `run` 启动开发服务器。
    *   `--host=0.0.0.0` 使服务可从外部访问。
    *   `--port=5000` 指定监听端口。

3.  **Python 直接运行 (推荐用于开发环境):**
    如果 `vui_svr/main.py` 文件中包含 `if __name__ == "__main__":` 启动逻辑，可以直接运行该模块。
    ```bash
    python -m vui_svr.main
    ```
    *   这通常会启动 Flask 内置的开发服务器，具体行为取决于 `main.py` 中的实现。
