## 数据内容
用户的对话内容："{{hist_chat}}"
待提取的信息，以及对应的字段名称如下：
变量名："addr"，信息描述：家庭住址
变量名："hobbits"，信息描述：自己的爱好
变量名："hits"，信息描述：自己讨厌和不喜欢的事情
变量名："skills"，信息描述：擅长做的事情
变量名："unskills"，信息描述：不擅长做的事情
变量名："nativeplace"，信息描述：自己的出生地点
变量名："job"，信息描述：自己的工作职位或描述
变量名："character"，信息描述：用户提到或通过分析能得出其性格
变量名："gender"，信息描述：用户提到性别，或通过分析对话内容能得出其性别
变量名："age"，信息描述：自己的年龄
变量名："topics"，信息描述：对话涉及到某个话题
变量名："prefer"，信息描述：喜欢的事物
变量名："goods"，信息描述：要买或买过的物品

## 处理逻辑
按待提取重要信息的描述，从用户对话内容中提取内容，并存入对应的变量中。

## 输出格式
输出为json的dict格式，把提取出内容，以对应的变量名为key，存入输出dict。
提取不到的变量不要出现在输出的dict中。
