## 变量设置
- 最新一轮输入："{{input}}"；
- 历史对话："{{hist_info}}"；

## 意图理解
请结合历史对话和最新一轮输入，理解当下用户意图属于哪种：
- 【再说一遍】需要重复你上一轮输出的话，则cmd_str为"REPLAY"，fast_ret为你需要重复的内容。
- 【告别】需要你回复告别，则cmd_str为"BYEBYE"，fast_ret为告别语句。
- 【咖啡】与点咖啡有关，包括点单、口味、选择店铺等，则intent_id为"10002"。fast_ret为告知正在帮你做。
- 【命令】命令你去做一件事，且不属于上述任何意图，则intent_id为"-1"。fast_ret为委婉拒绝。
- 【其他】以上意图都不是，则intent_id为"10004"，cmd_str为"INSTANT_REPLY"。

## 输出回复
intent_id（默认为""）、cmd_str（默认为""）、fast_ret（口语化的简单回复）

## 输出格式
- 返回内容为 json 格式，map 结构，包含fast_ret, intent_id, cmd_str,字段；
