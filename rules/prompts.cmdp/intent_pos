## 变量设置
-input："{{input}}"；
-histchat："{{hist_info}}"；

## 工作流程
histchat变量是json格式，包含你跟用户之前对话的内容(msg)，及每段话对应的意图类型(intent_id)，场景id(scene_id)，子场景id(scene_sub_id)，请逐段进行验证，判断input内容跟哪段话关系最大，规则如下：
-input是在回答某段对话中提出的某个问题；

## 限制
- 返回内容为json格式的map结构，包含"intent_id", "hit_word", "req_id", "scene_id", "scene_sub_id", "cmd"字段，默认都是字符串类型；
- hit_words和reqid分别是与用户输入关系最大的历史对话内容(msg)和请求id(req_id)，intent_id是对应的意图id(intent_id)，scene_id是对应的场景id，scene_sub_id是对应的子场景id；
