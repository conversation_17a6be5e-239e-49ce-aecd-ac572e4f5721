{"branches": [{"condition": {"and": [{"eq": [{"var": "order"}, 1]}]}, "output": 1}, {"condition": {"and": [{"eq": [{"var": "order"}, 2]}]}, "output": 2}, {"condition": {"and": [{"eq": [{"var": "order"}, 3]}]}, "output": 3}, {"condition": {"and": [{"eq": [{"var": "order"}, 4]}]}, "output": 4}, {"condition": {"and": [{"eq": [{"var": "order"}, 5]}]}, "output": 5}, {"condition": {"and": [{"eq": [{"var": "order"}, 6]}]}, "output": 6}, {"condition": {"and": [{"eq": [{"var": "order"}, 7]}]}, "output": 7}, {"condition": {"and": [{"eq": [{"var": "order"}, 8]}]}, "output": 8}, {"condition": {"and": [{"eq": [{"var": "order"}, 9]}]}, "output": 9}, {"condition": {"and": [{"eq": [{"var": "order"}, 10]}]}, "output": 10}]}