{"10001": {"name": "查新闻", "desc": "为用户查询最近发生的新闻资讯", "intent_type": 0, "intent_priority": 2, "task_bot": {"type": "gpt", "id": "task_10001", "name": "task_take_10001", "model": "deepseek-vc"}, "action_bot": {"type": "coze", "id": "7442620114442600483", "name": "10001_act_take"}, "app_list": []}, "10002": {"name": "买咖啡", "desc": "帮用户点咖啡", "prolog": "好，稍等", "intent_type": 1, "intent_priority": 3, "task_bot": {"type": "gpt", "id": "task_10002", "name": "task_take_10002", "model": "deepseek-vc", "rule": "taskrule_10002"}, "action_bot": {"type": "langflow", "id": "6f13f735-a523-4db5-aba4-75635062e2d5", "name": "10002_act_take"}, "app_list": [{"app_name": "瑞幸", "app_id": "com.lucky.luckyclient", "channel": "luckyin", "desc": "提供咖啡外卖，咖啡在线点单到店取服务", "rule_list": [{"scene_id": "ALL"}]}]}, "10004": {"name": "聊天", "desc": "跟用户闲聊，回答日常问题", "intent_type": 0, "intent_priority": 1, "task_bot": {"type": "direct", "id": "task_10004", "name": "task_take_10004"}, "action_bot": {"type": "coze", "id": "7447798606897201187", "name": "10004_act_take"}, "app_list": []}, "10006": {"name": "处理下", "desc": "一句话命令", "intent_type": 1, "intent_priority": 3, "task_bot": {"type": "direct", "id": "task_10006", "name": "task_take_10006"}, "action_bot": {"type": "langflow", "id": "b78829b9-74b0-4599-b77c-fdfeda19615a", "name": "10006_act_take"}, "app_list": []}, "10007": {"name": "打车", "desc": "帮用户打车", "prolog": "行，稍等", "intent_type": 1, "intent_priority": 3, "task_bot": {"type": "gpt", "id": "task_10007", "name": "task_take_10007", "model": "deepseek-vc", "rule": "taskrule_10007"}, "action_bot": {"type": "langflow", "id": "bee2b2d0-aa08-4795-826b-933ab2a5a3f8", "name": "10007_act_take"}, "app_list": [{"app_name": "曹操打车", "app_id": "cn.caocaokeji.user", "channel": "caocao", "desc": "提供打车服务", "rule_list": [{"scene_id": "ALL"}]}]}, "10008": {"name": "主动记忆", "desc": "记忆", "intent_type": 1, "intent_priority": 3, "task_bot": {"type": "gpt", "id": "task_10008", "name": "task_take_10008", "model": "deepseek-vc", "rule": "taskrule_10008"}, "action_bot": {"type": "dify", "id": "Bearer app-xtllLaH5EvwGfS4zrJaVsViY", "name": "10008_act_take"}, "app_list": []}, "10009": {"name": "IM助手", "desc": "IM助手", "intent_type": 1, "intent_priority": 3, "task_bot": {"type": "direct", "id": "task_10009", "name": "task_take_10009", "model": "deepseek-vc", "rule": "taskrule_10009"}, "action_bot": {"type": "langflow", "id": "88c9c7f9-18e8-40df-9ffc-d71309ef5400", "name": "10009_act_take"}, "app_list": [{"app_name": "微信", "app_id": "com.tencent.mm", "channel": "wechat", "desc": "微信聊天", "rule_list": [{"scene_id": "ALL"}]}]}}